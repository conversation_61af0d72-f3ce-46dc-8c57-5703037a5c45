#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from django.core.cache import cache
from django.db import transaction

from Base.utils.const import ConstDeclareType
from ..logger import logger
from .base import BaseCleanCodeGenerator

from CollectManageApp.models_base import Organization, CityRegion, OrgType, OrganizationOther
from CollectManageApp.models import OrgNonresidentDeclare, OrgNonresidentIssued, OtherOrgNonresidentDeclare


class OrgNonresidentCleanCodeGenerator(BaseCleanCodeGenerator):
    """
    排放登记编码生成器: 主体-非居民
    """
    CLEAN_TYPE = 'ORG'
    CLEAN_SUBTYPE = 'NONRESIDENT'

    def generate(self, clean_id):
        # 1. 获取编码前缀
        logger.info('非居民责任主体排放登记编码发放开始生成:%s' % clean_id)
        declare = OrgNonresidentDeclare.objects.filter(org_id=clean_id).first()
        db = "ljfl_db"
        # if declare.area_coding == "110112000000" and "RESTAURANTS" not in declare.rubbishes:
        #     db = "ljfl_declare_db"
        org = Organization.objects.using(db).filter(org_id=clean_id, is_declare=1, is_deleted=0).first()
        if not org:
            logger.error('主体不存在:%s' % clean_id)
            return dict(code=400, msg='主体不存在.')
        if org.clean_code:
            logger.error('排放登记编码已生成:%s' % clean_id)
            return dict(code=200, msg='排放登记编码已生成.', data=dict(
                clean_code=org.clean_code,
                clean_no=org.clean_no
            ))

        # 1.1 获取区位编码
        org_region = CityRegion.objects.filter(coding=org.comm_coding, grade=4, is_deleted=0).first()
        if not org_region or not org_region.clean_code:
            logger.error('获取区位编码失败:%s' % clean_id)
            return dict(code=400, msg='获取区位编码失败.')
        # 1.2 获取主体类型编码
        org_type = OrgType.objects.filter(org_type_id=org.org_sub_type_id,
                                          parent_id='60b5ef4bef5311ebbe73fa163e3babe8').first()
        if not org_type or not org_type.clean_code:
            logger.error('获取主体类型编码失败:%s' % clean_id)
            return dict(code=400, msg='获取主体类型编码失败.')

        clean_code_prefix = f'{org_region.clean_code}{org_type.clean_code}'
        logger.info('clean_code_prefix: %s' % clean_code_prefix)
        # 2. 锁定编码发号器
        # # 2.1 加锁
        # is_lock = self._lock()
        # if not is_lock:
        #     return dict(code=400, msg='服务器忙请稍后再试.')
        with cache.lock(self.CLEAN_CODE_LOCK_KEY):
            logger.info('cache.lock')
            # 2.2 发号逻辑
            try:
                with transaction.atomic(using='ljfl_db'):
                    with transaction.atomic(using='ljfl_declare_db'):
                        clean_code, clean_no = self._get_clean_code(clean_code_prefix, clean_id)
                        logger.info('clean_code, clean_no: %s' % clean_no)
                        # 修改正式库
                        Organization.objects.using(db).filter(org_id=clean_id, is_declare=1, is_deleted=0) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))

                        # 修改申报库
                        OrgNonresidentDeclare.objects.filter(org_id=clean_id) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))

                        # 如果是接口主体，增加接口推送记录
                        declare = OrgNonresidentDeclare.objects.filter(org_id=clean_id).first()
                        if declare:
                            creator = declare.creator
                            OrgNonresidentIssued.objects.create(**dict(
                                org_id=clean_id,
                                clean_code=clean_code,
                                clean_no=clean_no,
                                transport_company_id=declare.transport_company_id,
                                status=0,
                                creator=creator
                            ))

            except Exception as e:
                logger.exception(e)
                return dict(code=500, msg='排放登记编码发放失败.')

        # # 2. 释放锁
        # self._lock_release()
        logger.info('排放登记编码发放成功')
        return dict(code=200, msg='排放登记编码发放成功.', data=dict(
            clean_code=clean_code,
            clean_no=clean_no
        ))

    def other_generate(self, clean_id):
        # 开发区使用
        # 1. 获取编码前缀
        logger.info('other_generate')
        org = OrganizationOther.objects.filter(org_id=clean_id, is_declare__in=[1, 2], is_deleted=0).first()
        if not org:
            return dict(code=400, msg='主体不存在.')
        if org.clean_code:
            return dict(code=200, msg='排放登记编码已生成.', data=dict(
                clean_code=org.clean_code,
                clean_no=org.clean_no
            ))

        # 1.1 获取区位编码
        org_region = CityRegion.objects.filter(coding=org.comm_coding, grade=4, is_deleted=0).first()
        if not org_region or not org_region.clean_code:
            return dict(code=400, msg='获取区位编码失败.')
        # 1.2 获取主体类型编码
        org_type = OrgType.objects.filter(org_type_id=org.org_sub_type_id,
                                          parent_id='60b5ef4bef5311ebbe73fa163e3babe8').first()
        if not org_type or not org_type.clean_code:
            return dict(code=400, msg='获取主体类型编码失败.')

        clean_code_prefix = f'{org_region.clean_code}{org_type.clean_code}'
        logger.info('other_generate--clean_code_prefix')
        # 2. 锁定编码发号器
        # # 2.1 加锁
        # is_lock = self._lock()
        # if not is_lock:
        #     return dict(code=400, msg='服务器忙请稍后再试.')
        with cache.lock(self.CLEAN_CODE_LOCK_KEY):
            logger.info('cache.lock-OTHER_CLEAN_CODE_LOCK_KEY')
            # 2.2 发号逻辑
            try:
                with transaction.atomic(using='ljfl_db'):
                    with transaction.atomic(using='ljfl_declare_db'):
                        clean_code, clean_no = self._get_clean_code(clean_code_prefix, clean_id)

                        # 修改正式库
                        OrganizationOther.objects.filter(org_id=clean_id, is_declare=2, is_deleted=0) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))

                        # 修改申报库
                        OtherOrgNonresidentDeclare.objects.filter(org_id=clean_id) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))

                        # 如果是接口主体，增加接口推送记录
                        # declare = OtherOrgNonresidentDeclare.objects.filter(org_id=clean_id).first()
                        # if declare:
                        #     creator = declare.creator
                        #     OrgNonresidentIssued.objects.create(**dict(
                        #         org_id=clean_id,
                        #         clean_code=clean_code,
                        #         clean_no=clean_no,
                        #         transport_company_id=declare.transport_company_id,
                        #         status=0,
                        #         creator=creator
                        #     ))

            except Exception as e:
                logger.exception(e)
                return dict(code=500, msg='其他垃圾排放登记编码发放失败.')

            # # 2. 释放锁
            # self._lock_release()
            return dict(code=200, msg='其他垃圾排放登记编码发放成功.', data=dict(
                clean_code=clean_code,
                clean_no=clean_no
            ))
