# 字符串操作符对应sql操作符
# params字符串和操作符需要用__分隔
op_map = {
    "gte": ">=",
    "gt": ">",
    "lte": "<=",
    "lt": "<",
    "icontains": "LIKE",
    "contains": "LIKE",
    "=": "=",
    "ne": "<>",
    "in": "IN",
}


def sql_where_fields(k, params):
    """获取sql where判断的字段名和运算符号
    :param k: 字段名 字段名和操作符之间需要用__分隔
    :param params: 搜索条件
    :return: (数据库字段名, 数据库操作符)
    """
    k_symbol = k.split("__", 1)
    if len(k_symbol) == 1:
        field = k_symbol[0]
        symbol = "="
    else:
        field, symbol = k_symbol
    op = op_map[symbol]
    value = params[k]
    if value is None:
        op = "IS"
    if op == "LIKE":
        params[k] = f"%{value}%"
    if op == "IN":
        params[k] = tuple(value)
    return field, op


def sql_params_format(params, namespace="", params_key_list=None):
    """
    sql和搜索字典转化成可以供execute使用的字符串
    execute使用时需要传入params
    {'reward_coin__gte': 30} --> `reward_coin` >= %(reward_coin__gte)s
    :param params: 搜索条件，字典格式 仿照django filter
    :param namespace: 表名 表名.字段名的时候使用
    :param params_key_list: 搜索参数列表，一般是params.keys()
    :return:
    """
    if params_key_list is None:
        params_key_list = params.keys()
    # `数据库字段名` 操作符 %(搜索条件字段名)s
    _format_sql = "`{}` {} %({})s"
    if namespace:
        _format_sql = f"`{namespace}`.{_format_sql}"
    where_sql_list = [_format_sql.format(*sql_where_fields(k, params), k) for k in params_key_list]
    return " AND ".join(where_sql_list)


def sql_where_format_params(sql, params, where_sql_key="where_sql", namespace=""):
    """sql和搜索字典转化成可以供execute使用的字符串
    {'reward_coin__gte': 30} --> `reward_coin` >= %(reward_coin__gte)s
    """
    params_format = sql_params_format(params, namespace)
    return sql.format(**{where_sql_key: params_format})
