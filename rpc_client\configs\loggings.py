#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import
import os
import sys
import logging  # 引入logging模块
import os.path
import time

logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s')

logger = logging.getLogger()
logger.setLevel(logging.DEBUG)  # Log等级总开关
# 第二步，创建一个handler，用于写入日志文件
rq = time.strftime('%Y%m', time.localtime(time.time()))
log_path = os.path.dirname(os.getcwd()) + '/log/'
log_name = log_path + f'rpc{rq}.log'
# logging.basicConfig函数对日志的输出格式及方式做相关配置
def get_logger():
    # 第一步，创建一个logger
    logfile = log_name
    fh = logging.FileHandler(logfile)
    fh.setLevel(logging.DEBUG)  # 输出到file的log等级的开关
    # 第三步，定义handler的输出格式
    formatter = logging.Formatter("%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s")
    fh.setFormatter(formatter)
    # 第四步，将logger添加到handler里面
    logger.addHandler(fh)
    # 日志
    return logger


if __name__ == '__main__':
    logger = get_logger()
    logger.debug('this is a logger debug message')
