syntax = "proto3";
//import "user1.proto";
//import public "meeting2.proto";  引用其他包

package MiniApp; //包ming


// 定义服务接
service XiaoChengXuService {
    /*
        定义rpc方法
    */
    rpc login (LoginRequest) returns (LoginResponse) {}
    rpc get_user_info (GetUserInfoRequest) returns (GetUserInfoResponse) {}
    //rpc send_msg(SendMsgRequest) returns (SendMsgResponse) {}
    rpc save_formid (SaveFormidRequest) returns (SaveFormidResponse) {}
    rpc recv_wx_event (RecvWxEventRequest) returns (RecvWxEventResponse) {}
    rpc push_wx_msg (PushWxMsgRequest) returns (PushWxMsgResponse) {}
    rpc get_template_id (GetTemplateIdRequest) returns (GetTemplateIdResponse) {}
    rpc send_template_notice (SendMsgRequest) returns (SendMsgResponse) {}
}


//小的程序登录协议
message LoginRequest{
    string client_id = 1;
    string code = 2;
}
//小的程序登录响应协议
message LoginResponse{
    uint32 errcode = 1;
    string errmsg = 2;
    string client_id = 3;
    string openid = 4;
    string session_key =5;
    string unionid = 6;
    Jscode2SessionInfo data = 7;
}
message Jscode2SessionInfo{
    string client_id = 1;
    string openid = 2;
    string session_key =3;
    string unionid = 4;
}
//获取access_token
message AccessTokenRequest{
    string client_id = 1;
}
//获取access_token响应协议
message AccessTokenRespons{
    uint32 errcode = 1;
    string errmsg = 2;
    string client_id = 3;
    string access_token = 4;
    int32 expires_time = 5;

}

// 获取用户信息请求协议
message GetUserInfoRequest {
     string username = 1;
}
// 获取用户信息响应协议
message GetUserInfoResponse {
    uint32 errcode = 1;
    string errmsg = 2;
    string username = 3;
    int32 sex = 4;
}

//存储小程序用户的formid协议
message SaveFormidRequest{
    string client_id = 1;
    string username = 2;
    string formid = 3 ;
}
message SaveFormidResponse{
    uint32 errcode = 1;
    string errmsg = 2;
}
//发送订阅消息请求协议
message SendMsgRequest{
    string client_id = 1;//app来源
    string template_id = 2;//模板id
    string to_user = 3 ; //用户openid
    string type_id =4;   //消息类型
    int32 submit_time = 5;//提交时间
    string marker = 6;
    string reason = 7;
    message DataList{
        string data = 1;
    }
    repeated DataList data_list =8;
}
//发送订阅消息响应协议
message SendMsgResponse{
    uint32 errcode = 1;
    string errmsg = 2;
}
//接收微信事件推送
message RecvWxEventRequest{
    string client_id = 1;
    string content = 2;//事件内容
}
//接收微信事件反馈
message RecvWxEventResponse{
    uint32 errcode = 1;
    string errmsg = 2;
}

//推送微信消息
message PushWxMsgRequest{
    string client_id = 1;
    string msg_id = 2;//消息id
    string template_id =3;
}
//推送微信消息反馈反馈
message PushWxMsgResponse{
    uint32 errcode = 1;
    string errmsg = 2;
}

//获取模板id请求
message GetTemplateIdRequest{
    string client_id =1;
    string type_id =2;

}
//获取模板id响应
message GetTemplateIdResponse{
    uint32 errcode = 1;
    string errmsg = 2;
    message TemplateIdInfo{
        string client_id =1;
        string type_id =2;
        string template_id = 3;
        string name = 4;
        string marker = 5;
    }
    repeated TemplateIdInfo data =3;
}