#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from functools import wraps

from django.core.cache import cache
from rest_framework.response import Response


def _get_page_cache_key(request):
    url = request.path
    params = request.query_params.dict()
    params.pop('_refresh', None)
    for i in sorted(params):
        val = params[i]
        if val:
            url += '_{}_{}'.format(i, params[i])
    return url


def page_cache_some_times(cache_seconds=15 * 60):
    """
    页面缓存
    :param cache_seconds:
    :return:
    """

    def _wrapper(func):
        @wraps(func)
        def __wrapper(request):
            if request.method == 'GET':
                cache_key = _get_page_cache_key(request)
                clear = request.GET.get('_refresh')
                result = cache.get(cache_key) if not clear else None
                if result:
                    return Response(data=result)
                response = func(request)
                cache.set(cache_key, response.data, cache_seconds)
                return Response(data=response.data)
            return func(request)

        return __wrapper

    return _wrapper


def page_cache(duration=10 * 60):
    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            if request.method == 'GET':
                cache_key = _get_page_cache_key(request)
                clear = request.GET.get('_refresh')
                result = cache.get(cache_key) if not clear else None
                if result:
                    return Response(data=result)
                response = func(self, request, *args, **kwargs)
                cache.set(cache_key, response.data, duration)
                return Response(data=response.data)
            return func(self, request, *args, **kwargs)

        return wrapper

    return decorator
