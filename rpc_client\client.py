import json

import grpc
import sys
import os
import uuid
import time
from rpc_client import protoc_pb2
from rpc_client import protoc_pb2_grpc
from rpc_client.configs.config import configs


class wx_client(object):
    def __init__(self):
        host = configs.host
        port = configs.port
        print('init:', host, port)
        self.channel = grpc.insecure_channel(f'{host}:{port}')
        # 调用 rpc 服务
        self.stub = protoc_pb2_grpc.XiaoChengXuServiceStub(self.channel)

    def wixin_login_req(self, data):
        # 连接 rpc 服务器
        result = self.stub.login(protoc_pb2.LoginRequest(
            client_id=data.get('client_id', ''),
            code=data.get('code', '')
        ))
        print(result)
        return result

    def save_formid_req(self, data):
        result = self.stub.save_formid(protoc_pb2.SaveFormidRequest(
            client_id=data.get('client_id', ''),
            formid=data.get('formid', ''),
            username=data.get('username', '')
        ))
        print(result)
        return result

    def get_template_id_req(self, data):
        client_id = data.get('client_id')
        type_id = data.get('type_id')

        result = self.stub.get_template_id(protoc_pb2.GetTemplateIdRequest(
            client_id=client_id,
            type_id=type_id
        ))
        print(result.data)
        return result

    def send_template_notice_req(self, data):
        if not data.get('submit_time'):
            data['submit_time'] = int(time.time())
        result = self.stub.send_template_notice(protoc_pb2.SendMsgRequest(**data))
        return result

    def recv_wx_event(self, data):
        content = data.get('content', '')

        result = self.stub.recv_wx_event(protoc_pb2.RecvWxEventRequest(
            content=content
        ))
        print(result.data)
        return result

