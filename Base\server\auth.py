#!/home/<USER>/venv/bin/python3
# coding: utf-8
from __future__ import absolute_import

import json
import requests
from django.conf import settings


class AuthServerError(Exception):

    def __init__(self, response=None, code=500, message=''):
        super(AuthServerError, self).__init__()
        self.response = response
        self.code = code
        if message:
            message = '授权异常[{}]!'.format(message)
        else:
            message = '授权异常!'
        self.message = message


class AuthServer:
    def __init__(self):
        self.appId = settings.AUTH_APPID
        self.appSecret = settings.AUTH_APPSECRET
        self.host = settings.AUTH_HOST

    @staticmethod
    def _get_response(url, raise_exception=True):
        try:
            response = requests.get(url)
        except Exception as e:
            if raise_exception:
                raise AuthServerError(message='AuthServerError: {}'.format(str(e)))
            else:
                return None

        if response.status_code != 200 and not response.content:
            if raise_exception:
                raise AuthServerError(message='http status:{}'.format(response.status_code))
            else:
                return None

        content = response.content.decode('utf-8')
        result = json.loads(content)

        if result.get('code') != 200:
            if raise_exception:
                raise AuthServerError(response=content, message=result.get('msg'), code=result.get('code'))
            else:
                return None

        return result

    @staticmethod
    def _post_response(url, data=None, **kwargs):

        try:
            response = requests.post(url, data=data, **kwargs)
            print(response)
        except Exception as e:
            raise AuthServerError(message='AuthServerError: {}'.format(str(e)))

        if response.status_code != 200 and not response.content:
            raise AuthServerError(message='http status:{}'.format(response.status_code))

        content = response.content.decode('utf-8')
        print(content)
        result = json.loads(content)
        if result.get('code') != 200:
            raise AuthServerError(response=content, message=result.get('msg'), code=result.get('code'))

        return result

    @staticmethod
    def _post_json_response(url, data=None, **kwargs):
        try:
            response = requests.post(url, json=data, **kwargs)
            print(response)
        except Exception as e:
            raise AuthServerError(message='AuthServerError: {}'.format(str(e)))

        if response.status_code != 200 and not response.content:
            raise AuthServerError(message='http status:{}'.format(response.status_code))

        content = response.content.decode('utf-8')
        print(content)
        result = json.loads(content)
        if result.get('code') != 200:
            raise AuthServerError(response=content, message=result.get('msg'), code=result.get('code'))

        return result

    def login(self, username, password):
        url = 'http://{host}/oauth/token?grant_type=password&appid={appId}&username={username}&password={password}'
        url = url.format(host=self.host,
                         appId=self.appId,
                         username=username,
                         password=password)
        return self._get_response(url)

    def login_implicit(self, username):
        """隐式登陆"""
        url = 'http://{host}/oauth/token?grant_type=implicit&appid={appId}&appsecret={appSecret}&username={username}'
        url = url.format(host=self.host,
                         appId=self.appId,
                         appSecret=self.appSecret,
                         username=username)
        return self._get_response(url)

    def info(self, token, raise_exception=True):
        """获取授权用户信息"""
        url = 'http://{host}/oauth/manager?appid={appId}&token={token}'.format(
            host=self.host,
            appId=self.appId,
            token=token
        )
        return self._get_response(url, raise_exception=raise_exception)

    def register(self, relation_id, username, password):
        url = 'http://{host}/oauth/manager?appid={appId}&appsecret={appSecret}'.format(
            host=self.host,
            appId=self.appId,
            appSecret=self.appSecret
        )
        data = dict(role='SocialOrganization',
                    relation_id=relation_id,
                    username=username,
                    password=password)
        return self._post_response(url, data=data)

    def manager_info(self, token):
        """获取授权用户信息"""
        url = 'http://{host}/oauth/manager?appid={appId}&token={token}'.format(
            host=self.host,
            appId='117998218',
            token=token
        )
        return self._get_response(url)

    def modify(self, password, new_password, headers):
        """修改密码"""
        url = 'http://{host}/oauth/repassword?appid={appId}'.format(host=self.host,
                                                                    appId=self.appId,
                                                                    )
        data = dict(password=password,
                    new_password=new_password)
        return self._post_json_response(url, data=data, headers=headers)

    def logout(self, token):
        url = 'http://{host}/oauth/logout?appid={appId}&token={token}'.format(
            host=self.host,
            appId=self.appId,
            token=token
        )
        data = dict()
        return self._post_response(url, data=data)
