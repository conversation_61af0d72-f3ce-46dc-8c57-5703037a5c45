#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import
import os
import re
import sys

import requests
from openpyxl.styles import Side, Border, Alignment, Font
from openpyxl.utils import get_column_letter


def set_sheet_dim(ws, max_len=None):
    """
    设置列宽
    """
    # 设置一个字典用于保存列宽数据
    dims = {}
    for row in ws.rows:
        for cell in row:
            side = Side(style='thin')
            cell.border = Border(left=side, right=side, bottom=side, top=side)
            cell.alignment = Alignment(horizontal='center', vertical='center')
            if cell.value:
                # 遍历整个表格，把该列所有的单元格文本进行长度对比，找出最长的单元格
                # 在对比单元格文本时需要将中文字符识别为1.7个长度，英文字符识别为1个，这里只需要将文本长度直接加上中文字符数量即可
                # re.findall('([\u4e00-\u9fa5])', cell.value)能够识别大部分中文字符
                cell_len = 0.7 * len(re.findall('([\u4e00-\u9fa5])', str(cell.value))) + len(str(cell.value))
                real_max_len = max((dims.get(cell.column, 0), cell_len))
                if max_len:
                    dims[cell.column] = real_max_len if real_max_len < max_len else max_len
                else:
                    dims[cell.column] = real_max_len
    for col, value in dims.items():
        # 设置列宽，get_column_letter用于获取数字列号对应的字母列号，最后值+5是用来调整最终效果的
        ws.column_dimensions[get_column_letter(col)].width = value + 5


class XlsxSet():
    def set_row_font(self, ws, row=1, max_col=10, bold=False):
        """
        设置指定行字体
        """
        for col in range(1, max_col + 1):
            cell = ws.cell(row=row, column=col)
            cell.font = Font(bold=bold)


def file_upload(file_path, file_name=None, mkdir="file"):
    upload_url = "http://filemanager.ztbory.com/v1/file/fileupload/"
    files = {
        "files": (file_name, open(file_path, 'rb'))
    }
    data = {
        "mkdir": mkdir
    }
    r = requests.post(upload_url, files=files, data=data)
    response_data = r.json()
    print(response_data)
    file_url = response_data.get("data")
    return file_url


def file_upload(file_path, file_name=None, mkdir="file"):
    upload_url = "http://filemanager.ztbory.com/v1/file/fileupload/"
    files = {
        "files": (file_name, open(file_path, 'rb'))
    }
    data = {
        "mkdir": mkdir
    }
    r = requests.post(upload_url, files=files, data=data)
    response_data = r.json()
    print(response_data)
    file_url = response_data.get("data")
    return file_url
