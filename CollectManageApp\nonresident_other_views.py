#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import time

from django.db.models import OuterRef, <PERSON><PERSON>, Count
from rest_framework import viewsets
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import api_view

from Base.utils.const import BOTH_NONRESIDENT_RUBBISHES
from Base.utils.base import get_uuid_str
from Base.utils.const import ConstRubbishType, NonresidentRubbishes
from Base.utils.tools import StandardResultsSetPagination, TimeFilterTools
from django.db.models.expressions import Exists, OuterRef
from CollectManageApp.filters import NonresidentCleaningPointRecordFilter, NonresidentCarRecordFilter, \
    NonresidentCleaningPointTransRecordFilter
from CollectManageApp.models_base import CleaningPointRecord, OrgGroup, Organization, CleaningPoint, CarRecord, Car, \
    TransportCompany, CarRecordOrg, CarFlowRecord, RubbishType, FactoryLocation, TerminalFactoryRecord, \
    CleaningPointTransRecord
from CollectManageApp.nonresident_services import NonresidentOtherService
from CollectManageApp.serializers import NonresidentCleaningPointRecordSer, NonresidentOutboundRecordSer, OtherCarRecordSer
from collections import OrderedDict
from CollectManageApp.models import OrgNonresidentDeclare

from CollectManageApp.views import OtherCarRecordViews
from Lic.core import IsInternalNetworkOrAuthenticated



class NonresidentOutboundRecord(viewsets.ReadOnlyModelViewSet):
    """
    非居民出站记录
    """
    serializer_class = NonresidentOutboundRecordSer
    pagination_class = StandardResultsSetPagination  # 分页器
    search_fields = ('name',)  # 搜索选项
    filter_fields = ('area_coding', 'street_coding', 'comm_coding')
    filter_class = NonresidentCleaningPointTransRecordFilter
    ordering_fields = ('-create_time',)
    queryset = CleaningPointTransRecord.objects.all()

    def get_queryset(self):
        queryset = self.queryset
        duration = self.request.GET.get('duration')
        if duration:
            queryset = TimeFilterTools.create_time_filter(self.request, queryset, duration)
        return queryset

    def get_paginated_response_data(self, data):
        assert self.paginator is not None
        queryset = self.filter_queryset(self.get_queryset())
        summary = queryset.aggregate(total_weight=Sum('weight'))
        return OrderedDict([
            ('count', self.paginator.page.paginator.count),
            ('summary', summary),
            ('results', data),
        ])

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return Response(self.get_paginated_response_data(serializer.data))
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class NonresidentCleaningPointRecord(viewsets.ReadOnlyModelViewSet):
    """
    非居民进站记录
    """
    queryset = CleaningPointRecord.objects.using('tidb_ljfl_db').filter(
        is_deleted=0,
        type_id=ConstRubbishType.OTHER
    ).order_by("-create_time")
    serializer_class = NonresidentCleaningPointRecordSer
    pagination_class = StandardResultsSetPagination  # 分页
    search_fields = ('name',)  # 搜索选项
    filter_fields = ('area_coding', 'street_coding', 'comm_coding')
    filter_class = NonresidentCleaningPointRecordFilter
    ordering_fields = ('-create_time',)

    def get_queryset(self):
        queryset = self.queryset
        
        # 新增按日月年过滤
        duration = self.request.GET.get('duration') or 'daily'
        queryset = TimeFilterTools.create_time_filter(self.request, queryset, duration)

        org_exists = Organization.objects.using('tidb_ljfl_db').filter(
            org_id=OuterRef('org_id'),
            is_declare=1,
            logout_status=0,
            is_deleted=0,
            rubbishes__contains=NonresidentRubbishes.OTHER
        )

        transport_company_id = self.request.GET.get('transport_company_id')
        if transport_company_id:
            org_exists = org_exists.filter(other_transport_company_id=transport_company_id)
        cleaning_point_exists = CleaningPoint.objects.using('tidb_ljfl_db').filter(
            cleaning_point_id=OuterRef('cleaning_point_id'),
            is_deleted__in=(0, 1),
            transport_company_id__isnull=False
        ).exclude(transport_company_id='')
        role = self.request.manager.get('role')
        if role in ["CleaningPointCollectorManager", "TransferPointManager"]:
            cleaning_point_id = self.request.manager.get('extra_configure').get("cleaning_point_id")
            queryset = queryset.filter(cleaning_point_id=cleaning_point_id)
        else:
            group_second_queryset = Organization.objects.filter(is_deleted=0, is_declare=1, logout_status=0, is_group_second=1).values_list('org_id', flat=True)
            queryset = queryset.filter(Exists(org_exists), Exists(cleaning_point_exists)).exclude(org_id__in=group_second_queryset)
        return queryset

    def get_paginated_response(self, data):
        assert self.paginator is not None
        queryset = self.filter_queryset(self.get_queryset())
        summary = queryset.aggregate(
            total_weight=Sum('weight'),
            org_count=Count("org_id", distinct=True)
        )

        return Response(
            (
                OrderedDict(
                    [
                        ("count", self.paginator.page.paginator.count),
                        ("summary", summary),
                        ("results", data),
                    ]
                )
            )
        )


class NonresidentCleaningPointRecordWeight(NonresidentCleaningPointRecord):
    """
    非居民进站记录
    """

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        stats = queryset.aggregate(weight=Sum('weight'),
                                   cleaning_point_count=Count('cleaning_point_id', distinct=True),
                                   org_count=Count('org_id', distinct=True),
                                   all_bucket_count=Count('id'),
                                   )
        cleaning_point_ids = queryset.values_list("cleaning_point_id", flat=True).distinct()
        cleaning_point = CleaningPoint.objects.filter(cleaning_point_id__in=list(cleaning_point_ids))
        company_ids = cleaning_point.values_list("transport_company_id", flat=True).distinct()
        company = TransportCompany.objects.filter(is_deleted=0, transport_company_id__in=list(company_ids))
        company_names = company.values_list("company", flat=True).distinct()
        cleaning_point_names = cleaning_point.values_list("name", flat=True).distinct()
        stats["company_names"] = company_names
        stats["cleaning_point_names"] = cleaning_point_names
        return Response(stats)

@api_view(['GET'])
def get_other_org_count(request):
    # 只统计 排放垃圾类型是 其他垃圾 是非居民台账数量
    area_code = request.GET.get('area_coding') or "110105000000"
    street_code = request.GET.get('street_coding')
    rubbishes = request.GET.get('rubbishes') or "OTHER"
    org_name = request.GET.get('org_name')
    clean_no = request.GET.get('clean_no')

    queryset = Organization.objects.using("tidb_ljfl_db").filter(is_deleted=0, is_declare=1, area_coding=area_code)
    queryset = queryset.filter(rubbishes__in=[rubbishes, BOTH_NONRESIDENT_RUBBISHES])
    if street_code:
        queryset = queryset.filter(street_coding=street_code)
    if clean_no:
        queryset = queryset.filter(clean_no=clean_no)

    if org_name:
        org = Organization.objects.filter(name__icontains=org_name)
        org_ids = set([i.org_id for i in org if i])
        if org_ids:
            queryset = queryset.filter(org_id__in=org_ids)
        else:
            return 0

    declare_queryset = OrgNonresidentDeclare.objects.all()
    declare_queryset = declare_queryset.filter(rubbishes=rubbishes)
    org_id_list = declare_queryset.filter(check_type=3, status=1).values_list('org_id', flat=True)
    queryset = queryset.filter(logout_status=0).exclude(org_id__in=list(org_id_list))
    queryset = queryset.filter(is_group_second=0)

    return Response(dict(total=queryset.count()))


class NonresidentCarRecordViews(viewsets.ReadOnlyModelViewSet):
    """
    非居民收运及记录
    """
    queryset = CarRecord.objects.using('tidb_ljfl_db').filter(is_deleted=0, type_id=ConstRubbishType.OTHER).order_by(
        "-create_time")
    # serializer_class = NonresidentCarRecordSer
    serializer_class = OtherCarRecordSer
    pagination_class = StandardResultsSetPagination  # 分页
    search_fields = ('name',)  # 搜索选项
    filter_fields = ('area_coding', 'street_coding', 'comm_coding')
    filter_class = NonresidentCarRecordFilter
    ordering_fields = ('-create_time',)

    def get_queryset(self):
        queryset = self.queryset
        # 新增按日月年过滤
        duration = self.request.GET.get('duration')
        if duration:
            queryset = TimeFilterTools.create_time_filter(self.request, self.queryset, duration)

        # car_nums = Car.objects.using('tidb_ljfl_db').filter(standing_book=1,
        #                                                     transport_type=1,
        #                                                     is_declare=1,
        #                                                     type_id=ConstRubbishType.OTHER,
        #                                                     ).values_list('car_num', flat=True)
        # queryset = queryset.filter(
        #     car_num__in=car_nums,
        # )
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        area_coding = request.GET.get("area_coding")
        street_coding = request.GET.get("street_coding")
        all_org = request.GET.get("all_org")
        logout_status = request.GET.get("logout_status", 0)

        car_query = Car.objects.using("tidb_ljfl_db").filter(is_declare=1,
                                                             standing_book=1,
                                                             type_id=ConstRubbishType.OTHER,
                                                             )

        org_query = Organization.objects.using("tidb_ljfl_db").filter(
            is_deleted=0,
            is_declare=1,
            logout_status=0,
            rubbishes__contains=NonresidentRubbishes.OTHER,
        )
        if area_coding:
            org_query = org_query.filter(area_coding=area_coding)
            car_query = car_query.filter(area_coding=area_coding)
        if street_coding:
            org_query = org_query.filter(street_coding=street_coding)

        if logout_status:
            org_query = org_query.filter(logout_status=logout_status)

        org_ids = org_query.values_list('org_id', flat=True)

        car_nums = car_query.values_list('car_num')
        queryset = queryset.filter(car_num__in=car_nums)

        all_weight = {
            "all_weight": 0,
            "car_count": 0,
            "org_count": 0,
        }

        if all_org and int(all_org) == 1:
            # 非居民主体和空主体记录
            # 汇总数据分开计算 - 优化
            all_weight = queryset.aggregate(weight=Sum('weight'),
                                            car_count=Count('car_num', distinct=True),
                                            )
            # 获取识别非居民主体数
            record_org_ids = queryset.values_list('org_id', flat=True).distinct()
            all_weight["org_count"] = len(set(org_ids) & set(record_org_ids))
        elif all_org and int(all_org) == 2:  # 未识别主体
            ids = queryset.filter(org_id__in=org_ids).values_list("id", flat=True)
            queryset = queryset.exclude(id__in=ids)
            all_weight = queryset.aggregate(weight=Sum('weight'),
                                            car_count=Count('car_num', distinct=True),
                                            )
            all_weight["org_count"] = 0

        else:
            # 识别主体
            queryset = queryset.filter(org_id__in=org_ids)
            all_weight = queryset.aggregate(weight=Sum('weight'),
                                            car_count=Count('car_num', distinct=True),
                                            org_count=Count('org_id', distinct=True))

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response({
                'all_weight': round(all_weight.get('weight') or 0, 2),
                'car_count': all_weight.get('car_count'),
                'org_count': all_weight.get('org_count'),
                'data': serializer.data,
            }
            )
        serializer = self.get_serializer(queryset, many=True)
        return Response({'all_weight': round(all_weight.get('weight') or 0, 2),
                         'car_count': all_weight.get('car_count'),
                         'org_count': all_weight.get('org_count'),
                         'data': serializer.data,
                         })


class NonresidentOtherCarRecordViews(OtherCarRecordViews, viewsets.ReadOnlyModelViewSet):
    """
    非居民其他收运记录
    """
    type_id = ConstRubbishType.OTHER
    car_queryset = Car.objects.using("tidb_ljfl_db").filter(is_declare=1, standing_book=1, transport_type=1,
                                                            type_id=type_id)
    queryset = CarRecord.objects.using('tidb_ljfl_db').filter(is_deleted=0, type_id=type_id).order_by("-create_time")


class NonresidentCarRecordWeightViews(NonresidentCarRecordViews):
    """
    非居民收运及记录 统计
    """
    queryset = CarRecord.objects.using('tidb_ljfl_db').filter(is_deleted=0, type_id=ConstRubbishType.OTHER)
    search_fields = ('name',)  # 搜索选项
    filter_fields = ('area_coding', 'street_coding', 'comm_coding')
    filter_class = NonresidentCarRecordFilter
    ordering_fields = ('-create_time',)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        stats = queryset.aggregate(weight=Sum('weight'),
                                   car_count=Count('car_num', distinct=True),
                                   org_count=Count('org_id', distinct=True),
                                   all_bucket_count=Count('id'),
                                   )
        return Response(stats)


class OrgInfo(APIView):

    # @page_cache(5*60)
    def get(self, request):
        params = request.GET.dict()
        org_id = params.get('org_id')
        if not org_id:
            return Response({'code': 400, 'msg': '参数不全！'})
        service = NonresidentOtherService(request)
        org_info = service.get_org_info_by_org_id()

        if not org_info:
            return Response({'code': 404, 'msg': '主体不存在！'})

        return Response(data=org_info)


class CleaningPointInfo(APIView):

    # @page_cache(5*60)
    def get(self, request):
        params = request.GET.dict()
        cleaning_point_id = params.get('cleaning_point_id')
        if not cleaning_point_id:
            return Response({'code': 400, 'msg': '参数不全！'})
        service = NonresidentOtherService(request)
        info = service.get_cleaning_point_info_by_id()

        if not info:
            return Response({'code': 404, 'msg': '不存在！'})

        return Response(data=info)


class OrgRecordBillsALL(APIView):

    def get(self, request):
        '''
        责任主体返回电子联单总数
        org_id:
        '''
        org_id = request.GET.get('org_id', '')
        duration = request.GET.get('duration', 'daily')
        start_date = request.GET.get('start_date', )
        end_date = request.GET.get('end_date', )
        if not org_id or not start_date or not end_date:
            return Response({"code": 4003, "msg": "参数不全!"})
        car_record_org_qs = CarRecordOrg.objects.filter(is_deleted=0, org_id=org_id,
                                                        type_id='b8c900bac02a11eaa8a9000c29d3cc31')
        # 走正常流程联单
        car_record_org_qs = TimeFilterTools.create_time_filter(request, car_record_org_qs, duration)
        weight = 0
        count = 0
        org_name = ''
        if car_record_org_qs.exists():
            car_record_org_qs_result = car_record_org_qs.values('car_flow_id', 'org_id', 'org_name', 'car_num',
                                                                'type_id', 'capacity').annotate(
                count=Count('id')).annotate(
                weight=Sum('weight'))
            car_list = []
            company_list = []
            for bill in car_record_org_qs_result:
                try:
                    car_obj = Car.objects.get(is_deleted=0, car_num=bill.get('car_num'))
                    company = car_obj.company
                except:
                    company = ''
                company_list.append(company)
                car_list.append(bill.get('car_num'))
                # 添加垃圾去向
                weight += bill.get('weight')
                count += bill.get('count')
                org_name = bill.get('org_name')
                org_id = bill.get('org_id')
            data = {
                'org_name': org_name,
                'org_id': org_id,
                'car_num': ','.join(list(set(car_list))),
                'rubbish_type_name': '其他垃圾',
                'company': ','.join(list(set(company_list))),  # 运输服务公司 ，
                'count': count,
                'weight': weight,
            }
            return Response(data=data)
        else:
            # 用car_record 表
            car_record_qs = CarRecord.objects.filter(is_deleted=0, org_id=org_id,
                                                     type_id='b8c900bac02a11eaa8a9000c29d3cc31')
            car_record_qs = TimeFilterTools.create_time_filter(request, car_record_qs, duration)
            car_record_org_qs_result = car_record_qs.values('org_id', 'org_name', 'car_num', 'type_id').annotate(
                count=Count('id')).annotate(weight=Sum('weight'))
            company_list = []
            car_list = []
            for bill in car_record_org_qs_result:
                try:
                    car_obj = Car.objects.get(is_deleted=0, car_num=bill.get('car_num'))
                    company = car_obj.company
                except:
                    company = ''
                company_list.append(company)
                car_list.append(bill.get('car_num'))
                # 添加垃圾去向
                weight += bill.get('weight')
                count += bill.get('count')
                org_name = bill.get('org_name')
                org_id = bill.get('org_id')
            data = {
                'org_name': org_name,
                'org_id': org_id,
                'car_num': ','.join(list(set(car_list))),
                'rubbish_type_name': '其他垃圾',
                'company': ','.join(list(set(company_list))),  # 运输服务公司 ，
                'count': count,
                'weight': weight,
            }

            return Response(data=data)


class OrgRecordBills(APIView):

    def get(self, request):
        '''
          责任主体返回多个电子联单
          org_id:
          '''
        org_id = request.query_params.get('org_id', '')
        duration = request.query_params.get('duration', 'daily')
        start_date = request.query_params.get('start_date', )
        end_date = request.query_params.get('end_date', )
        if not org_id or not start_date or not end_date:
            return Response({"code": 4003, "msg": "参数不全!"})

        car_record_qs = CarFlowRecord.objects.filter(is_deleted=0, type_id='b8c900bac02a11eaa8a9000c29d3cc31')
        car_record_org_qs = CarRecordOrg.objects.filter(is_deleted=0, org_id=org_id,
                                                        type_id='b8c900bac02a11eaa8a9000c29d3cc31')
        car_record_org_qs = TimeFilterTools.create_time_filter(request, car_record_org_qs, duration)
        rubbish_type_dict = {type.get('type_id'): type.get('name') for type in
                             RubbishType.objects.all().values('type_id', 'name')}
        location = FactoryLocation.objects.filter(is_deleted=0)
        location_map = {i.factory_location_id: i.alias_name or i.name for i in location}
        if car_record_org_qs.exists():
            # 收运总桶数量和总的条数
            car_record_org_qs_result = car_record_org_qs.values('car_flow_id', 'org_id', 'org_name', 'car_num',
                                                                'type_id', 'capacity').annotate(
                count=Count('id')).annotate(
                weight=Sum('weight'))
            bill_list = []
            for bill in car_record_org_qs_result:
                try:
                    car_obj = Car.objects.get(is_deleted=0, car_num=bill.get('car_num'))
                    company = car_obj.company
                except:
                    company = ''
                # 添加垃圾去向
                car_flow_id = bill.get('car_flow_id')
                try:
                    flow_obj = car_record_qs.get(car_flow_id=car_flow_id)
                    factory_location_name = location_map.get(
                        flow_obj.factory_location_id) or flow_obj.factory_location_name
                    factory_location_id = flow_obj.factory_location_id
                    confirm_time = flow_obj.finished_time or flow_obj.create_time
                except:
                    factory_location_name = ''
                    factory_location_id = ''
                    # 如果查不到时间默认当前时间
                    confirm_time = int(time.time())
                bill_list.append(
                    {'car_flow_id': car_flow_id,
                     'org_name': bill.get('org_name'),
                     'org_id': bill.get('org_id'),
                     'car_num': bill.get('car_num'),
                     'rubbish_type_name': rubbish_type_dict.get(bill.get('type_id')) or '',
                     'company': company,
                     'count': bill.get('count'),
                     'weight': bill.get('weight'),
                     'factory_location_name': factory_location_name,
                     'factory_location_id': factory_location_id,
                     'confirm_time': confirm_time,
                     'modify_capacity': bill.get('capacity') or ''
                     }
                )
            return Response(data=bill_list)
        else:
            # 用car_record 表
            car_record_qs = CarRecord.objects.filter(is_deleted=0, org_id=org_id)
            car_record_qs = TimeFilterTools.create_time_filter(request, car_record_qs, duration)
            car_record_org_qs_result = car_record_qs.values('org_id', 'org_name', 'car_num', 'type_id').annotate(
                count=Count('id')).annotate(weight=Sum('weight'))
            last_time = car_record_qs.order_by('-id').first().create_time if car_record_qs.exists() else ''
            bill_list = []
            for bill in car_record_org_qs_result:
                try:
                    car_obj = Car.objects.get(is_deleted=0, car_num=bill.get('car_num'))
                    company = car_obj.company
                except:
                    company = ''
                try:
                    # 从末端厂拿出数据
                    stats_obj = TerminalFactoryRecord.objects.filter(
                        platenumber=bill.get('car_num')).order_by('-id').first()
                    factory_location_name = ''
                    factory_location_id = ''
                    if stats_obj:
                        terminal_time = stats_obj.weightptime.timestamp()
                        # 如果末端厂时间大于车辆收运的最后时间说明本次收运确实到达末端厂   否则不返回末端信息
                        if last_time < terminal_time:
                            factory_location_name = location_map.get(
                                stats_obj.factory_location_id) or stats_obj.weighingName
                            factory_location_id = stats_obj.factory_location_id

                except:
                    factory_location_name = ''
                    factory_location_id = ''
                bill_list.append(
                    {'car_flow_id': get_uuid_str(),
                     'org_name': bill.get('org_name'),
                     'org_id': bill.get('org_id'),
                     'car_num': bill.get('car_num'),
                     'rubbish_type_name': rubbish_type_dict.get(bill.get('type_id')) or '',
                     'company': company,
                     'count': bill.get('count'),
                     'weight': bill.get('weight'),
                     'factory_location_name': factory_location_name,
                     'factory_location_id': factory_location_id,
                     'confirm_time': last_time,
                     'modify_capacity': bill.get('capacity') or ''
                     }
                )
            return Response(data=bill_list)


class CarWaringStats(APIView):
    permission_classes = (IsInternalNetworkOrAuthenticated, )

    def get(self, request):
        service = NonresidentOtherService(request)
        stats = service.get_car_warning_stats()
        return Response(data=stats)


class CleaningPointWaringStats(APIView):
    permission_classes = (IsInternalNetworkOrAuthenticated, )

    def get(self, request):
        service = NonresidentOtherService(request)
        stats = service.get_cleaning_point_warning_stats()
        return Response(data=stats)
