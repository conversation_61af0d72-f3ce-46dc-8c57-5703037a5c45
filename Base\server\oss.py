#!/home/<USER>/venv/bin/python3
# coding: utf-8
from __future__ import absolute_import

import json
import requests
from django.conf import settings


class OssServerError(Exception):

    def __init__(self, response=None, code=500, message=''):
        super(OssServerError, self).__init__()
        self.response = response
        self.code = code
        if message:
            message = '基础服务异常[{}]!'.format(message)
        else:
            message = '基础服务异常!'
        self.message = message


class OssServer:
    def __init__(self):
        self.host = settings.OSS_HOST

    @staticmethod
    def _get_response(url, params=None):
        try:
            response = requests.get(url, params=params)
        except Exception as e:
            raise OssServerError(message='OssServerError: {}'.format(str(e)))

        if response.status_code != 200 and not response.content:
            raise OssServerError(message='http status:{}'.format(response.status_code))

        content = response.content.decode('utf-8')
        result = json.loads(content)
        if result.get('code') != 1:
            raise OssServerError(response=content, message=result.get('msg'), code=result.get('code'))

        return result.get('data')

    @staticmethod
    def _post_response(url, headers=None, files=None, data=None, **kwargs):
        try:
            response = requests.post(url, headers=headers, files=files, data=data, **kwargs)
        except Exception as e:
            raise OssServerError(message='OssServerError: {}'.format(str(e)))

        if response.status_code != 200 and not response.content:
            raise OssServerError(message='http status:{}'.format(response.status_code))

        content = response.content.decode('utf-8')
        result = json.loads(content)
        if result.get('code') != 1:
            raise OssServerError(response=content, message=result.get('msg'), code=result.get('code'))

        return result.get('data')

    def upload(self, files):
        """文件上传"""
        url = 'http://{host}/v1/file/fileupload/'.format(host=self.host)
        data = {'mkdir': 'license'}
        return self._post_response(url, data=data, files=files)
