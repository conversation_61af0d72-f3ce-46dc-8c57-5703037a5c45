import datetime
import urllib
from urllib.parse import urlencode

from django.core.cache import cache
from django.forms.models import model_to_dict
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from ztbory_django_shield.base.core import Shield

from Base.server import wechat_server, auth_server
from Base.utils.base import get_uuid_str
from Base.api import ApiResponse, ConstCode, get_logger
from Base.utils.cryptor import sm4_cryptor
from CollectManageApp.models import AuthorizedOrg
from CollectManageApp.models_base import WechatSessionKey, WeChatAppID, CarType, Car, CleaningPoint, RubbishType, \
    CityRegion
from CollectManageApp.models_transport import TransportUser, TransportDriver, Company
from .decrypt_wechat_data import WXBizDataCrypt, is_default_info_by_version

logger = get_logger('django')


class MiniLoginVs(APIView):
    """
    微信登录
    """
    permission_classes = (AllowAny,)
    queryset = AuthorizedOrg.objects.filter(is_deleted=0)

    def post(self, request):
        params = request.data.copy()
        logger.info(params)
        code = params.get('code')
        xcx_version = params.get('version')  # 小程序基础库版本号
        # encryptedData = params.get('encryptedData')
        iv = params.get('iv')
        login_type = params.get('login_type', 'collect_manage')

        if not all((code)):
            return ApiResponse(ConstCode.BadRequest, msg='参数错误.')

        cache_wechat_item = self.get_wechat_config_info(login_type)
        if not cache_wechat_item:
            return ApiResponse(ConstCode.BadRequest, msg='小程序配置信息获取失败.')

        spec = {
            'app_id': cache_wechat_item['app_id'],
            'app_secret': cache_wechat_item['app_secret'],
            "js_code": code,
        }
        logger.info(f"spec{spec}")
        data = wechat_server.get_js_code_session(**spec)
        if not data.get('session_key'):
            return ApiResponse(ConstCode.BadRequest, msg='获取session_key失败.')

        open_id = data['openid']
        session_key = data['session_key']

        wechat_session_key_obj = WechatSessionKey.objects.filter(open_id=open_id).first()
        if not wechat_session_key_obj:
            WechatSessionKey.objects.create(**{'open_id': open_id, 'session_key': session_key, 'uuid': get_uuid_str()})
        else:
            if wechat_session_key_obj.session_key != session_key:
                wechat_session_key_obj.session_key = session_key
                wechat_session_key_obj.save()

        # 解密 encrypted_data
        logger.info(f"cache_wechat_item:{cache_wechat_item}")
        logger.info(f"session_key:{session_key}")
        # decrypt_biz = WXBizDataCrypt(cache_wechat_item['app_id'], session_key)
        # decrypt_result = decrypt_biz.decrypt(encryptedData, iv)
        # decrypt_result.update({'open_id': open_id})
        update_user_info = False
        if xcx_version:
            is_default_info = is_default_info_by_version(xcx_version)
            update_user_info = not is_default_info
        # 本地测试
        # decrypt_result = dict(
        #     open_id='zhimakaimen',
        #     nickName='土豆',
        #     avatarUrl='https://c-ssl.duitang.com/uploads/item/202004/19/20200419105341_udfqg.jpeg'
        # )
        resident_dict = self.band_resident_record({'open_id': open_id}, update_user_info=update_user_info)

        # 更新client_id
        try:
            if open_id:
                auth_user = self.queryset.filter(open_id=open_id).first()
                if not auth_user.client_id:
                    auth_user.client_id = login_type
                    auth_user.save()
        except Exception as e:
            print(f"client_id 更新失败:{e}")

        return resident_dict

    def get_wechat_config_info(self, login_type):
        """
        获取微信配置 AppId  AppSecret
        :param login_type:
        :return:
        """
        login_type = login_type or 'collect_manage'
        key = f'Cache_wechat_conf:{login_type}'
        cache_wechat_conf = cache.get(key, {})
        if not cache_wechat_conf:
            wechat_obj = WeChatAppID.objects.filter(login_type=login_type).first()
            if not wechat_obj:
                return {}
            item = model_to_dict(wechat_obj, exclude=['id', 'name', 'note', 'state', 'update_time', 'create_time'])
            if not item:
                return {}

            cache_wechat_conf = {'app_id': item['app_id'], 'app_secret': item['app_secret'],
                                 'expire_in': item['expire_in'], 'area_coding': item['area_coding']}
            cache.set(key, cache_wechat_conf, cache_wechat_conf['expire_in'])

        return cache_wechat_conf

    def band_resident_record(self, decrypt_result, update_user_info=False):
        """
        校验居民是否存在不存在则绑定居民信息
        :param decrypt_result: 解密出来用户信息
        :param update_user_info: 是否更新用户信息
        :return:
        """
        open_id = decrypt_result['open_id']
        username = 'SC_{}'.format(open_id)
        resident_item = {
            'open_id': open_id,
            'username': username,
            'wechat_nickname': decrypt_result.get('nickName', ''),
            'wechat_picture': decrypt_result.get('avatarUrl', '')
        }

        resident_obj = self.queryset.filter(open_id=open_id).first()
        if not resident_obj:
            relation_id = get_uuid_str()
            _datetime = datetime.datetime.today()
            resident_item['register_time'] = _datetime
            resident_item['update_time'] = _datetime
            resident_item['relation_id'] = relation_id
            resident_item['is_deleted'] = 0
            resident_obj = self.queryset.create(**resident_item)
        else:
            relation_id = resident_obj.relation_id

        try:
            auth_server.register(relation_id=relation_id,
                                 username=username,
                                 password='so123456')
        except Exception:
            pass

        # 校验 当前登录者微信名称及头像是否变更
        # 不支持头像昵称获取
        # if update_user_info:
        #     is_update_resident = False
        #     if resident_obj.wechat_nickname != resident_item['wechat_nickname']:
        #         resident_obj.wechat_nickname = resident_item['wechat_nickname']
        #         is_update_resident = True
        #     if resident_obj.wechat_picture != resident_item['wechat_picture']:
        #         resident_obj.wechat_picture = resident_item['wechat_picture']
        #         is_update_resident = True
        #     if is_update_resident:
        #         resident_obj.save()
        # else:
        #     resident_item['wechat_nickname'] = resident_obj.wechat_nickname
        #     resident_item['wechat_picture'] = resident_obj.wechat_picture

        login_result = auth_server.login_implicit(username=username)
        token = login_result['data']['token']
        authorized = model_to_dict(resident_obj)
        authorized.update(dict(token=token))
        return ApiResponse(data=authorized)


class SignOutVs(APIView):
    """
    微信退出登录
    """

    def post(self, request):
        auth_server.logout(request.token)
        return Response({'ok': True})


class LoginVs(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        params = request.data.copy()
        if not (params.get('username') and params.get('password')):
            return Response({'msg': '参数错误.', 'code': 400})

        user_obj = AuthorizedOrg.objects.filter(phone=params['username'], is_deleted=0).first()
        if not user_obj:
            return Response({'msg': '账号不存在.', 'code': 400})

        login_result = auth_server.login(params['username'], params['password'])
        token_data = login_result['data']

        # 根据token 获取 登录信息
        login_info_result = auth_server.info(token_data['token'])
        result = login_info_result['data']['manager']

        result.update(dict({'token_data': token_data, **model_to_dict(user_obj)}))
        return Response(result)


class LoginCar(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        params = request.data.copy()
        username = params.get('username', '')
        password =params.get('password', '')
        if not (params.get('username') and params.get('password')):
            return Response({'msg': '参数错误.', 'code': 400})

        is_password_safely = Shield.is_password_safely(username, password)

        login_result = auth_server.login(username, urllib.parse.quote(password))
        token_data = login_result['data']

        # 根据token 获取 登录信息
        login_info_result = auth_server.info(token_data['token'])
        result = login_info_result['data']['manager']

        car_type_data = {}
        car_item = Car.objects.filter(car_num=username, is_deleted=0).values('car_type_id', 'type_id',
                                                                                       "is_declare").first()
        prex = "COLLECT_APP_USER_PASSWORD_ERROR_"
        error_password_key = f"{prex}{username}"

        times = cache.get(error_password_key) or 0
        if times >= 6:
            return Response({
                "code": 400,
                "msg": "授权异常[账号已被锁定.]!"
            })
        if result.get("role") == "TransportDriver" and car_item and car_item.get("is_declare") == 1:
            pass
        elif result.get("role") == "CleaningPointCollectorManager":
            pass
        else:
            times += 1
            cache.set(error_password_key, times, 60 * 10)
            return Response({
                "code": 400,
                "msg": f"授权异常[账户不存在或密码错误.密码错误6次账户将会锁定10分钟, 目前错误次数:{times}.]!"
            })

        if car_item:
            car_type_id = car_item['car_type_id']
            car_type_item = CarType.objects.filter(car_type_id=car_type_id).values('name').first()
            car_type_name = car_type_item['name'] if car_type_item else ''
            type_id = car_item['type_id']
            is_declare = car_item.get("is_declare")
            car_type_data.update({
                'car_type_id': car_type_id,
                'car_type_name': car_type_name,
                'type_id': type_id,
                'is_declare': is_declare
            })

        result.update({'token_data': token_data,
                       'car_type_data': car_type_data,
                       'is_password_safely': is_password_safely,
                       })
        return Response(result)

class LoginCarEncrypt(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        params = request.data.copy()
        username = params.get('username', '')
        password =params.get('password', '')
        if not (params.get('username') and params.get('password')):
            return Response({'msg': '参数错误.', 'code': 400})

        try:
            username = sm4_cryptor.decrypt(username)
            password = sm4_cryptor.decrypt(password)
        except Exception as e:
            return Response({'msg': '请对参数进行加密传输.', 'code': 400})

        is_password_safely = Shield.is_password_safely(username, password)

        login_result = auth_server.login(username, urllib.parse.quote(password))
        token_data = login_result['data']

        # 根据token 获取 登录信息
        login_info_result = auth_server.info(token_data['token'])
        result = login_info_result['data']['manager']

        car_type_data = {}
        car_item = Car.objects.filter(car_num=username, is_deleted=0).values('car_type_id', 'type_id',
                                                                                       "is_declare").first()
        prex = "COLLECT_APP_USER_PASSWORD_ERROR_"
        error_password_key = f"{prex}{username}"

        times = cache.get(error_password_key) or 0
        if times >= 6:
            return Response({
                "code": 400,
                "msg": "授权异常[账号已被锁定.]!"
            })
        if result.get("role") == "TransportDriver" and car_item and car_item.get("is_declare") == 1:
            pass
        elif result.get("role") == "CleaningPointCollectorManager":
            pass
        else:
            times += 1
            cache.set(error_password_key, times, 60 * 10)
            return Response({
                "code": 400,
                "msg": f"授权异常[账户不存在或密码错误.密码错误6次账户将会锁定10分钟, 目前错误次数:{times}.]!"
            })

        if car_item:
            car_type_id = car_item['car_type_id']
            car_type_item = CarType.objects.filter(car_type_id=car_type_id).values('name').first()
            car_type_name = car_type_item['name'] if car_type_item else ''
            type_id = car_item['type_id']
            is_declare = car_item.get("is_declare")
            car_type_data.update({
                'car_type_id': car_type_id,
                'car_type_name': car_type_name,
                'type_id': type_id,
                'is_declare': is_declare
            })

        result.update({'token_data': token_data,
                       'car_type_data': car_type_data,
                       'is_password_safely': is_password_safely,
                       })
        return Response(result)

class LoginCleaningPoint(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        params = request.data.copy()
        if not (params.get('username') and params.get('password')):
            return Response({'msg': '参数错误.', 'code': 400})

        login_result = auth_server.login(params['username'], params['password'])
        token_data = login_result['data']

        # 根据token 获取 登录信息
        login_info_result = auth_server.info(token_data['token'])
        result = login_info_result['data']['manager']

        data_info = {}
        obj = CleaningPoint.objects.filter(name=result['realname'], is_deleted=0).first()
        rubbish_type = RubbishType.objects.filter(is_deleted=0)
        rubbish_type_map = {i.type_id: i.name for i in rubbish_type}

        city_region = CityRegion.objects.filter(is_deleted=0).only("name", "coding")
        city_region_map = {i.coding: i.name for i in city_region}

        if obj:
            name = obj.name
            cleaning_point_id = obj.cleaning_point_id
            type_id = obj.type_id
            type_name = rubbish_type_map.get(type_id)

            data_info.update({
                'name': name,
                'cleaning_point_id': cleaning_point_id,
                'area_coding': obj.area_coding,
                'area_name': city_region_map.get(obj.area_coding),
                'street_coding': obj.street_coding,
                'street_name': city_region_map.get(obj.street_coding),
                'comm_coding': obj.comm_coding,
                'comm_name': city_region_map.get(obj.comm_coding),
                'type_id': type_id,
                'type_name': type_name,
                'transport_company_id': obj.transport_company_id
            })

        result.update({'token_data': token_data, 'data_info': data_info})
        return Response(result)


class LoginByWechatPhone(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        params = request.data.copy()

        encrypted_data = params.get('encrypted_data')
        iv = params.get('iv')
        open_id = params.get('open_id')
        login_type = 'collect_manage'

        if not all((encrypted_data, iv, open_id)):
            return Response({'msg': '微信小程序获取手机号参数错误.', 'code': 400})

        session_key_obj = WechatSessionKey.objects.filter(open_id=open_id).first()
        if not session_key_obj:
            return Response({'msg': '微信小程序获取SessionKey失败.', 'code': 400})

        key = f'Cache_wechat_conf:{login_type}'
        cache_wechat_conf = cache.get(key, {})
        if not cache_wechat_conf:
            wechat_obj = WeChatAppID.objects.filter(**{'state': 100, 'name': '非居民端责任主体收运'}).first()
            cache_wechat_conf = model_to_dict(wechat_obj,
                                              exclude=['id', 'name', 'note', 'state', 'update_time', 'create_time'])
            if not cache_wechat_conf:
                return Response({'msg': '微信小程序获取配置失败.', 'code': 400})

        session_key = session_key_obj.session_key
        decrypt_biz = WXBizDataCrypt(cache_wechat_conf['app_id'], session_key)
        decrypt_result = decrypt_biz.decrypt(encrypted_data, iv)
        phone = decrypt_result.get("phoneNumber")

        return Response({'phone': phone})


class CompanyRfidLogin(APIView):

    def post(self, request):
        params = request.data.copy()
        if not (params.get('username') and params.get('password')):
            return Response({'msg': '参数错误.', 'code': 400})
        # 授权中心通过>角色是收运公司>资质审核通过（资质审核库中能查到该用户而且user_status=1）>登录成功！
        login_result = auth_server.login(params['username'], params['password'])
        token_data = login_result['data']

        login_info_result = auth_server.info(token_data['token'])
        result = login_info_result['data']['manager']
        role = result.get('role')
        user_obj = TransportUser.objects.filter(username=params['username'], user_status=1).first()
        if role != 'Qualification' or not user_obj:
            return Response({'msg': '账号不允许登录.', 'code': 400})
        if not user_obj:
            return Response({'msg': '账号不存在.', 'code': 400})
        # 添加收运公司标识
        company_rec_dict = {'cy贾梦瑶87528115': 'GuFei'}
        company_rec = company_rec_dict.get(params['username']) or ''
        result.update({'token': token_data['token'], 'company_rec': company_rec})
        return Response(result)


class CollectAppForgetPassword(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        params = request.data.copy()
        username = params.get('username')
        if not params.get('username'):
            return Response({'msg': '参数错误.', 'code': 400})
        obj = TransportDriver.objects.filter(username=username).first()
        if not obj:
            return Response({'msg': '账号不存在或无绑定公司.', 'code': 400})
        transport_company_id = obj.transport_company_id
        company = Company.objects.filter(is_delete=0, status=1, company_id=transport_company_id).first()
        if not company:
            return Response({'msg': '账号不存在或无绑定公司!', 'code': 400})
        phone = company.phone
        admin = company.admin
        return Response({
            "admin":admin,
            "phone": sm4_cryptor.encrypt(phone)
        })


class LiveAreaLogin(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        params = request.data.copy()
        username = params.get('username', '')
        password = params.get('password', '')
        if not (params.get('username') and params.get('password')):
            return Response({'msg': '参数错误.', 'code': 400})

        try:
            username = sm4_cryptor.decrypt(username)
            password = sm4_cryptor.decrypt(password)
        except Exception as e:
            return Response({'msg': '请对参数进行加密传输.', 'code': 400})

        is_password_safely = Shield.is_password_safely(username, password)

        login_result = auth_server.login(username, urllib.parse.quote(password))
        token_data = login_result['data']

        prex = "LIVE_AREA_USER_PASSWORD_LOCK_"
        error_password_key = f"{prex}{username}"

        times = cache.get(error_password_key) or 0
        # 根据token 获取 登录信息
        login_info_result = auth_server.info(token_data['token'])
        result = login_info_result['data']['manager']

        if times >= 6:
            return Response({
                "code": 400,
                "msg": "授权异常[账号已被锁定.]!"
            })
        if result.get("role") == "CommManager":
            pass
        else:
            times += 1
            cache.set(error_password_key, times, 60 * 10)
            return Response({
                "code": 400,
                "msg": f"授权异常[账户不存在或密码错误.密码错误6次账户将会锁定10分钟, 目前错误次数:{times}.]!"
            })
        result.update({
            'token_data': token_data,
            'is_password_safely': is_password_safely,
        })
        return Response(result)
