from __future__ import absolute_import
from Base.api import logger
import json
import requests
from django.conf import settings

class QualificationServeError(Exception):

    def __init__(self, response=None, code=500, message=''):
        super(QualificationServeError, self).__init__()
        self.response = response
        self.code = code
        if message:
            message = '资质审核系统异常[{}]!'.format(message)
        else:
            message = '基础服务异常!'
        self.message = message


class QualificationServe():

    def __init__(self):
        self.host = settings.QUALIFI_HOST

    @staticmethod
    def _get_response(url, params=None):
        try:
            response = requests.get(url, params=params)
        except Exception as e:
            raise QualificationServeError(message='QualificationServeError: {}'.format(str(e)))

        if response.status_code != 200 and not response.content:
            raise QualificationServeError(message='http status:{}'.format(response.status_code))

        content = response.content.decode('utf-8')
        result = json.loads(content)
        if result.get('code') != 200:
            raise QualificationServeError(response=content, message=result.get('msg'), code=result.get('code'))

        return result.get('data')

    #获取用户信息
    def get_compamy_message(self,credit_code):

        url = 'https://{host}/qualification/contract_new/?credit_code={credit_code}'
        url = url.format(host=self.host,credit_code=credit_code)
        return self._get_response(url)


