import json

from django.utils.deprecation import MiddlewareMixin

from utils.response_filter import ResponseDataFilter


class ResponseFilterMiddleware(MiddlewareMixin):
    """响应数据脱敏中间件"""

    def process_response(self, request, response):
        """处理响应数据"""
        # 只处理JSON响应
        content_type = response.get('Content-Type', '')
        if 'application/json' in content_type:
            try:
                # 解析响应数据
                if hasattr(response, 'content') and response.content:
                    data = json.loads(response.content.decode('utf-8'))

                    # 进行脱敏处理
                    filtered_data = ResponseDataFilter.filter_sensitive_data(data)

                    # 更新响应内容
                    response.content = json.dumps(filtered_data, ensure_ascii=False).encode('utf-8')

                    # 更新Content-Length
                    response['Content-Length'] = len(response.content)

            except (json.JSONDecodeError, UnicodeDecodeError, AttributeError):
                pass

        return response