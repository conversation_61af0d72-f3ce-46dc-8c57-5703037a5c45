#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import *

DEBUG = False

SCHEDULER_SWITCH = True

ALLOWED_HOSTS = [
    '*'
]
DATABASES = {
    'default': {},
    'ljfl_declare_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_declare_db',
        'USER': 'collectmanage',
        'HOST': '************',
        'PASSWORD': 'Collectmanage_20220625',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'collectmanage',
        'HOST': '************',
        'PASSWORD': 'Collectmanage_20220625',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'ljfl_db_replica': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'collectmanage',
        'HOST': '************',
        'PASSWORD': 'Collectmanage_20220625',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'transport_db': {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': '************',  # 生产环境
        'PORT': 3306,
        'USER': 'transport',
        'PASSWORD': 'Transport_db20210727',
        'NAME': 'transport_db',
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'solid': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'solid_logistics',
        'USER': 'solidlogistics',
        'PASSWORD': 'solidlogistics20200107',
        'HOST': '************',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    "jfpt": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "bj_jfpt_admin",
        "USER": "collectmanage",
        "HOST": "************",
        "PASSWORD": "Collectmanage_20220625",
        "PORT": 3306,
    },
    "tidb_ljfl_db": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "ljfl_db",
        "USER": "cityljfl",
        "PASSWORD": "City_ljfl20221024",
        "HOST": "************",
        "PORT": 3999,
        "OPTIONS": {"charset": "utf8mb4"},
    },
    "other_transport_db": {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': '************',  # 生产环境
        'PORT': '3306',
        'USER': 'Other_tran',
        'PASSWORD': 'Other_tran20220819',
        'NAME': 'other_transport_db',
        'OPTIONS': {'charset': 'utf8mb4'},
    },
    "bagbreak_db": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "bagbreak",
        "USER": "bagbreak",
        "PASSWORD": "Bag_break20230103",
        "PORT": 3306,
        "HOST": "************",
        "OPTIONS": {"charset": "utf8mb4"},
    },
}

SENTINELS = [
    ("************", 16380),
    ("************", 16380),
    ("***********", 16380),
]
DJANGO_REDIS_CONNECTION_FACTORY = "django_redis.pool.SentinelConnectionFactory"
# Enable the alternate connection factory.

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "KEY_PREFIX": "apiProduction:collectManageAPPV3",
        # The hostname in LOCATION is the primary (service / master) name
        "LOCATION": "redis://ztbrmaster/4",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.SentinelClient",
            "SENTINELS": SENTINELS,
        },
    },
}

# 管理授权
AUTH_APPID = *********
AUTH_APPSECRET = 'jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7'
AUTH_HOST = '***********:8296'
# AUTH_HOST = 'auth.ztbory.com'
# 基础服务
BASE_HOST = '***********:8290'
# OSS服务
OSS_HOST = 'filemanager.ztbory.com'
# 资质审核服务
QUALIFI_HOST = '***********:8520'

COMPANY_SYS_IP = 'http://***********:8520'

# 市级服务
CITY_IP = 'http://***********:8094'

INTERNAL_NETWORKS = ["***********/24", "************"]
