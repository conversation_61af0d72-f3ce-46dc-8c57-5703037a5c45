#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from django.core.cache import cache
from django.db import transaction

from ..logger import logger
from .base import BaseCleanCodeGenerator

from CollectManageApp.models_base import TransportCompany, TransportCompanyArea, CityRegion
from CollectManageApp.models_transport import Company, CompanyArea


class OrgTransportAreaCleanCodeGenerator(BaseCleanCodeGenerator):
    """
    排放登记编码生成器: 主体-收运单位-收运范围
    """
    CLEAN_TYPE = 'ORG'
    CLEAN_SUBTYPE = 'TRANSPORT-AREA'

    def generate(self, clean_id):
        # 1. 获取编码前缀
        transport_area = TransportCompanyArea.objects.filter(transport_company_area_id=clean_id,
                                                             is_deleted=0).first()
        if not transport_area:
            return dict(code=400, msg='收运公司区域不存在.')
        if transport_area.clean_code:
            return dict(code=200, msg='排放登记编码已生成.', data=dict(
                clean_code=transport_area.clean_code,
                clean_no=transport_area.clean_no,
            ))

        # 1.1 获取区位编码
        region = CityRegion.objects.filter(coding=transport_area.area_coding, grade=2, is_deleted=0).first()
        if not region or not region.clean_code:
            return dict(code=400, msg='获取区位编码失败.')

        # 1.2 获取收运公司编码
        company = TransportCompany.objects.filter(transport_company_id=transport_area.transport_company_id,
                                                  is_declare=1,
                                                  is_deleted=0).first()
        if not company or not company.clean_code:
            return dict(code=400, msg='获取收运公司编码失败.')

        clean_code_prefix = f'{region.clean_code}{company.clean_code}'

        # 2. 锁定编码发号器
        with cache.lock(self.CLEAN_CODE_LOCK_KEY):
            # 2.2 发号逻辑
            try:
                with transaction.atomic(using='ljfl_db'):
                    with transaction.atomic(using='transport_db'):
                        clean_code, clean_no = self._get_clean_code(clean_code_prefix, clean_id, code_format='')

                        # 修改正式库
                        TransportCompanyArea.objects.filter(transport_company_area_id=clean_id,
                                                            is_deleted=0) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))

                        # 修改申报库
                        CompanyArea.objects.filter(transport_company_area_id=clean_id) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))


            except Exception as e:
                logger.exception(e)
                return dict(code=500, msg='排放登记编码发放失败.')

        return dict(code=200, msg='排放登记编码发放成功.', data=dict(
            clean_code=clean_code,
            clean_no=clean_no
        ))
