#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from .logger import logger

from .issuing.org_nonresident import OrgNonresidentCleanCodeGenerator
from .issuing.org_transport import OrgTransportCleanCodeGenerator
from .issuing.attach_car import AttachCarCleanCodeGenerator
from .issuing.org_transport_area import OrgTransportAreaCleanCodeGenerator
from .issuing.attach_factory import AttachFactoryCleanCodeGenerator
from .issuing.org_credit_no import OrgCreditNoGenerator


class CleanCodeIssuing:
    """排放登记编码发放"""

    def __init__(self):
        self.clean_type_sub_type_allow = {
            # 1. 主体编码
            # 1.1 居民
            # 1.2 非居民
            ('ORG', 'NONRESIDENT'): OrgNonresidentCleanCodeGenerator,
            # 1.3 收集单位
            # 1.4 收运单位
            ('ORG', 'TRANSPORT'): OrgTransportCleanCodeGenerator,
            ('ORG', 'TRANSPORT-AREA'): OrgTransportAreaCleanCodeGenerator,
            # 1.5 处理单位
            # 2. 附
            # 2.1 收集设施
            # 2.2 收运车辆
            ('ATTACH', 'CAR'): AttachCarCleanCodeGenerator,
            # 2.3 处理设施
            ('ATTACH', 'FACTORY'): AttachFactoryCleanCodeGenerator,
            # 2.4 其他

            # 3. 自定义
            # 3.1 主体虚拟统一社会信用代码生成
            ('ORG', 'CREDIT_NO'): OrgCreditNoGenerator,
        }

    def get_clean_code(self, clean_type, clean_sub_type, clean_id):
        """
        获取排放登记编码
        :param clean_type: 排放登记编码类型 ORG,ATTACH
        :param clean_sub_type: 排放登记编码子类型 RESIDENT,NONRESIDENT,COLLECT,TRANSPORT,TERMINAL,CAR
        :param clean_id: 排放登记编号对象id，由类型/子类型决定
        :return:
        """
        clean_key = (clean_type.upper(), clean_sub_type.upper())
        if clean_key not in self.clean_type_sub_type_allow.keys():
            return dict(code=400, msg='请提供正确的排放登记编码类型.')

        clean_class = self.clean_type_sub_type_allow.get(clean_key)
        result = clean_class().generate(clean_id)
        logger.info('CLEAN_TYPE:%s CLEAN_SUBTYPE:%s CLEAN_ID:%s RESPONSE:%s',
                    clean_type, clean_sub_type, clean_id, result)
        return result

    def get_other_clean_code(self, clean_type, clean_sub_type, clean_id):
        """
        开发区使用
        获取排放登记编码
        :param clean_type: 排放登记编码类型 ORG,ATTACH
        :param clean_sub_type: 排放登记编码子类型 RESIDENT,NONRESIDENT,COLLECT,TRANSPORT,TERMINAL,CAR
        :param clean_id: 排放登记编号对象id，由类型/子类型决定
        :return:
        """
        clean_key = (clean_type.upper(), clean_sub_type.upper())
        if clean_key not in self.clean_type_sub_type_allow.keys():
            return dict(code=400, msg='请提供正确的排放登记编码类型.')

        clean_class = self.clean_type_sub_type_allow.get(clean_key)
        result = clean_class().other_generate(clean_id)
        logger.info('开发区 CLEAN_TYPE:%s CLEAN_SUBTYPE:%s CLEAN_ID:%s RESPONSE:%s',
                    clean_type, clean_sub_type, clean_id, result)
        return result
