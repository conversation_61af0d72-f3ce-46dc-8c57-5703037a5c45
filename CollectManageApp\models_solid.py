from django.db import models
from django.utils import timezone


#固废扫码记录
class SolidTerminalRecord(models.Model):
    name=models.CharField(max_length=50,default="",verbose_name=('名称'),)
    creator = models.CharField(max_length=20,null=True, blank=True,verbose_name=('创建人名称(选填)'))
    create_time = models.DateTimeField(default=timezone.now,verbose_name=('创建时间'))
    update_time = models.DateTimeField(default=timezone.now,)#重写更新时间
    recorder = models.CharField(max_length=32,null=True, blank=True,verbose_name=('记录者名称(选填)'))  #记录者名称
    recorder_id = models.IntegerField(null=True, blank=True,verbose_name=('记录者ID')) #记录者ID
    rubbish_type = models.IntegerField(null=True, blank=True,verbose_name=('垃圾类型ID(必填)'))    #垃圾类型
    org_id = models.IntegerField(null=True,blank=True,)
    org_name=models.CharField(max_length=200,blank=True,null=True,verbose_name=('主体名称'))
    #车牌号
    car_id = models.CharField(max_length=200, blank=True, null=True, verbose_name=('车牌号'))
    #记录类型 包括现场有标签和现场只用二维码记录(0代表使用扫标签方式。1代表使用扫二维码方式)
    record_type = models.IntegerField(null=True, blank=True, verbose_name=('记录类型'))
    # 小数点后两位
    weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name=('重量'))
    #质量判定后的weight
    weight2 = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name=('重量'))
    #垃圾桶尺寸标志(数量)
    can_size240=models.IntegerField(null=True, blank=True, verbose_name=('垃圾桶尺寸'))
    can_size120=models.IntegerField(null=True, blank=True, verbose_name=('垃圾桶尺寸'))
    #增加30L桶数
    can_size30=models.IntegerField(null=True, blank=True, verbose_name=('垃圾桶尺寸'))
    #质量判定
    quality=models.CharField(max_length=200, blank=True, null=True, verbose_name=('质量判定'))
    # 质量判定编码  1234  优良中差
    quality_code = models.IntegerField(null=True, blank=True, verbose_name=('质量判定编码'))
    #图片列表
    pic_list=models.CharField(max_length=255, blank=True, null=True, verbose_name=('图片列表'))
    longitude = models.FloatField(null=True, blank=True, verbose_name=('经度(选填)'))
    latitude = models.FloatField(null=True, blank=True, verbose_name=('纬度(选填)'))
    source_zone = models.BigIntegerField(null=True, blank=True, verbose_name=(u'12位行政编码(必填)'))
    address=models.CharField(max_length=200, blank=True, null=True, verbose_name=('手持机上传地址'))
    #增加记录类别是正常收运还是预约订单收运 0正常收运，1预约收运
    collect_type=models.IntegerField(default=0 ,verbose_name=('收集类型'))
    #增加司机对垃圾的描述
    detail=models.CharField(max_length=200, blank=True, null=True, verbose_name=('垃圾的描述'))
    #增加小程序端对本次收运的确认状态   0代表新的收运记录  1 满意  2申诉中  3申诉驳回 4申诉已处理
    complain_status=models.IntegerField(default=0 ,verbose_name=('申诉类型'))
    # #增加拒收状态 0代表全部收运，1代表部分拒收 2 代表全部拒收
    refuse_status=models.IntegerField(default=0 ,verbose_name=('拒收类型'))
    appeal_quality = models.CharField(max_length=200, blank=True, null=True, verbose_name=('申诉页面中质量判定'))
    appeal_weight = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, verbose_name=('申诉页面中修正重量'))
    bid = models.CharField(default='',max_length=45, null=True, blank=True,verbose_name=(u'基础库主体id '))

    def __str__(self):
        return self.car_id

    class Meta:
        managed = False
        db_table = 'terminal_record'
        app_label = 'solid'


#固废主体
class SolidOrganization(models.Model):
    create_time = models.DateTimeField(default=timezone.now,verbose_name=('创建时间'))
    update_time = models.DateTimeField(auto_now=True)
    name = models.CharField(max_length=50,default="",verbose_name=('名称'),)
    coding = models.BigIntegerField(null=True, blank=True,verbose_name=(u'12位行政编码(必填)'))
    # 主体名称
    org_name = models.CharField(max_length=255, null=True, blank=True,verbose_name=(u'主体名称'))
    #基础库主体id
    bid = models.CharField(default='',max_length=45, null=True, blank=True,verbose_name=(u'基础库主体id '))
    #主体地址
    address = models.CharField(max_length=255, null=True, blank=True,verbose_name=(u'主体地址'))
    #区名称
    area_name = models.CharField(max_length=100, null=True, blank=True,verbose_name=(u'区名称'))
    # 街道名称
    street_name = models.CharField(max_length=100, null=True, blank=True,verbose_name=(u'街道名称'))
    #负责人
    manager = models.CharField(max_length=200, null=True, blank=True,verbose_name=(u'负责人'))
    phone = models.CharField(max_length=200, null=True, blank=True)
    org_type = models.IntegerField(null=True,blank=True,verbose_name=(u'主体类型id'))
    remark = models.CharField(max_length=200, null=True, blank=True, verbose_name=(u'备用'))
    # 经度
    longitude = models.FloatField(null=True,blank=True,verbose_name=('经度(选填)'))
    # 纬度
    latitude = models.FloatField(null=True,blank=True,verbose_name=('纬度(选填)'))
    #二维码地址
    qr_code=models.CharField(max_length=255, null=True, blank=True, verbose_name='二维码地址')
    #二维码的uuid字符串
    qr_content = models.CharField(max_length=32, null=True, blank=True, verbose_name='二维码内容')
    #主体状态  默认1  停用0
    org_status=models.IntegerField(default=1,verbose_name=('主体状态'))
    #is_delete  0没有删除，1已经删除
    is_delete=models.IntegerField(default=0,verbose_name=('删除状态'))
    #合同编号
    con_num=models.CharField(max_length=200, null=True, blank=True, verbose_name='合同编号')
    #收运企业
    trans_com=models.CharField(max_length=200, null=True, blank=True, verbose_name='收运企业')
    #主体使用面积
    scope=models.CharField(max_length=20, null=True, blank=True, verbose_name='主体使用面积')
    #细分类
    detai_class=models.CharField(max_length=200, null=True, blank=True, verbose_name='细分类')
    #对接负责人
    third_person=models.CharField(max_length=200, null=True, blank=True, verbose_name='对接负责人')
    #对接负责人电话
    third_phone = models.CharField(max_length=200, null=True, blank=True)
    #以下是新增字段 保持和基础路一致
    #统一信用代码
    credit_code=models.CharField(max_length=45, null=True, blank=True,default='')
    #食品经营许可证
    permission_code=models.CharField(max_length=255, null=True, blank=True,default='')
    #管理责任人
    liabler=models.CharField(max_length=255, null=True, blank=True,default='')
    #排放登记代码
    clean_no=models.CharField(max_length=255, null=True, blank=True,default='')
    #营业面积（m²）
    floor_area=models.CharField(max_length=56, null=True, blank=True,default='')
    #日就餐规模(人)
    restaurant_dining=models.IntegerField(null=True, blank=True,default=0)
    #经营形式
    mam_type=models.CharField(max_length=255, null=True, blank=True,default='')
    #经营形式子类
    mam_subtype=models.CharField(max_length=255, null=True, blank=True,default='')
    #责任主体类型
    org_type_id=models.CharField(max_length=45, null=True, blank=True,default='')
    #主体类型细类
    org_sub_type_id=models.CharField(max_length=45, null=True, blank=True,default='')
    #单位注册地址
    official_address=models.CharField(max_length=255, null=True, blank=True,default='')
    #控水控杂设备套数
    reduce_water_device=models.CharField(max_length=56, null=True, blank=True,default='')
    #油水分离设备套数
    reduce_oil_device=models.CharField(max_length=56, null=True, blank=True,default='')
    #预估外运量（公斤）
    restaurant_predict_weight=models.IntegerField(null=True, blank=True,default=0)
    #120L餐厨垃圾桶数量
    restaurant_trash_120=models.IntegerField(null=True, blank=True,default=0)
    #240L餐厨垃圾桶数量
    restaurant_trash_240=models.IntegerField(null=True, blank=True,default=0)
    #银行账户
    bank_account=models.CharField(max_length=255, null=True, blank=True,default='')
    #是否是非居民数据
    non_resident=models.IntegerField(null=True, blank=True,default=0)

    def __str__(self):
        return self.name

    class Meta:
        managed = False
        db_table = 'organization'
        app_label = 'solid'


#固废的车
class SolidCar(models.Model):
    mac = models.CharField(max_length=32, unique=True,null=True, blank=True, verbose_name=('车牌号'))

    def __str__(self):
        return self.mac
    class Meta:
        db_table = 'car_list'
        app_label = 'solid'



#维修工单表
class RepairWorkingOrder(models.Model):
    working_order_id = models.CharField(max_length=45,default='',blank=True, null=True)
    car_num = models.CharField(max_length=45,default='',blank=True, null=True)
    contacts = models.CharField(max_length=256, blank=True, null=True)
    phone = models.CharField(max_length=256, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    #整改状态 默认状态是0:未整改；1:已经整改  工单明细中全部整改才算本条工单已经整改
    status = models.IntegerField(blank=True, null=True,default=0)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        db_table = 'repair_working_order'
        app_label = 'solid'


#工单详情
class WorkingRegulations(models.Model):
    working_order_id = models.CharField(max_length=45,default='',blank=True, null=True)
    regulation_id = models.CharField(max_length=45,default='',blank=True, null=True)
    regulations_type = models.CharField(max_length=45,default='',blank=True, null=True)
    #整改状态 默认状态是0:未整改；1:已经整改
    status = models.IntegerField(blank=True, null=True,default=0)
    car_num = models.CharField(max_length=45,default='',blank=True, null=True)
    cover = models.CharField(max_length=1000, blank=True, null=True)
    description = models.CharField(max_length=1000, blank=True, null=True)
    commit_time = models.IntegerField(blank=True, null=True,default=0)
    repaire_time = models.IntegerField(blank=True, null=True,default=0)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    remark = models.CharField(max_length=500, blank=True, null=True)

    class Meta:
        db_table = 'working_regulations'
        app_label = 'solid'

