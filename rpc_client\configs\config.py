# -*- coding: utf-8 -*-
import sys
import getpass
import socket
import os

sys.path.append('../')
from . import config_release, config_local, config_dev
from rpc_client.lib.utils_tools import toDict

#

environ = os.environ.get('ENVIRONMENT')

# 本地配置
# # environ = 'local'
# print("environ:", environ)
# hostname = socket.gethostname()
# if environ in ['local']:
#     configs = config_local.configs
# elif environ in ['test', '']:
#     configs = config_dev.configs
# else:
#     configs = config_release.configs


environment = 'testing_local'
configs = config_local.configs
hostname = socket.gethostname()
if hostname in ['core-business-0001', 'core-business-0002',
                'core-business-0003', 'Server-e6cede4f-d941-4d4c-becf-e2399a27b12e.novalocal']:
    environment = 'production'
    configs = config_release.configs
elif hostname in ['Server-61198fa2-11ab-4fd6-8c45-cd138a1534fe.novalocal']:
    environment = 'testing'
    configs = config_dev.configs

configs = toDict(configs)
