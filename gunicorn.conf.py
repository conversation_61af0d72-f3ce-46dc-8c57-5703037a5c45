import gevent
from gevent import monkey

gevent.monkey.patch_all()

import os
import logging
import logging.handlers
from logging.handlers import WatchedFileHandler
import multiprocessing
from multiprocessing import cpu_count

if not os.path.isdir('./log'):
    os.makedirs('./log')

bind = "0.0.0.0:8522"

# proc_name = 'ljfl_terminal'

workers = 4
worker_class = "gevent"
forworded_allow_ips = '*'
backlog = 1024
loglevel = 'info'
access_log_format = '%(t)s %(p)s %(h)s "%(r)s" %(s)s %(L)s %(b)s %(f)s" "%(a)s"'

keepalive = 6
timeout = 65
graceful_timeout = 10
worker_connections = 65535

accesslog = "./log/gunicorn_access.log"  # 访问日志文件
errorlog = "./log/gunicorn_error.log"  # 错误日志文件
