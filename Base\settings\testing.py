#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import *

DEBUG = False

SCHEDULER_SWITCH = True

EMAIL_ENABLE = True  # 邮件通知是否可用

DATABASES = {
    'default': {},
    'ljfl_declare_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_declare_db',
        'USER': 'ljfldb',
        'HOST': '***********',
        'PASSWORD': 'ljfl_db20201210',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'ljfldb',
        'HOST': '***********',
        'PASSWORD': 'ljfl_db20201210',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'ljfl_db_replica': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'ljfldb',
        'HOST': '***********',
        'PASSWORD': 'ljfl_db20201210',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'transport_db': {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': '***********',  # 测试
        'PORT': '3306',
        'USER': 'ljfldb',
        'PASSWORD': 'ljfl_db20201210',
        'NAME': 'transport_db',
        'OPTIONS': {'charset': 'utf8mb4'},
    },
    'solid': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'solid_logistics',
        'USER': 'solidlogistics',
        'PASSWORD': 'solidlogistics20200107',
        'HOST': '***********',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'},
    },
    "jfpt": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "bj_jfpt_admin",
        "USER": "ljfldb",
        "HOST": "***********",
        "PASSWORD": "ljfl_db20201210",
        "PORT": 3306,
    },
    "tidb_ljfl_db": {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'ljfldb',
        'HOST': '***********',
        'PASSWORD': 'ljfl_db20201210',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    "other_transport_db": {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': '***********',  # 测试
        'PORT': 3306,
        'USER': 'ljfldb',
        'PASSWORD': 'ljfl_db20201210',
        'NAME': 'other_transport_db',
        'OPTIONS': {'charset': 'utf8mb4'},
    },
    "bagbreak_db": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "bagbreak",
        "USER": "ljfldb",
        "PASSWORD": "ljfl_db20201210",
        "PORT": 3306,
        'HOST': '***********',
        "OPTIONS": {"charset": "utf8mb4"},
    }
}

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://***********:6379/15",
        'KEY_PREFIX': 'api:test:resident_server',
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
        },
        "TIMEOUT": None
    }
}

# 管理授权
AUTH_APPID = *********
AUTH_APPSECRET = 'jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7'
AUTH_HOST = '***********:8296'
# 基础服务
BASE_HOST = '***********:8290'
# OSS服务
OSS_HOST = 'filemanager.ztbory.com'
# 资质审核服务
QUALIFI_HOST = '***********:8520'

COMPANY_SYS_IP = 'http://***********:8520'

# 市级服务
CITY_IP = 'http://************:8094'

# 统一登陆服务
ACCOUNT_SERVER_URL = "http://*************:8581"


INTERNAL_NETWORKS = ["***********/24", "************"]
