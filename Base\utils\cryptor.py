from django.conf import settings
from gmssl import sm4


class SM4Cryptor:
    """
    国产加密算法： sm4加解密
    """

    def __init__(self):
        self.gm_sm4 = sm4.CryptSM4()
        self.key = bytes.fromhex(settings.GM_SM4_KEY)

    def encrypt(self, value):
        """
        国密sm4加密
        :param value: 待加密的字符串
        :return: sm4加密后的十六进制字符
        """
        gm_sm4 = self.gm_sm4
        gm_sm4.set_key(self.key, sm4.SM4_ENCRYPT)
        data_str = str(value)
        encrypt_value = gm_sm4.crypt_ecb(data_str.encode())
        return encrypt_value.hex()

    def decrypt(self, encrypt_value):
        """
        国密sm4解密
        :param encrypt_value: 待解密的十六进制字符
        :return: 原字符串
        """
        gm_sm4 = self.gm_sm4
        gm_sm4.set_key(self.key, sm4.SM4_DECRYPT)
        decrypt_value = gm_sm4.crypt_ecb(bytes.fromhex(encrypt_value))
        return decrypt_value.decode()


sm4_cryptor = SM4Cryptor()
