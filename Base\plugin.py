#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import rest_framework.fields
from django.utils.translation import gettext_lazy as _

rest_framework.fields.CharField.default_error_messages = {
    'invalid': _('不是有效的字符.'),
    'blank': _('不可为空.'),
    'max_length': _('不可超过{max_length}个字符.'),
    'min_length': _('不可少于{min_length}个字符.'),
}

rest_framework.fields.ChoiceField.default_error_messages = {
    'invalid_choice': _('"{input}" 不是一个有效的选项.')
}

rest_framework.fields.DecimalField.default_error_messages = {
    'invalid': _('需要提供有效的数字.'),
    'max_value': _('数值应小于或等于{max_value}.'),
    'min_value': _('数值应大于或等于{min_value}.'),
    'max_digits': _('最多保留{max_digits}位小数.'),
    'max_decimal_places': _('Ensure that there are no more than {max_decimal_places} decimal places.'),
    'max_whole_digits': _('Ensure that there are no more than {max_whole_digits} digits before the decimal point.'),
    'max_string_length': _('数值过大.')
}

rest_framework.fields.DateField.default_error_messages = {
    'invalid': _('错误的日期格式，例如: 2000-01-01.'),
    'datetime': _('应为日期，但是传递参数为时间.'),
}
