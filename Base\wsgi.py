"""
WSGI config for Base project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/2.1/howto/deployment/wsgi/
"""

import os
import socket

from django.core.wsgi import get_wsgi_application

environment = 'development'
hostname = socket.gethostname()
if hostname in ['core-business-0002',
                'core-business-0003', 'Server-e6cede4f-d941-4d4c-becf-e2399a27b12e.novalocal']:
    environment = 'production'
elif hostname in ['Server-61198fa2-11ab-4fd6-8c45-cd138a1534fe.novalocal','core-business-0001']:
    environment = 'testing'

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Base.settings.{}'.format(environment))

application = get_wsgi_application()
