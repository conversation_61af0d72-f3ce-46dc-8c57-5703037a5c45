#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import datetime
import time

from dateutil.relativedelta import relativedelta
from django.db.models import Q, <PERSON><PERSON>, <PERSON>, <PERSON>, Count, Case, When
from django.forms import model_to_dict
from rest_framework import serializers
from django.core.cache import cache
from django.conf import settings

from Base.api import get_logger
from Base.utils.base import get_current_year, desensitize
from Base.utils.const import BOTH_NONRESIDENT_RUBBISHES, ConstRubbishType, ConstWeightType, NONRESIDENT_RUBBISHES_NAME, \
    NonresidentRubbishes, RfidTypeIdEnum
from Base.utils.cryptor import sm4_cryptor
from Base.utils.tools import TimeFilterTools
from CollectManageApp.models import OrgNonresidentDeclare, AuthorizedOrgRelationId, NonresidentCharge, \
    MyNonresidentAppointmentCollection, NonresidentPayOrder, OtherOrgNonresidentD<PERSON>lare, OrgNonresidentQuota, \
    AppointmentRecycling, TrashCanAlarmRecord, OilWaterSeparationDeviceRecord, ResidentRecord, Resident
from CollectManageApp.models_base import CleaningPoint, Organization, PendingCompanyRegistration, TerminalRecord, \
    CarRecord, RubbishType, OrgType, CityRegion, \
    TransportCompany, Car, CarRecordOrg, CarFlowRecord, TerminalFactoryRecord, CarType, TransportCompanyArea, \
    OrgDetail, FactoryLocation, RfidType, OrgRfid, TransportContract, OpinionDetails, ApplyStationRecod, VersionModel, \
    AppealRecod, CarRecordFlow, CarBillOrg, AppealDetailRecod, NonResidentBase, OrgGroup, \
    OrganizationOther, CleaningPointRecord, CleaningPointTransRecord, CarState, CleaningPointRecordFlow
from CollectManageApp.models_transport import TransportUnit, ContractNew, PayConfig
from CollectManageApp.scripts import __area_coding_to_name__, car_filter_rubbishes, export_status
import json
from .models_other_transport import Company as OtherTransportCompany
from CollectManageApp.kewei_db_models import (
    TrashStationModel
)

logger = get_logger('django')


def get_cleaning_point_mapping(serializer):
    if not hasattr(serializer, "_cleaning_point_mapping"):
        objs = CleaningPoint.objects.all().only("cleaning_point_id", "name").values("cleaning_point_id", "name")
        serializer._driver_car_mapping = {i.get('cleaning_point_id'): i for i in objs}
    return serializer._driver_car_mapping


def get_transport_company_mapping(serializer):
    if not hasattr(serializer, 'transport_company_mapping'):
        try:
            items = iter(serializer.instance)
        except TypeError:
            items = [serializer.instance]
        car_nums = set(i.car_num for i in items)
        car_objs = Car.objects.filter(car_num__in=car_nums)
        car_num_company_id_dict = {i.car_num: i.transport_company_id for i in car_objs}
        transport_company_ids = car_num_company_id_dict.values()
        company_objs = TransportCompany.objects.filter(is_deleted=0, transport_company_id__in=transport_company_ids)
        company_mapping = {i.transport_company_id: i.company for i in company_objs}
        serializer.transport_company_mapping = {car_num: company_mapping.get(company_id, "") for car_num, company_id in
                                                car_num_company_id_dict.items()}
    return serializer.transport_company_mapping


def get_rubbish_mapping(serializer):
    if not hasattr(serializer, 'rubbish_mapping'):
        rubbish_queryset = RubbishType.objects.values('type_id', 'name').filter(is_deleted=0)
        serializer.rubbish_mapping = {obj['type_id']: obj['name'] for obj in rubbish_queryset}
    return serializer.rubbish_mapping


def get_org_mapping(serializer):
    if not hasattr(serializer, 'org_mapping'):
        try:
            items = iter(serializer.instance)
        except TypeError:
            items = [serializer.instance]
        org_ids = set(i.org_id for i in items)
        org_queryset = Organization.objects.filter(is_deleted=0, org_id__in=org_ids)
        serializer.org_mapping = {obj.org_id: obj for obj in org_queryset}
    return serializer.org_mapping


def get_rfid_type_mapping(serializer):
    if not hasattr(serializer, 'rfid_type_mapping'):
        try:
            items = iter(serializer.instance)
        except TypeError:
            items = [serializer.instance]
        rfid_type_ids = set(i.rfid_type_id for i in items)
        org_queryset = RfidType.objects.values('rfid_type_id', 'name').filter(is_deleted=0,
                                                                              rfid_type_id__in=rfid_type_ids)
        serializer.rfid_type_mapping = {obj['rfid_type_id']: obj['name'] for obj in org_queryset}
    return serializer.rfid_type_mapping


def get_region_mapping(self):
    if not hasattr(self, 'region_mapping'):
        region_codes = set()
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        for item in items:
            if hasattr(item, 'area_coding'):
                region_codes.add(item.area_coding)
            if hasattr(item, 'street_coding'):
                region_codes.add(item.street_coding)
            if hasattr(item, 'comm_coding'):
                region_codes.add(item.comm_coding)
        regions = CityRegion.objects.filter(coding__in=region_codes, is_deleted=0)
        self.region_mapping = {r.coding: r for r in regions}
    return self.region_mapping


def get_org_type_mapping(self):
    if not hasattr(self, 'org_type_mapping'):
        org_type_ids = set()
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        for item in items:
            if hasattr(item, 'org_type_id'):
                org_type_ids.add(item.org_type_id)
            if hasattr(item, 'org_sub_type_id'):
                org_type_ids.add(item.org_sub_type_id)

        org_type_qs = OrgType.objects.filter(org_type_id__in=org_type_ids, is_deleted=0)
        self.org_type_mapping = {r.org_type_id: r for r in org_type_qs}
    return self.org_type_mapping


# 通过org_id获取organizaiton的区域名称
def get_org_id_region_mapping(self):
    if not hasattr(self, 'org_id_region_mapping'):
        region_codes = set()
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]

        org_ids = set(i.org_id for i in items)
        org_qs = Organization.objects.filter(is_deleted=0, org_id__in=org_ids)

        for org in org_qs:
            if org.area_coding:
                region_codes.add(org.area_coding)
            if org.street_coding:
                region_codes.add(org.street_coding)
            if org.comm_coding:
                region_codes.add(org.comm_coding)
        regions = CityRegion.objects.filter(coding__in=region_codes, is_deleted=0)
        self.org_id_region_mapping = {r.coding: r for r in regions}
    return self.org_id_region_mapping


def org_group_mapping(self):
    if not hasattr(self, 'org_group_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        org_qs = OrgGroup.objects.filter(org_id__in=org_ids).exclude(org_group_id__in=org_ids).values_list('org_id',
                                                                                                           flat=True)
        self.org_group_mapping = list(org_qs)
    return self.org_group_mapping


def get_cleaning_point_flow_mapping(serializer):
    if not hasattr(serializer, "_cleaning_point_flow_mapping"):
        try:
            items = iter(serializer.instance)
        except TypeError:
            items = [serializer.instance]
        cleaning_point_record_ids = [i.cleaning_point_record_id for i in items]
        objs = CleaningPointRecordFlow.objects.filter(is_deleted=0,
                                                      cleaning_point_record_id__in=cleaning_point_record_ids)
        serializer._cleaning_point_flow_mapping = {i.cleaning_point_record_id: i for i in objs}
    return serializer._cleaning_point_flow_mapping


def org_rfid_list(self):
    if not hasattr(self, 'org_rfid_list'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        self.org_rfid_list = list(
            OrgRfid.objects.filter(org_id__in=org_ids, is_deleted=0).values_list('org_id', flat=True).distinct())
    return self.org_rfid_list


def nonresident_org_rfid_list(self):
    if not hasattr(self, 'nonresident_org_rfid_list'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)

        area_coding = self.context['request'].GET.get('area_coding')
        rubbishes = self.context['request'].GET.get('rubbishes')
        rfid_org_list = OrgRfid.objects.filter(
            Q(
                Q(type_id=ConstRubbishType.KITCHEN, rfid_type_id=RfidTypeIdEnum.KITCHEN)
                |
                Q(type_id=ConstRubbishType.OTHER,
                  rfid_type_id=RfidTypeIdEnum.OTHER)
            ),
            is_deleted=0, org_id__isnull=False,
            is_declare=1, logout_status=0,
            org_id__in=org_ids,

        ).exclude(org_id='').values_list('org_id', flat=True)
        if area_coding:
            rfid_org_list = rfid_org_list.filter(area_coding=area_coding)
        if rubbishes and rubbishes == NonresidentRubbishes.RESTAURANTS:
            rfid_org_list = rfid_org_list.filter(type_id=ConstRubbishType.KITCHEN,
                                                 rfid_type_id=RfidTypeIdEnum.KITCHEN)
        elif rubbishes and rubbishes == NonresidentRubbishes.OTHER:
            rfid_org_list = rfid_org_list.filter(type_id=ConstRubbishType.OTHER,
                                                 rfid_type_id=RfidTypeIdEnum.OTHER)
        elif rubbishes and rubbishes == NonresidentRubbishes.BOTH_NONRESIDENT_RUBBISHES:
            rfid_org_list_cancu = rfid_org_list.filter(
                Q(type_id=ConstRubbishType.KITCHEN, rfid_type_id=RfidTypeIdEnum.KITCHEN)
            )
            rfid_org_list_other = rfid_org_list.filter(
                Q(type_id=ConstRubbishType.OTHER, rfid_type_id=RfidTypeIdEnum.OTHER)
            )
            rfid_org_list = set(rfid_org_list_cancu) & set(rfid_org_list_other)
        rfid_org_list = set(rfid_org_list)
        self.nonresident_org_rfid_list = rfid_org_list
    return self.nonresident_org_rfid_list


def org_rfid_mapper(self):
    if not hasattr(self, 'org_rfid_mapper'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]

        org_ids = set(i.org_id for i in items)
        org_rfids = OrgRfid.objects.filter(
            org_id__in=org_ids, is_deleted=0, is_declare=1, logout_status=0).values('org_id', "type_id")
        self.org_rfid_mapper = {(o["org_id"] + o["type_id"]): True for o in org_rfids}

    return self.org_rfid_mapper


def get_org_rfid_mapping_by_time(self):
    if not hasattr(self, 'org_rfid_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        request = self.context['request']
        end_date = request.GET.get("end_date")
        qs = OrgRfid.objects.filter(org_id__in=org_ids, is_deleted=0)
        if end_date:
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d") + relativedelta(days=1)
            timeArray_de = time.strptime(str(end_date.date()), "%Y-%m-%d")
            timestamp_de = time.mktime(timeArray_de)
            qs = qs.filter(create_time__lt=timestamp_de)
        self.org_rfid_mapping = {i.get("org_id"): i.get("last_time") for i in
                                 qs.values('org_id').annotate(
                                     last_time=Max("create_time"))}
    return self.org_rfid_mapping


def get_waste_org_rfid_mapping_by_time(self):
    if not hasattr(self, 'waste_org_rfid_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        request = self.context['request']
        end_date = request.GET.get("end_date")
        qs = OrgRfid.objects.filter(org_id__in=org_ids, is_deleted=0)
        if end_date:
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d") + relativedelta(days=1)
            timeArray_de = time.strptime(str(end_date.date()), "%Y-%m-%d")
            timestamp_de = time.mktime(timeArray_de)
            qs = qs.filter(create_time__lt=timestamp_de)
        self.waste_org_rfid_mapping = {i.get("org_id") + i.get("rfid_type_id"): i.get("last_time") for i in
                                       qs.values('org_id', "rfid_type_id").annotate(
                                           last_time=Max("create_time"))}
    return self.waste_org_rfid_mapping


def get_waste_org_car_cleaning_record_mapping(self):
    if not hasattr(self, 'waste_org_car_cleaning_record_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        type_ids = ["b8c900bac02a11eaa8a9000c29d3cc31", "b84b760ec02a11eaa8a9000c29d3cc31"]
        car_qs = CarRecord.objects.filter(
            org_id__in=org_ids, is_deleted=0, type_id__in=type_ids).values("org_id", "type_id").annotate(
            last_time=Min("create_time"))
        cleaning_qs = CleaningPointRecord.objects.filter(
            org_id__in=org_ids, is_deleted=0, type_id__in=type_ids).values("org_id", "type_id").annotate(
            id=Min("id"))

        car_record_mapping = {i.get("org_id") + i.get("type_id"): 1 for i in car_qs}
        cleaning_record_mapping = {i.get("org_id") + i.get("type_id"): 1 for i in cleaning_qs}
        car_record_mapping.update(cleaning_record_mapping)

        self.waste_org_car_cleaning_record_mapping = car_record_mapping
    return self.waste_org_car_cleaning_record_mapping


def get_second_transport_company_mapping(self):
    if not hasattr(self, 'second_transport_company_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        transport_company_ids = set(i.transport_company_id for i in items)
        transport_company_qs = TransportCompany.objects.filter(transport_company_id__in=transport_company_ids)
        self.second_transport_company_mapping = {company.transport_company_id: company for company in
                                                 transport_company_qs}
    return self.second_transport_company_mapping


def get_non_resident_base_mapping(self, rubbishes=NonresidentRubbishes.RESTAURANTS):
    key = 'non_resident_base_mapping'
    if rubbishes == NonresidentRubbishes.OTHER:
        key = f'non_resident_base_mapping_{rubbishes}'
    if not hasattr(self, key):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        non_resident_base_qs = NonResidentBase.objects.filter(org_id__in=org_ids, rubbishes=rubbishes)

        non_resident_base_mapping = {non_resident.org_id: non_resident for non_resident in non_resident_base_qs}
        setattr(self, key, non_resident_base_mapping)
    return getattr(self, key)


def org_nonresident_declare_mapping(self):
    if not hasattr(self, 'nonresident_declare_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        non_resident_declare_qs = OrgNonresidentDeclare.objects.filter(check_type__in=[0, 1], status=2,
                                                                       org_id__in=org_ids)

        self.nonresident_declare_mapping = {non_resident.org_id: non_resident for non_resident in
                                            non_resident_declare_qs}
    return self.nonresident_declare_mapping


def get_contract_new_mapping(self, rubbishes=NonresidentRubbishes.RESTAURANTS):
    key = 'contract_new_mapping'
    if rubbishes == NonresidentRubbishes.OTHER:
        key = f'other_{key}'
    if not hasattr(self, key):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        contract_new_qs = ContractNew.objects.filter(
            org_id__in=org_ids,
            is_delete=0,
            status=3,
            rubbishes=rubbishes
        ).values(
            'contract_picture', 'contract_id', 'org_id', "update_time"
        )
        contract_new_mapping = {contract_new.get('org_id'): contract_new for contract_new in contract_new_qs}
        setattr(self, key, contract_new_mapping)
    return getattr(self, key)


def get_car_type_mapping(serializer):
    if not hasattr(serializer, "_car_type_mapping"):
        serializer._car_type_mapping = {c.car_type_id: c.name for c in CarType.objects.filter(is_deleted=0)}
    return serializer._car_type_mapping


def get_other_contract_new_mapping(self):
    return get_contract_new_mapping(self, NonresidentRubbishes.OTHER)


def get_contract_new_mapping_by_time(self, rubbishes=NonresidentRubbishes.RESTAURANTS):
    key = 'contract_new_mapping_by_time'
    if rubbishes == NonresidentRubbishes.OTHER:
        key = f'other_{key}'
    if not hasattr(self, key):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        request = self.context['request']
        end_date = request.GET.get("end_date")
        contract_new_qs = ContractNew.objects.filter(org_id__in=org_ids, is_delete=0, status=3, rubbishes=rubbishes)
        if end_date:
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d") + relativedelta(days=1)
            contract_new_qs = contract_new_qs.filter(create_time__lt=end_date)
        contract_new_qs = contract_new_qs.only(
            'org_id', 'contract_picture', 'create_time', 'contract_status', 'service_start_date', 'service_end_date'
        )
        contract_new_mapping_by_time = {contract_new.org_id: contract_new for contract_new in contract_new_qs}
        setattr(self, key, contract_new_mapping_by_time)
    return getattr(self, key)


def get_logout_type_mapping(self, rubbishes=NonresidentRubbishes.RESTAURANTS):
    key = 'logout_type_mapping'
    if rubbishes == NonresidentRubbishes.OTHER:
        key = f'other_{key}'
    if not hasattr(self, key):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)

        declare = OrgNonresidentDeclare.objects.filter(
            check_type__in=[2, 3, 4],
            status=2,
            org_id__in=org_ids,
            rubbishes=rubbishes
        ).values('org_id', 'audit_time', 'check_type')
        logout_type_mapping = {d.get('org_id'): d for d in declare}
        setattr(self, key, logout_type_mapping)
    return getattr(self, key)


def get_transport_contract_org_mapping(self):
    if not hasattr(self, 'transport_contract_org_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        transport_company_ids = set(i.transport_company_id for i in items)
        transport_company_qs = ContractNew.objects.filter(company_id__in=transport_company_ids,
                                                          rubbishes=NonresidentRubbishes.RESTAURANTS,
                                                          contract_status__in=(1, 4), is_delete=0)
        self.transport_contract_org_mapping = {str(company.company_id) + '_' + str(company.org_id): company
                                               for company in transport_company_qs}
    return self.transport_contract_org_mapping


def get_other_transport_contract_org_mapping(self):
    if not hasattr(self, 'other_transport_contract_org_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        transport_company_ids = set(i.other_transport_company_id for i in items)
        transport_company_qs = ContractNew.objects.filter(company_id__in=transport_company_ids,
                                                          rubbishes=NonresidentRubbishes.OTHER,
                                                          contract_status__in=(1, 4), is_delete=0)
        self.other_transport_contract_org_mapping = {str(company.company_id) + '_' + str(company.org_id): company
                                                     for company in transport_company_qs}
    return self.other_transport_contract_org_mapping


def get_org_detail_mapping(self):
    if not hasattr(self, 'org_detail_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        org_details = OrgDetail.objects.filter(org_id__in=org_ids, is_deleted=0).values('org_id', 'floor_area',
                                                                                        'loating')
        self.org_detail_mapping = {org_.get('org_id'): org_ for org_ in org_details}
    return self.org_detail_mapping


def get_org_has_car_record_mapping(self):
    if not hasattr(self, 'org_has_car_record_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        request = self.context['request']
        duration = request.GET.get("duration")
        qs = CarRecord.objects.using("tidb_ljfl_db").filter(is_deleted=0, org_id__in=org_ids)

        qs = TimeFilterTools.create_time_filter(request, qs, duration)
        self.org_has_car_record_mapping = {i.get("org_id"): i for i in
                                           qs.values("org_id").annotate(last_time=Max("create_time"),
                                                                        farthest_time=Min("create_time"))}
    return self.org_has_car_record_mapping


def other_org_nonresident_declare_mapping(self):
    if not hasattr(self, 'other_nonresident_declare_mapping'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        org_ids = set(i.org_id for i in items)
        non_resident_declare_qs = OtherOrgNonresidentDeclare.objects.filter(status=2, org_id__in=org_ids,
                                                                            check_type__in=[0, 1])

        self.other_nonresident_declare_mapping = {non_resident.org_id: non_resident for non_resident in
                                                  non_resident_declare_qs}
    return self.other_nonresident_declare_mapping


def get_car_record_flow_mapping(self):
    if not hasattr(self, '_car_record_flow_map'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        car_record_ids = [i.car_record_id for i in items]
        record_org = CarRecordFlow.objects.using("tidb_ljfl_db").filter(is_deleted=0,
                                                                        car_record_id__in=car_record_ids)
        factory_map = {i.factory_location_id: i for i in FactoryLocation.objects.filter(is_deleted=0)}
        self._car_record_flow_map = {}
        for obj in record_org:
            obj.factory_location_name = factory_map.get(obj.factory_location_id) or ""
            self._car_record_flow_map["obj.car_record_id"] = obj
    return self._car_record_flow_map


def get_cleaning_point_name_mapping(self):
    if not hasattr(self, '_cleaning_point_map'):
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        ids = [i.cleaning_point_id for i in items]
        objs = CleaningPoint.objects.using("tidb_ljfl_db").filter(cleaning_point_id__in=ids)
        self._cleaning_point_map = {obj.cleaning_point_id: obj for obj in objs}
    return self._cleaning_point_map


def get_car_gps_mapping(self):
    if not hasattr(self, '_car_gps_mapping'):
        request = self.context['request']
        try:
            items = iter(self.instance)
        except TypeError:
            items = [self.instance]
        ids = [i.car_num for i in items]
        objs = CarState.objects.filter(state_type=1, car_num__in=ids)
        duration = request.GET.get("duration")
        if duration:
            objs = TimeFilterTools.new_create_time_filter(request, objs, duration)
        gps_car_nums = set(objs.values_list("car_num", flat=True).distinct())
        self._car_gps_mapping = {car_num: 1 for car_num in gps_car_nums}
    return self._car_gps_mapping


def get_logout_type_name(declare_check_type):
    if declare_check_type == 2:
        logout_type = '非居民注销'
    elif declare_check_type == 3:
        logout_type = '区注销'
    elif declare_check_type == 4:
        logout_type = '街道注销'
    else:
        logout_type = ''
    return logout_type


class UserRegisterSerializer(serializers.Serializer):
    # username = serializers.CharField(min_length=4, max_length=20)
    # password = serializers.CharField(min_length=6, max_length=20)
    relation_id = serializers.CharField(max_length=32)


class CityRegionSer(serializers.ModelSerializer):
    class Meta:
        model = CityRegion
        # fields = '__all__'
        exclude = ['region_id', 'parent_id', ]


class TransportCompanySer(serializers.ModelSerializer):
    class Meta:
        model = CityRegion
        fields = '__all__'


class OrganizationSer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = '__all__'

    def get_area_name(self, obj):
        if not cache.get('area_name'):
            cache.set('area_name', CityRegion.objects.all(), 60 * 60)
        try:
            if obj:
                w = cache.get('area_name').get(coding=obj)
                serializer = CityRegionSer(w)
                return serializer.data['name']
        except Exception as e:
            return ''

    def get_street_name(self, obj):
        if not cache.get('street_name'):
            cache.set('street_name', CityRegion.objects.all(), 60 * 60)
        try:
            if obj:
                w = cache.get('street_name').get(coding=obj)
                serializer = CityRegionSer(w)
                return serializer.data['name']
        except Exception as e:
            return ''

    def get_org_sub_type_name(self, obj):
        if not cache.get('org_type_name'):
            cache.set('org_type_name', OrgType.objects.all(), 60 * 60)
        try:
            if obj:
                w = cache.get('org_type_name').get(org_type_id=obj['org_sub_type_id'])
                serializer = OrgTypeSer(w)
                return serializer.data['name']
        except Exception as e:
            print(e)
            return ''

    def get_transport_company_name(self, obj):
        if not cache.get('transport_company_name'):
            cache.set('transport_company_name', TransportCompany.objects.all(), 60 * 60)
        try:
            if obj:
                w = cache.get('transport_company_name').get(coding=obj)
                serializer = TransportCompanySer(w)
                return serializer.data['company']
        except Exception as e:
            return ''

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['area_name'] = self.get_area_name(obj=data['area_coding'])
        data['street_name'] = self.get_street_name(obj=data['street_coding'])
        data['org_sub_type_name'] = self.get_org_sub_type_name(obj=data)
        data['transport_company_name'] = self.get_transport_company_name(obj=data['transport_company_id'])
        # 返回处理之后的数据
        return data


class OrganizationforSearch(serializers.ModelSerializer):
    org_type_name = serializers.SerializerMethodField()

    def get_org_type_name(self, obj):
        org_type_name = ''
        if obj.org_type_id:
            org_type_item = OrgType.objects.filter(org_type_id=obj.org_type_id).values('name').first()
            if org_type_item:
                org_type_name = org_type_item['name']

        return org_type_name

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        region = get_region_mapping(self).get(obj.area_coding)
        return region.name if region else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        region = get_region_mapping(self).get(obj.street_coding)
        return region.name if region else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        region = get_region_mapping(self).get(obj.comm_coding)
        return region.name if region else ''

    class Meta:
        model = Organization
        fields = '__all__'


class RubbishTypeSer(serializers.ModelSerializer):
    class Meta:
        model = RubbishType
        fields = '__all__'


class OrgTypeSer(serializers.ModelSerializer):
    class Meta:
        model = OrgType
        fields = '__all__'


class TerminalRecordSer(serializers.ModelSerializer):
    class Meta:
        model = TerminalRecord
        fields = '__all__'

    # def get_weight(self, obj):
    #     scan_time = obj['scan_time']
    #     commit_time = obj['commit_time']
    #     # ds = datetime.datetime.strftime(scan_time, '%Y-%m-%d %H:%M:%S')
    #     # de = datetime.datetime.strftime(commit_time, '%Y-%m-%d %H:%M:%S')
    #     # # 将时间字符串转成时间撮
    #     # timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
    #     weight = CarRecord.objects.filter(
    #         create_time__gte=scan_time, create_time__lte=commit_time).aggregate(weight=Sum('weight'))
    #
    #     result = weight.get('weight', 0) or 0
    #     return result

    weight = serializers.SerializerMethodField()

    def get_weight(self, obj):
        result = obj.guess_weight
        return result

    type_name = serializers.SerializerMethodField()

    def get_type_name(self, obj):
        try:
            rubbish_name = RubbishType.objects.get(type_id=obj.type_id).name
        except Exception as e:
            rubbish_name = '未知'
        return rubbish_name

    org_sub_type_name = serializers.SerializerMethodField()

    def get_org_sub_type_name(self, obj):
        try:
            org_sub_type_name = OrgType.objects.get(org_type_id=obj.org_sub_type_id).name
        except Exception as e:
            org_sub_type_name = '未知'
        return org_sub_type_name

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        try:
            area_name = CityRegion.objects.get(coding=obj.area_coding).name
        except Exception as e:
            area_name = ''
        return area_name

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        try:
            street_name = CityRegion.objects.get(coding=obj.street_coding).name
        except Exception as e:
            street_name = ''
        return street_name

    # def to_representation(self, instance):
    #     data = super().to_representation(instance)
    #     data['area_name'] = self.get_area_name(obj=data['area_coding'])
    #     data['street_name'] = self.get_street_name(obj=data['street_coding'])
    #     data['weight'] = self.get_weight(obj=data)
    #     data['type_name'] = self.get_type_name(obj=data)
    #     data['org_sub_type_name'] = self.get_org_sub_type_name(obj=data)
    #     # 返回处理之后的数据
    #     return data


class OrgNonresidentDeclareSer(serializers.ModelSerializer):
    class Meta:
        model = OrgNonresidentDeclare
        fields = '__all__'

    def __get_transport_company(self, transport_company_id):
        if not hasattr(self, '_transport_company_cache'):
            self._transport_company_cache = dict()
        company = self._transport_company_cache.get(transport_company_id)
        if not company:
            company = TransportCompany.objects.filter(transport_company_id=transport_company_id).first()
            if company:
                company = dict(name=company.company,
                               clean_code=company.clean_code,
                               admin=company.admin,
                               phone=desensitize(company.phone))
                self._transport_company_cache[transport_company_id] = company
        return company or dict(
            name='',
            admin='',
            phone='',
            clean_code=''
        )

    org_sub_type_name = serializers.SerializerMethodField()

    def get_org_sub_type_name(self, obj):
        org_sub_type_name = OrgType.objects.filter(org_type_id=obj.org_sub_type_id, is_deleted=0).first()
        return org_sub_type_name.name if org_sub_type_name else '未知'

    org_type_name = serializers.SerializerMethodField()

    def get_org_type_name(self, obj):
        org_type_name = OrgType.objects.filter(org_type_id=obj.org_type_id, is_deleted=0).first()
        return org_type_name.name if org_type_name else '未知'

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        area_name = CityRegion.objects.filter(coding=obj.area_coding, is_deleted=0).first()
        return area_name.name if area_name else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        street_name = CityRegion.objects.filter(coding=obj.street_coding, is_deleted=0).first()
        return street_name.name if street_name else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        comm_name = CityRegion.objects.filter(coding=obj.comm_coding, is_deleted=0).first()
        return comm_name.name if comm_name else ''

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        company = self.__get_transport_company(obj.transport_company_id)
        return company.get('name', '') if company else ''

    transport_company_clean_code = serializers.SerializerMethodField()

    def get_transport_company_clean_code(self, obj):
        return self.__get_transport_company(obj.transport_company_id).get('clean_code', '')

    transport_company_admin = serializers.SerializerMethodField()

    def get_transport_company_admin(self, obj):
        return self.__get_transport_company(obj.transport_company_id).get('admin', '')

    transport_company_phone = serializers.SerializerMethodField()

    def get_transport_company_phone(self, obj):
        return self.__get_transport_company(obj.transport_company_id).get('phone', '')

    is_admin = serializers.SerializerMethodField()

    def get_is_admin(self, obj):
        if 'request' in self.context and hasattr(self.context['request'], 'manager'):
            creator = self.context['request'].manager.get('relation_id')
            org_id = obj.org_id
            authorized_org = AuthorizedOrgRelationId.objects.filter(authorized_org_relation_id=creator,
                                                                    org_id=org_id).first()
            if authorized_org:
                return authorized_org.is_admin
            else:
                _org = AuthorizedOrgRelationId.objects.filter(org_id=org_id).first()
                return 0 if _org else 1
        return 0

    reson_str = serializers.SerializerMethodField()

    def get_reson_str(self, obj):
        reson_str = settings.RESON_DATA.get(obj.logout_reson, '')
        return reson_str

    check_type_str = serializers.SerializerMethodField()

    def get_check_type_str(self, obj):
        if obj.check_type == 0:
            check_type_str = '非居民单位注册信息'
        elif obj.check_type == 1:
            check_type_str = '非居民单位修改信息'
        elif obj.check_type == 2:
            check_type_str = '非居民单位注销信息'
        else:
            check_type_str = ''
        return check_type_str

    factory_location_data = serializers.SerializerMethodField()

    def get_factory_location_data(self, obj):
        try:
            if obj.transport_company_id and NonresidentRubbishes.RESTAURANTS in obj.rubbishes:
                factory = Car.objects.filter(standing_book=1,
                                             is_declare=1,
                                             area_coding=obj.area_coding,
                                             type_id__in=[ConstRubbishType.KITCHEN, ConstRubbishType.CHUYU],
                                             transport_company_id=obj.transport_company_id,
                                             ).values_list("factory_location_id", flat=True).distinct()
                factory_ids = []
                for item in factory:
                    factory_ids += item.split(",") if item else []
                factory_ids = set(factory_ids)
                factory_objs = FactoryLocation.objects.filter(is_deleted=0, factory_location_id__in=factory_ids).values(
                    'name', 'contacts', 'phone', 'clean_no')
                # for obj in factory_objs:
                #     obj["phone"] = desensitize(obj["phone"])
                return list(factory_objs)
            else:
                return []
        except Exception as e:
            logger.error(f"get_factory_location_data error:{e}")
            return []

    other_factory_location_data = serializers.SerializerMethodField()

    def get_other_factory_location_data(self, obj):
        """非居民其他垃圾直收直运车去向"""
        try:
            if obj.transport_company_id and NonresidentRubbishes.OTHER in obj.rubbishes:
                factory = Car.objects.filter(standing_book=1,
                                             is_declare=1,
                                             area_coding=obj.area_coding,
                                             type_id=ConstRubbishType.OTHER,
                                             transport_company_id=obj.transport_company_id,
                                             ).values_list("factory_location_id", flat=True).distinct()
                factory_ids = []
                for item in factory:
                    factory_ids += item.split(",") if item else []
                factory_ids = set(factory_ids)
                factory_objs = FactoryLocation.objects.filter(is_deleted=0, factory_location_id__in=factory_ids).values(
                    'name', 'contacts', 'phone', 'clean_no')
                # for obj in factory_objs:
                #     obj["phone"] = desensitize(obj["phone"])
                return list(factory_objs)
            else:
                return []
        except Exception as e:
            logger.error(f"get_other_factory_location_data error:{e}")
            return []

    def to_representation(self, obj):
        data = super().to_representation(obj)
        data["phone"] = desensitize(obj.phone)
        return data


class TerminalRecordSerializers(serializers.ModelSerializer):
    class Meta:
        model = TerminalRecord
        fields = '__all__'

    rubbish_name = serializers.SerializerMethodField()

    def get_rubbish_name(self, obj):
        try:
            rubbish_name = RubbishType.objects.get(type_id=obj.type_id).name
        except Exception as e:
            rubbish_name = '未知'
        return rubbish_name

    org_sub_type_name = serializers.SerializerMethodField()

    def get_org_sub_type_name(self, obj):
        org_sub_type_name = OrgType.objects.filter(org_type_id=obj.org_sub_type_id, is_deleted=0).first()
        return org_sub_type_name.name if org_sub_type_name else '未知'

    org_type_name = serializers.SerializerMethodField()

    def get_org_type_name(self, obj):
        org_type_name = OrgType.objects.filter(org_type_id=obj.org_type_id, is_deleted=0).first()
        return org_type_name.name if org_type_name else '未知'

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        area_name = CityRegion.objects.filter(coding=obj.area_coding, is_deleted=0).first()
        return area_name.name if area_name else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        street_name = CityRegion.objects.filter(coding=obj.street_coding, is_deleted=0).first()
        return street_name.name if street_name else ''

    company = serializers.SerializerMethodField()

    def get_company(self, obj):
        car_num = obj.car_num
        car = Car.objects.filter(car_num=car_num).first()
        return car.company if car else ''

    contacts = serializers.SerializerMethodField()

    def get_contacts(self, obj):
        request = self.context['request']
        org_id = request.GET.get('org_id')
        org_nonresidet = OrgNonresidentDeclare.objects.filter(status=1, org_id=org_id).first()
        return org_nonresidet.contacts if org_nonresidet else ''

    # 确认状态
    confirm_status = serializers.SerializerMethodField()

    def get_confirm_status(self, obj):
        expiration_time = obj.expiration_time
        confirm_status = obj.confirm_status
        now_time = int(time.time())
        if confirm_status:
            return 1
        else:
            if expiration_time <= now_time:
                obj.confirm_status = 1
                obj.grade_score = 5
                obj.confirm_time = now_time
                obj.save()
                return 1
            else:
                return 0

    def to_representation(self, obj):
        data = super().to_representation(obj)
        start_time = obj.scan_time
        commit_time = obj.commit_time
        stream_number = obj.stream_number
        can_size120 = obj.can_size120
        can_size240 = obj.can_size240
        car_num = obj.car_num
        correction_weight = 0
        if stream_number:
            uuid_list = []
            all_bucket = can_size120 + can_size240
            for i in range(all_bucket):
                uuid_list.append(stream_number + '-' + str(i + 1))
            uuid_list.append(stream_number)
            car_record = CarRecordOrg.objects.filter(is_deleted=0, stream_number__in=uuid_list, car_num=car_num)
        else:
            car_record = CarRecordOrg.objects.filter(is_deleted=0, create_time__gte=start_time,
                                                     create_time__lte=commit_time,
                                                     car_num=car_num)
        weight_sum = car_record.aggregate(weight=Sum('weight'),
                                          modify_weight=Sum('modify_weight'),
                                          capacity_weight=Sum('capacity_weight'))

        record = car_record.values('stream_number',
                                   'modify_weight',
                                   'weight',
                                   'capacity_weight',
                                   'type_id',
                                   'quality',
                                   'create_time',
                                   'cover')

        # 车载称重
        data['modify_weight'] = round(weight_sum.get('modify_weight', 0) or 0, 2)
        data['capacity_weight'] = round(weight_sum.get('capacity_weight', 0) or 0, 2)
        data['record'] = record
        data['weight'] = round(weight_sum.get('weight'), 2)
        return data


class CarFlowRecordSerializers(serializers.ModelSerializer):
    class Meta:
        model = CarFlowRecord
        fields = '__all__'

    rubbish_name = serializers.SerializerMethodField()

    def get_rubbish_name(self, obj):
        try:
            rubbish_name = RubbishType.objects.get(type_id=obj.type_id).name
        except Exception as e:
            rubbish_name = '未知'
        return rubbish_name

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        area_name = CityRegion.objects.filter(coding=obj.area_coding, is_deleted=0).first()
        return area_name.name if area_name else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        street_name = CityRegion.objects.filter(coding=obj.street_coding, is_deleted=0).first()
        return street_name.name if street_name else ''

    company = serializers.SerializerMethodField()

    def get_company(self, obj):
        car_num = obj.car_num
        car = Car.objects.filter(car_num=car_num).first()
        return car.company if car else ''

    def to_representation(self, obj):
        data = super().to_representation(obj)
        start_time = obj.start_time
        finished_time = obj.finished_time
        car_flow_id = obj.car_flow_id
        car_num = obj.car_num
        terminal_reocrd = TerminalRecord.objects.filter(is_deleted=0, commit_status=1, confirm_status=1,
                                                        car_num=car_num,
                                                        car_flow_id=car_flow_id)
        if not terminal_reocrd.exists():
            terminal_reocrd = TerminalRecord.objects.filter(is_deleted=0, commit_status=1, confirm_status=1,
                                                            car_num=car_num,
                                                            scan_time__gte=start_time, scan_time__lt=finished_time)

        record = terminal_reocrd.values('stream_number', 'org_name', 'type_id', 'commit_time', 'scan_time',
                                        'can_size240', 'can_size120', 'car_num', 'publisher')
        driver = ''
        for row in record:
            correction_weight = 0
            start_time = row['scan_time']
            commit_time = row['commit_time']
            stream_number = row['stream_number']
            can_size120 = row['can_size120']
            can_size240 = row['can_size240']
            car_num = row['car_num']
            if stream_number:
                uuid_list = []
                all_bucket = can_size120 + can_size240
                for i in range(all_bucket):
                    uuid_list.append(stream_number + '-' + str(i + 1))
                uuid_list.append(stream_number)
                car_record = CarRecordOrg.objects.filter(is_deleted=0, stream_number__in=uuid_list, car_num=car_num)
            else:
                car_record = CarRecordOrg.objects.filter(is_deleted=0, create_time__gte=start_time,
                                                         create_time__lte=commit_time,
                                                         car_num=car_num)

            car_obj = car_record.first()
            quality = car_obj.quality if car_obj else '优'
            weight_sum = car_record.aggregate(weight=Sum('weight'),
                                              modify_weight=Sum('modify_weight'),
                                              capacity_weight=Sum('capacity_weight'))
            row['weight'] = round(weight_sum.get('weight', 0) or 0, 2)
            row['quality'] = quality
            row['modify_weight'] = round(weight_sum.get('modify_weight', 0) or 0, 2)
            row['capacity_weight'] = round(weight_sum.get('capacity_weight', 0) or 0, 2)
            row['create_time'] = row['commit_time']
            driver = row['publisher']
        data['record'] = record
        data['org_count'] = len(record)
        total_weight = 0
        total_can_size120 = 0
        total_can_size240 = 0
        for row in record:
            total_weight += row.get('weight', 0)
            total_can_size120 += row.get('can_size120', 0)
            total_can_size240 += row.get('can_size240', 0)

        data['total_weihgt'] = round(total_weight, 2)
        data['total_can_size120'] = total_can_size120
        data['total_can_size240'] = total_can_size240
        data['container_card'] = '001'
        data['driver'] = driver
        terminal_factory_record_id = obj.terminal_factory_record_id
        if terminal_factory_record_id:
            treminal_factory_location = TerminalFactoryRecord.objects.filter(is_deleted=0,
                                                                             terminal_factory_record_id=terminal_factory_record_id).first()
        else:
            treminal_factory_location = TerminalFactoryRecord.objects.all().none()
        data['weightmtime'] = treminal_factory_location.weightmtime if treminal_factory_location else ''
        data['mweight'] = treminal_factory_location.mweight if treminal_factory_location else 0
        data['weightptime'] = treminal_factory_location.weightptime if treminal_factory_location else ''
        data['pweight'] = treminal_factory_location.pweight if treminal_factory_location else 0
        data['deliveryunit'] = treminal_factory_location.deliveryunit if treminal_factory_location else ''
        data['weighbridge_personnel'] = ''
        data['receiving_area'] = ''
        return data


class TransportCompanySer(serializers.ModelSerializer):
    class Meta:
        model = TransportCompany
        fields = '__all__'

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        area_name = CityRegion.objects.filter(coding=obj.area_coding, is_deleted=0).first()
        return area_name.name if area_name else ''


class TransportCompanyAreaSer(serializers.ModelSerializer):
    class Meta:
        model = TransportCompanyArea
        fields = '__all__'


class CarSer(serializers.ModelSerializer):
    class Meta:
        model = Car
        fields = '__all__'


class CityCompanySer(serializers.ModelSerializer):
    area_name = serializers.SerializerMethodField()
    c_area_name = serializers.SerializerMethodField()
    street_name = serializers.SerializerMethodField()
    company_type_name = serializers.SerializerMethodField()
    car_count = serializers.SerializerMethodField()
    factory_count = serializers.SerializerMethodField()
    factory_info = serializers.SerializerMethodField()
    org_count = serializers.SerializerMethodField()
    area_clean_code = serializers.SerializerMethodField()
    cleaning_point_count = serializers.SerializerMethodField()

    def get_company_area_mapping(self):
        if not hasattr(self, 'company_area_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            transport_company_ids = set(i.transport_company_id for i in items)
            area_coding = self.context['request'].GET.get('area_coding')
            rubbishes = self.context['request'].GET.get('rubbishes')
            queryset = TransportCompanyArea.objects.filter(transport_company_id__in=transport_company_ids, status=3)
            if area_coding:
                queryset = queryset.filter(area_coding=area_coding)
            if rubbishes:
                queryset = queryset.filter(rubbishes=rubbishes)
            queryset = queryset.values('clean_code', 'transport_company_id')

            self.company_area_mapping = {transport.get('transport_company_id'): transport.get('clean_code')
                                         for transport in queryset}
        return self.company_area_mapping

    def get_area_clean_code(self, obj):
        company_area_mapping = self.get_company_area_mapping()
        clean_code = company_area_mapping.get(obj.transport_company_id)
        return clean_code

    def get_area_name(self, obj):
        area_coding = self.context['request'].GET.get('area_coding')
        rubbishes = self.context['request'].GET.get('rubbishes')
        queryset = TransportCompanyArea.objects.filter(transport_company_id=obj.transport_company_id, status=3)
        if area_coding:
            queryset = queryset.filter(area_coding=area_coding)
        if rubbishes:
            queryset = queryset.filter(rubbishes=rubbishes)
        area_name_list = queryset.values_list('area_coding', flat=True)

        area_name_set = set()
        for name in area_name_list:
            if name:
                area_name_set.add(__area_coding_to_name__.get(name))
        return ','.join(area_name_set)

    def get_c_area_name(self, obj):
        name = __area_coding_to_name__.get(obj.c_area_coding, '')
        return name

    def get_street_name(self, obj):
        area_coding = self.context['request'].GET.get('area_coding')
        rubbishes = self.context['request'].GET.get('rubbishes')
        queryset = TransportCompanyArea.objects.filter(transport_company_id=obj.transport_company_id, status=3)
        if area_coding:
            queryset = queryset.filter(area_coding=area_coding)
        if rubbishes:
            queryset = queryset.filter(rubbishes=rubbishes)
        street_name_list = queryset.values_list('street_name', flat=True)
        names = ','.join(list(street_name_list))
        return ','.join(list(set(names.split(','))))

    def get_company_type_name(self, obj):
        name = settings.COMPANY_TYPE.get(obj.company_type, '')
        return name

    def get_car(self, obj):
        area_coding = self.context['request'].GET.get('area_coding')
        rubbishes = self.context['request'].GET.get('rubbishes')

        # 市级车辆只考虑standing_book=1
        qs = Car.objects.filter(transport_company_id=obj.transport_company_id, standing_book=1, is_declare=1)
        if area_coding:
            qs = qs.filter(area_coding=area_coding)
        qs = car_filter_rubbishes(qs, rubbishes)
        car_list = qs.values_list('factory_location_id', flat=True)
        return car_list

    def get_car_count(self, obj):
        data = self.get_car(obj)
        return len(data)

    def get_factory_count(self, obj):
        data = self.get_car(obj)
        data_list = []
        for fact in data:
            if fact:
                facts = fact.split(',')
                data_list.extend(facts)
        return len(set(data_list))

    def get_org_count(self, obj):
        queryset = Organization.objects.filter(is_deleted=0, is_declare=1, logout_status=0)
        rubbishes = self.context['request'].GET.get('rubbishes')
        area_coding = self.context['request'].GET.get('area_coding')
        if rubbishes == NonresidentRubbishes.OTHER:
            queryset = queryset.filter(other_transport_company_id=obj.transport_company_id,
                                       rubbishes__in=[rubbishes, BOTH_NONRESIDENT_RUBBISHES])
        elif rubbishes == NonresidentRubbishes.RESTAURANTS:
            queryset = queryset.filter(transport_company_id=obj.transport_company_id,
                                       rubbishes__in=[rubbishes, BOTH_NONRESIDENT_RUBBISHES])
        else:
            queryset = queryset.filter(Q(transport_company_id=obj.transport_company_id) | Q(
                other_transport_company_id=obj.transport_company_id))
        queryset = queryset.filter(is_group_second=0)
        if area_coding:
            queryset = queryset.filter(area_coding=area_coding)
        return queryset.count()

    def get_cleaning_point_count(self, obj):
        """密闭式清洁站数量"""
        rubbishes = self.context['request'].GET.get('rubbishes')
        if rubbishes == NonresidentRubbishes.OTHER:
            # 其他垃圾才有密闭式清洁站
            area_coding = self.context['request'].GET.get('area_coding')
            queryset = CleaningPoint.objects.filter(transport_company_id=obj.transport_company_id)
            if area_coding:
                queryset = queryset.filter(area_coding=area_coding)
            return queryset.count()
        else:
            return 0

    def get_factory_info(self, obj):
        data = self.get_car(obj)
        data_list = []
        for fact in data:
            if fact:
                facts = fact.split(',')
                data_list.extend(facts)
        data_list = list(set(data_list))
        factory = FactoryLocation.objects.filter(factory_location_id__in=data_list). \
            values('name', 'phone', 'contacts', 'address', 'factory_location_id')

        return factory

    class Meta:
        model = TransportCompany
        fields = "__all__"


class TransportCompanyAreaDetailSer(serializers.ModelSerializer):
    """清运公司服务区域-公司详情"""

    area_name = serializers.SerializerMethodField()
    area_clean_code = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        return __area_coding_to_name__.get(obj.area_coding, '')

    def get_area_clean_code(self, obj):
        return obj.clean_code

    def to_representation(self, instance):
        data = super().to_representation(instance)
        company = get_second_transport_company_mapping(self).get(instance.transport_company_id)
        if not company:
            company_data = {}
        else:
            address = company.address
            if len(address) > 6:
                address = address[:6] + "*" * 6
            else:
                address = "*" * 6
            phone = desensitize(company.phone) if company.phone else ''
            company_data = {
                'c_area_coding': company.c_area_coding,
                'c_area_name': __area_coding_to_name__.get(company.c_area_coding, ''),
                'company_type_name': settings.COMPANY_TYPE.get(company.company_type, ''),
                'admin': company.admin[0] + "**" if company.admin else '',
                'company': company.company,
                'company_picture': company.company_picture,
                'email': company.email,
                'address': address,
                'license_number': company.license_number,
                'credit_code': company.credit_code,
                'manage_unit': company.manage_unit,
                'office_address': company.office_address,
                'phone': phone,
                'quali_file': company.quali_file,
                'receiving_unit': company.receiving_unit,
                'remark': company.remark,
                'source_area': company.source_area,
                'source_street': company.source_street,
                'sub_mchid': company.sub_mchid,
                'viald_time': company.viald_time,
                'clean_code': company.clean_code,
            }
        data.update(company_data)
        data['rubbishes_name'] = NONRESIDENT_RUBBISHES_NAME.get(data['rubbishes'], '')
        return data

    class Meta:
        model = TransportCompanyArea
        fields = "__all__"

    vehicle_count = serializers.SerializerMethodField()

    def get_vehicle_count(self, obj):
        request = self.context['request']
        area_code = request.GET.get("area_coding")
        if not area_code:
            return 0

        agg = Car.objects.filter(transport_company_id=obj.transport_company_id,
                                 is_deleted=0, standing_book=1, area_system=1, area_coding=area_code)
        return agg.count()

    def get_rubbishes_org_count(self, company_id, area_code, rubbishes):
        if not area_code or not company_id:
            return 0

        queryset = (Organization.objects.using("tidb_ljfl_db").
                    filter(is_deleted=0, is_declare=1, area_coding=area_code))
        queryset = queryset.filter(rubbishes__in=[rubbishes, BOTH_NONRESIDENT_RUBBISHES])
        queryset = queryset.filter(Q(transport_company_id=company_id) | Q(other_transport_company_id=company_id))

        declare_queryset = OrgNonresidentDeclare.objects.using("ljfl_declare_db").all()
        declare_queryset = declare_queryset.filter(rubbishes=rubbishes)
        org_id_list = declare_queryset.filter(check_type=3, status=1).values_list('org_id', flat=True)
        queryset = queryset.filter(logout_status=0).exclude(org_id__in=list(org_id_list))

        queryset = queryset.filter(is_group_second=0)

        return queryset.count()

    org_count = serializers.SerializerMethodField()

    def get_org_count(self, obj):
        # 统计 非居民台账数量

        request = self.context['request']
        area_code = request.GET.get("area_coding")
        company = request.GET.get("company")
        # 查具体清运单位才返回主体数
        if not area_code or not company:
            return 0

        other_count = self.get_rubbishes_org_count(obj.transport_company_id, area_code, NonresidentRubbishes.OTHER)
        restaurants_count = self.get_rubbishes_org_count(obj.transport_company_id, area_code,
                                                         NonresidentRubbishes.RESTAURANTS)

        return other_count + restaurants_count


class CompanyCarSer(serializers.ModelSerializer):
    car_type_name = serializers.SerializerMethodField()
    is_weight_name = serializers.SerializerMethodField()
    is_gps_name = serializers.SerializerMethodField()
    area_system_name = serializers.SerializerMethodField()
    uhf_device_name = serializers.SerializerMethodField()
    is_quality_name = serializers.SerializerMethodField()
    is_spray_name = serializers.SerializerMethodField()
    service_area_name = serializers.SerializerMethodField()
    transport_type_name = serializers.SerializerMethodField()
    rubbish_type_name = serializers.SerializerMethodField()
    com_clean_code = serializers.SerializerMethodField()
    com_license_number = serializers.SerializerMethodField()
    com_approval_author = serializers.SerializerMethodField()
    com_office_address = serializers.SerializerMethodField()
    com_company_type = serializers.SerializerMethodField()
    com_address = serializers.SerializerMethodField()
    com_phone = serializers.SerializerMethodField()
    com_admin = serializers.SerializerMethodField()
    factory_info = serializers.SerializerMethodField()
    more_area = serializers.SerializerMethodField()
    factory_count = serializers.SerializerMethodField()

    def get_car_type_name(self, obj):
        car_type_obj = CarType.objects.filter(is_deleted=0, car_type_id=obj.car_type_id).first()
        return car_type_obj.name if car_type_obj else ''

    def get_is_gps_name(self, obj):
        is_gps_name = export_status(obj.is_gps)
        return is_gps_name

    def get_area_system_name(self, obj):
        area_system_name = export_status(obj.area_system)
        return area_system_name

    def get_is_spray_name(self, obj):
        is_spray_name = export_status(obj.is_spray)
        return is_spray_name

    def get_uhf_device_name(self, obj):
        uhf_device_name = export_status(obj.uhf_device)
        return uhf_device_name

    def get_is_quality_name(self, obj):
        is_quality_name = export_status(obj.is_quality)
        return is_quality_name

    def get_is_weight_name(self, obj):
        is_weight_name = export_status(obj.is_weight)
        return is_weight_name

    def get_service_area_name(self, obj):
        area_obj = CityRegion.objects.filter(is_deleted=0, coding=obj.area_coding).first()
        return area_obj.name if area_obj else ''

    def get_transport_type_name(self, obj):
        service_area_name = settings.TRANSPORT_TYPE.get(obj.transport_type, '')
        return service_area_name

    def get_rubbish_type_name(self, obj):
        rubbish_obj = RubbishType.objects.filter(is_deleted=0, type_id=obj.type_id).first()
        return rubbish_obj.name if rubbish_obj else ''

    def company_obj(self, obj):
        company = TransportCompany.objects.filter(transport_company_id=obj.transport_company_id, is_deleted=0). \
            values('clean_code', 'approval_author', 'license_number', 'office_address', 'company_type',
                   'address', 'phone', 'admin')
        if company and company[0]:
            clean_code = company[0].get('clean_code', '')
            approval_author = company[0].get('approval_author', '')
            license_number = company[0].get('license_number', '')
            office_address = company[0].get('office_address', '')
            admin = company[0].get('admin', '')
            phone = company[0].get('phone', '')
            address = company[0].get('address', '')
            company_type = company[0].get('company_type', '')
            company_type_name = settings.COMPANY_TYPE.get(company_type, '')
        else:
            clean_code = ''
            approval_author = ''
            license_number = ''
            office_address = ''
            company_type_name = ''
            address = ''
            phone = ''
            admin = ''

        phone = desensitize(phone) if phone else ''
        admin = desensitize(admin) if admin else ''
        address = desensitize(address) if address else ''
        data = dict(clean_code=clean_code, approval_author=approval_author, license_number=license_number,
                    office_address=office_address, company_type=company_type_name, address=address,
                    phone=phone, admin=admin)
        return data

    def get_com_clean_code(self, obj):
        data = self.company_obj(obj)
        return data.get('clean_code')

    def get_com_approval_author(self, obj):
        data = self.company_obj(obj)
        return data.get('approval_author')

    def get_com_license_number(self, obj):
        data = self.company_obj(obj)
        return data.get('license_number')

    def get_com_office_address(self, obj):
        data = self.company_obj(obj)
        return data.get('office_address')

    def get_com_company_type(self, obj):
        data = self.company_obj(obj)
        return data.get('company_type')

    def get_com_address(self, obj):
        data = self.company_obj(obj)
        return data.get('address')

    def get_com_phone(self, obj):
        data = self.company_obj(obj)
        return data.get('phone')

    def get_com_admin(self, obj):
        data = self.company_obj(obj)
        return data.get('admin')

    def get_factory_info(self, obj):
        res_list = []
        if obj.factory_location_id:
            id_list = obj.factory_location_id.split(',')
            factory = FactoryLocation.objects.filter(factory_location_id__in=id_list). \
                values('name', 'phone', 'contacts', 'address', 'factory_location_id')
            for res in factory:
                res['is_beian'] = ''
                res['clean_code'] = ''
                res_list.append(res)
        return res_list

    def get_more_area(self, obj):
        return '否'

    def get_factory_count(self, obj):
        counts = 0
        if obj.factory_location_id:
            factory = obj.factory_location_id.split(',')
            counts = len(factory)
        return counts

    dest_cleaning_point_info = serializers.SerializerMethodField()

    def get_dest_cleaning_point_info(self, obj):
        res_list = []
        try:
            if obj.dest_cleaning_point_id:
                id_list = obj.dest_cleaning_point_id.split(',')
                mapping = get_cleaning_point_mapping(self)
                for cleaning_point_id in id_list:
                    if mapping.get(cleaning_point_id):
                        res_list.append(mapping.get(cleaning_point_id))
        except Exception as e:
            logger.error(f"dest_cleaning_point_info报错:{e}")
        return res_list

    class Meta:
        model = Car
        fields = "__all__"


class OrgNonresidentSer(serializers.ModelSerializer):
    org_sub_type_name = serializers.SerializerMethodField()

    def get_org_sub_type_name(self, obj):
        org_type = get_org_type_mapping(self).get(obj.org_sub_type_id)
        return org_type.name if org_type else ''

    org_type_name = serializers.SerializerMethodField()

    def get_org_type_name(self, obj):
        org_type = get_org_type_mapping(self).get(obj.org_type_id)
        return org_type.name if org_type else ''

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        region = get_region_mapping(self).get(obj.area_coding)
        return region.name if region else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        region = get_region_mapping(self).get(obj.street_coding)
        return region.name if region else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        region = get_region_mapping(self).get(obj.comm_coding)
        return region.name if region else ''

    def get_transport_contract(self, obj):
        transport_contract_org_mapping = get_transport_contract_org_mapping(self)
        transport_contract = transport_contract_org_mapping.get(str(obj.transport_company_id) + '_' + str(obj.org_id))
        if transport_contract:
            return transport_contract.contract_num or '', transport_contract.viald_time or ''
        else:
            return '', ''

    transport_contract_num = serializers.SerializerMethodField()

    def get_transport_contract_num(self, obj):
        contract_num, viald_time = self.get_transport_contract(obj)
        return contract_num

    transport_contract_viald_time = serializers.SerializerMethodField()

    def get_transport_contract_viald_time(self, obj):
        contract_num, viald_time = self.get_transport_contract(obj)
        return json.loads(viald_time) if viald_time else ['', '']

    def get_transport_company(self, obj):
        company = self.__get_transport_company(obj.transport_company_id)
        return company.get('company', ''), company.get('admin', ''), company.get('phone', ''), company.get('clean_code',
                                                                                                           '')

    def get_transport_company_name_mapping(self):
        if not hasattr(self, 'transport_company_name_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            transport_company_ids = set()
            for i in items:
                if i.transport_company_id:
                    transport_company_ids.add(i.transport_company_id)
                if i.other_transport_company_id:
                    transport_company_ids.add(i.other_transport_company_id)

            companys = TransportCompany.objects.filter(transport_company_id__in=transport_company_ids,
                                                       is_deleted=0).values(
                'transport_company_id', 'company', 'clean_code', 'admin', 'phone'
            )
            self.transport_company_name_mapping = {company.get('transport_company_id'): company for company in companys}
        return self.transport_company_name_mapping

    def __get_transport_company(self, transport_company_id):
        transport_company_name_mapping = self.get_transport_company_name_mapping()
        return transport_company_name_mapping.get(transport_company_id) or {'company': '', 'admin': '', 'phone': '',
                                                                            'clean_code': ''}

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return company

    transport_company_admin = serializers.SerializerMethodField()

    def get_transport_company_admin(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return admin

    transport_company_phone = serializers.SerializerMethodField()

    def get_transport_company_phone(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return desensitize(phone) if phone else ""

    transport_company_clean_code = serializers.SerializerMethodField()

    def get_transport_company_clean_code(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return clean_code

    contract_picture = serializers.SerializerMethodField()

    def get_contract_picture(self, obj):
        contract = get_contract_new_mapping(self).get(obj.org_id, "")
        return contract and contract.get('contract_picture') or ''

    def get_org_detail(self, obj):
        obj_detail = get_org_detail_mapping(self).get(obj.org_id)
        if obj_detail:
            floor_area = obj_detail.get('floor_area', '') or ''
            loating = obj_detail.get('loating', '') or ''
        else:
            floor_area = ''
            loating = ''
        return floor_area, loating

    floor_area = serializers.SerializerMethodField()

    def get_floor_area(self, obj):
        floor_area, loating = self.get_org_detail(obj)
        return floor_area

    loating = serializers.SerializerMethodField()

    def get_loating(self, obj):
        floor_area, loating = self.get_org_detail(obj)
        return loating

    factory_name = serializers.SerializerMethodField()

    def get_factory_name(self, obj):
        return ''

    contract_num = serializers.SerializerMethodField()

    def get_contract_num(self, obj):
        return ''

    # 乙方统一信用代码
    second_credit_code = serializers.SerializerMethodField()

    def get_second_credit_code(self, obj):
        second_transport_company_mapping = get_second_transport_company_mapping(self)
        transport_company = second_transport_company_mapping.get(obj.transport_company_id)
        return transport_company.credit_code if transport_company else ''

    # 乙方单位性质
    second_company_type = serializers.SerializerMethodField()

    def get_second_company_type(self, obj):
        second_transport_company_mapping = get_second_transport_company_mapping(self)
        transport_company = second_transport_company_mapping.get(obj.transport_company_id)
        return transport_company.get_company_type_str() if transport_company else ''

    # 非居民信息是否补全
    complete_base = serializers.SerializerMethodField()

    def get_complete_base(self, obj):
        complete_base_obj = get_non_resident_base_mapping(self).get(obj.org_id)
        if complete_base_obj:
            return '是'
        return '否'

    other_complete_base = serializers.SerializerMethodField()

    def get_other_complete_base(self, obj):
        complete_base_obj = get_non_resident_base_mapping(self, rubbishes=NonresidentRubbishes.OTHER).get(obj.org_id)
        if complete_base_obj:
            return '是'
        return '否'

    # 注册时间(初次审核通过)
    register_time = serializers.SerializerMethodField()

    def get_register_time(self, obj):
        if obj.create_time:
            return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.create_time))
        return ''

    # 是否签署过合同
    is_sign_contract = serializers.SerializerMethodField()

    def get_is_sign_contract(self, obj):
        contract_new = get_contract_new_mapping(self).get(obj.org_id)
        if contract_new:
            return '是'
        return '否'

    def get_org_declare_mapping(self, rubbishes=NonresidentRubbishes.RESTAURANTS):
        key = 'org_declare_mapping'
        if rubbishes == NonresidentRubbishes.OTHER:
            key = f'other_{key}'
        if not hasattr(self, key):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            org_ids = set(i.org_id for i in items)
            org_queryset = OrgNonresidentDeclare.objects.filter(
                org_id__in=org_ids,
                rubbishes=rubbishes,
            ).order_by('update_time')
            org_declare_mapping = {obj.org_id: obj for obj in org_queryset}
            setattr(self, key, org_declare_mapping)
        return getattr(self, key)

    def _get_org_status(self, declare_mapping, org_id):
        obj = declare_mapping.get(org_id)
        org_status = ''
        if obj and obj.status == 2:
            org_status = get_logout_type_name(obj.check_type)
            org_status = org_status or '正常'
        elif obj and obj.status == 3 and obj.check_type in (4, 3, 2):
            org_status = '正常'
        elif obj and obj.check_type == 4 and obj.status == 1:
            org_status = '街道注销待审核'
        else:
            org_status = '正常'
        return org_status

    def _get_logout_type(self, org_id, rubbishes=NonresidentRubbishes.RESTAURANTS):
        declare = get_logout_type_mapping(self, rubbishes).get(org_id)
        return get_logout_type_name(declare and declare.get('check_type') or -1)

    factory_location_data = serializers.SerializerMethodField()

    def get_factory_location_data(self, obj):
        if obj.transport_company_id:
            factory = Car.objects.filter(standing_book=1,
                                         is_declare=1,
                                         area_coding=obj.area_coding,
                                         type_id__in=[ConstRubbishType.KITCHEN, ConstRubbishType.CHUYU],
                                         transport_company_id=obj.transport_company_id,
                                         ).values_list("factory_location_id", flat=True).distinct()
            factory_ids = []
            for item in factory:
                factory_ids += item.split(",") if item else []
            factory_ids = set(factory_ids)
            factory_objs = FactoryLocation.objects.filter(is_deleted=0, factory_location_id__in=factory_ids).values(
                'name', 'contacts', 'phone', 'clean_no')
            # for obj in factory_objs:
            #     obj["phone"] = desensitize(obj["phone"])
            return list(factory_objs)
        else:
            return []

    other_factory_location_data = serializers.SerializerMethodField()

    def get_other_factory_location_data(self, obj):
        """非居民其他垃圾直收直运车去向"""
        if obj.other_transport_company_id and NonresidentRubbishes.OTHER in obj.rubbishes:
            factory = Car.objects.filter(standing_book=1,
                                         is_declare=1,
                                         area_coding=obj.area_coding,
                                         type_id=ConstRubbishType.OTHER,
                                         transport_company_id=obj.other_transport_company_id,
                                         ).values_list("factory_location_id", flat=True).distinct()
            factory_ids = []
            for item in factory:
                factory_ids += item.split(",") if item else []
            factory_ids = set(factory_ids)
            factory_objs = FactoryLocation.objects.filter(is_deleted=0, factory_location_id__in=factory_ids).values(
                'name', 'contacts', 'phone', 'clean_no')
            # for obj in factory_objs:
            #     obj["phone"] = desensitize(obj["phone"])
            return list(factory_objs)
        else:
            return []

    def get_other_transport_contract(self, obj):
        other_transport_contract_org_mapping = get_other_transport_contract_org_mapping(self)
        transport_contract = other_transport_contract_org_mapping.get(
            str(obj.other_transport_company_id) + '_' + str(obj.org_id)
        )
        if transport_contract:
            return transport_contract.contract_num or '', transport_contract.viald_time or ""
        else:
            return '', ""

    other_transport_contract_num = serializers.SerializerMethodField()

    def get_other_transport_contract_num(self, obj):
        contract_num, viald_time = self.get_other_transport_contract(obj)
        return contract_num

    other_transport_contract_viald_time = serializers.SerializerMethodField()

    def get_other_transport_contract_viald_time(self, obj):
        contract_num, viald_time = self.get_other_transport_contract(obj)
        return json.loads(viald_time) if viald_time else ['', '']

    def to_representation(self, instance):
        data = super().to_representation(instance)
        logout_type = self._get_logout_type(data['org_id'], NonresidentRubbishes.RESTAURANTS)
        data['logout_type'] = logout_type
        other_logout_type = self._get_logout_type(data['org_id'], NonresidentRubbishes.OTHER)
        data['other_logout_type'] = other_logout_type
        data['rubbishes_name'] = NONRESIDENT_RUBBISHES_NAME.get(data['rubbishes'], '')

        if instance.address:
            data['address'] = desensitize(instance.address)

        if instance.contacts:
            data['contacts'] = desensitize(instance.contacts)

        if instance.rubbishes in [
            NonresidentRubbishes.RESTAURANTS,
            NonresidentRubbishes.BOTH_NONRESIDENT_RUBBISHES
        ]:
            org_declare_mapping = self.get_org_declare_mapping()
        elif instance.rubbishes == NonresidentRubbishes.OTHER:
            org_declare_mapping = self.get_org_declare_mapping(NonresidentRubbishes.OTHER)
        else:
            org_declare_mapping = {}
        data['org_status'] = self._get_org_status(org_declare_mapping, instance.org_id)
        # data['phone'] = sm4_cryptor.encrypt(data.get("phone")) if data.get("phone") else ""

        # 其他标签识别码安装时间（最近一次安装时间）
        org_waste_rfid_mapping = get_waste_org_rfid_mapping_by_time(self)
        last_time = org_waste_rfid_mapping.get(data["org_id"] + "73db639d03bc4e5e9ff7b3c1654a8167")
        data['other_trash_rfid_lasttime'] = time.strftime("%Y-%m-%d %H:%M:%S",
                                                          time.localtime(last_time)) if last_time else ""

        # 餐厨标签识别码安装时间（最近一次安装时间）
        last_time = org_waste_rfid_mapping.get(data["org_id"] + "9991aedf2c36452b97e89e16ab49e096")
        data['restaurant_trash_rfid_lasttime'] = time.strftime("%Y-%m-%d %H:%M:%S",
                                                               time.localtime(last_time)) if last_time else ""

        car_cleaning_record_mapping = get_waste_org_car_cleaning_record_mapping(self)

        other = car_cleaning_record_mapping.get(data["org_id"] + "b8c900bac02a11eaa8a9000c29d3cc31")
        data["other_org_identification"] = "是" if other else "否"

        restaurant = car_cleaning_record_mapping.get(data["org_id"] + "b84b760ec02a11eaa8a9000c29d3cc31")
        data["restaurant_org_identification"] = "是" if restaurant else "否"

        return data

    def get_org_group_mapping(self):
        if not hasattr(self, 'org_group_mapping'):
            org_group = OrgGroup.objects.filter(status=1, logout_status=0).values_list('org_group_id', flat=True)
            self.org_group_mapping = org_group
        return self.org_group_mapping

    org_group = serializers.SerializerMethodField()

    def get_org_group(self, obj):
        rubbishes = self.context['request'].GET.get('rubbishes')
        value = ""
        map = self.get_group_org_rubbishes_mapping()
        if rubbishes == NonresidentRubbishes.RESTAURANTS and obj.org_id + "_" + rubbishes in map:
            value = "是"
        elif rubbishes == NonresidentRubbishes.OTHER and obj.org_id + "_" + obj.rubbishes in map:
            value = "是"
        else:
            if obj.org_id + "_" + NonresidentRubbishes.RESTAURANTS in map:
                value = "是"
            if obj.org_id + "_" + NonresidentRubbishes.OTHER in map:
                value = "是"
        return value

    def get_group_org_rubbishes_mapping(self):
        if not hasattr(self, 'group_org_rubbishes_mapping'):
            org_group = OrgGroup.objects.filter(status=1,
                                                logout_status=0
                                                ).values_list('org_group_id', "rubbishes")
            self.group_org_rubbishes_mapping = [item[0] + "_" + item[1] for item in org_group]
        return self.group_org_rubbishes_mapping

    second_org_rubbishes = serializers.SerializerMethodField()

    def get_second_org_rubbishes(self, obj):
        map = self.get_group_org_rubbishes_mapping()
        if obj.rubbishes == NonresidentRubbishes.RESTAURANTS and obj.org_id + "_" + obj.rubbishes in map:
            return NonresidentRubbishes.RESTAURANTS
        elif obj.rubbishes == NonresidentRubbishes.OTHER and obj.org_id + "_" + obj.rubbishes in map:
            return NonresidentRubbishes.OTHER
        elif obj.rubbishes == NonresidentRubbishes.BOTH_NONRESIDENT_RUBBISHES:
            rubbishes = []
            if obj.org_id + "_" + NonresidentRubbishes.RESTAURANTS in map:
                rubbishes.append(NonresidentRubbishes.RESTAURANTS)
            if obj.org_id + "_" + NonresidentRubbishes.OTHER in map:
                rubbishes.append(NonresidentRubbishes.OTHER)
            return ",".join(rubbishes)

    has_rfid = serializers.SerializerMethodField()

    def get_has_rfid(self, obj):
        has_rfid = '否'
        org_list = nonresident_org_rfid_list(self)
        if obj.org_id in org_list:
            has_rfid = '是'
        return has_rfid

    rfid_last_create_time = serializers.SerializerMethodField()

    def get_rfid_last_create_time(self, obj):
        last_time = ''
        org_rfid_mapping = get_org_rfid_mapping_by_time(self)
        last_time = org_rfid_mapping.get(obj.org_id)
        last_time = time.strftime("%Y-%m-%d", time.localtime(last_time)) if last_time else ""
        return last_time

    other_transport_company_name = serializers.SerializerMethodField()

    def get_other_transport_company_name(self, obj):
        company = self.__get_transport_company(obj.other_transport_company_id)
        return company.get('company', '') if company else ''

    other_transport_company_clean_code = serializers.SerializerMethodField()

    def get_other_transport_company_clean_code(self, obj):
        return self.__get_transport_company(obj.other_transport_company_id).get('clean_code', '')

    other_transport_company_admin = serializers.SerializerMethodField()

    def get_other_transport_company_admin(self, obj):
        return self.__get_transport_company(obj.other_transport_company_id).get('admin', '')

    other_transport_company_phone = serializers.SerializerMethodField()

    def get_other_transport_company_phone(self, obj):
        phone = self.__get_transport_company(obj.other_transport_company_id).get('phone', '')
        # return desensitize(phone) if phone else ""
        return phone

    other_contract_url = serializers.SerializerMethodField()

    def get_other_contract_url(self, obj):
        contract = get_other_contract_new_mapping(self).get(obj.org_id)

        return contract and contract.get('contract_picture') or ''

    # 是否签署过合同
    other_is_sign_contract = serializers.SerializerMethodField()

    def get_other_is_sign_contract(self, obj):
        contract_new = get_contract_new_mapping(self, rubbishes=NonresidentRubbishes.OTHER).get(obj.org_id)
        if contract_new:
            return '是'
        return '否'

    # 其他垃圾合同签署时间
    other_contract_sign_time = serializers.SerializerMethodField()

    def get_other_contract_sign_time(self, obj):
        contract_new = get_contract_new_mapping(self, rubbishes=NonresidentRubbishes.OTHER).get(obj.org_id)
        if contract_new and contract_new.get("update_time"):
            return contract_new.get("update_time").strftime("%Y-%m-%d %H:%M:%S")
        return ""

    # 餐厨垃圾合同签署时间
    restaurant_contract_sign_time = serializers.SerializerMethodField()

    def get_restaurant_contract_sign_time(self, obj):
        contract_new = get_contract_new_mapping(self, rubbishes=NonresidentRubbishes.RESTAURANTS).get(obj.org_id)
        if contract_new and contract_new.get("update_time"):
            return contract_new.get("update_time").strftime("%Y-%m-%d %H:%M:%S")
        return ""

    # 其他垃圾合同合同是否过期
    other_contract_exceed_status = serializers.SerializerMethodField()

    def get_other_contract_exceed_status(self, obj):
        _, valid_time = self.get_other_transport_contract(obj)
        contract_times = json.loads(valid_time) if valid_time else ['', '']
        if contract_times and len(contract_times) > 1 and contract_times[1]:
            if contract_times[1] > str(datetime.datetime.now().date()):
                return "否"
            return "是"

        return ""

    # 餐厨垃圾合同是否过期
    restaurant_contract_exceed_status = serializers.SerializerMethodField()

    def get_restaurant_contract_exceed_status(self, obj):
        _, valid_time = self.get_transport_contract(obj)
        contract_times = json.loads(valid_time) if valid_time else ['', '']
        if contract_times and len(contract_times) > 1 and contract_times[1]:
            if contract_times[1] < str(datetime.datetime.now().date()):
                return "是"
            return "否"

        return ""

    class Meta:
        model = Organization
        fields = '__all__'


class OrgNonresidentSimpleSer(serializers.ModelSerializer):
    org_sub_type_name = serializers.SerializerMethodField()

    def get_org_sub_type_name(self, obj):
        org_type = get_org_type_mapping(self).get(obj.org_sub_type_id)
        return org_type.name if org_type else ''

    org_type_name = serializers.SerializerMethodField()

    def get_org_type_name(self, obj):
        org_type = get_org_type_mapping(self).get(obj.org_type_id)
        return org_type.name if org_type else ''

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        region = get_region_mapping(self).get(obj.area_coding)
        return region.name if region else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        region = get_region_mapping(self).get(obj.street_coding)
        return region.name if region else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        region = get_region_mapping(self).get(obj.comm_coding)
        return region.name if region else ''

    def get_org_group_mapping(self):
        if not hasattr(self, 'org_group_mapping'):
            org_group = OrgGroup.objects.filter(status=1, logout_status=0).values_list('org_group_id', flat=True)
            self.org_group_mapping = org_group
        return self.org_group_mapping

    org_group = serializers.SerializerMethodField()

    def get_org_group(self, obj):
        rubbishes = self.context['request'].GET.get('rubbishes')
        value = ""
        map = self.get_group_org_rubbishes_mapping()
        if rubbishes == NonresidentRubbishes.RESTAURANTS and obj.org_id + "_" + rubbishes in map:
            value = "是"
        elif rubbishes == NonresidentRubbishes.OTHER and obj.org_id + "_" + obj.rubbishes in map:
            value = "是"
        else:
            if obj.org_id + "_" + NonresidentRubbishes.RESTAURANTS in map:
                value = "是"
            if obj.org_id + "_" + NonresidentRubbishes.OTHER in map:
                value = "是"
        return value

    def get_transport_company(self, obj):
        company = self.__get_transport_company(obj.transport_company_id)
        return company.get('company', ''), company.get('admin', ''), company.get('phone', ''), company.get('clean_code',
                                                                                                           '')

    def get_transport_company_name_mapping(self):
        if not hasattr(self, 'transport_company_name_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            transport_company_ids = set()
            for i in items:
                if i.transport_company_id:
                    transport_company_ids.add(i.transport_company_id)
                if i.other_transport_company_id:
                    transport_company_ids.add(i.other_transport_company_id)

            companys = TransportCompany.objects.filter(transport_company_id__in=transport_company_ids,
                                                       is_deleted=0).values(
                'transport_company_id', 'company', 'clean_code', 'admin', 'phone'
            )
            self.transport_company_name_mapping = {company.get('transport_company_id'): company for company in companys}
        return self.transport_company_name_mapping

    def __get_transport_company(self, transport_company_id):
        transport_company_name_mapping = self.get_transport_company_name_mapping()
        return transport_company_name_mapping.get(transport_company_id) or {'company': '', 'admin': '', 'phone': '',
                                                                            'clean_code': ''}

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return company

    other_transport_company_name = serializers.SerializerMethodField()

    def get_other_transport_company_name(self, obj):
        company = self.__get_transport_company(obj.other_transport_company_id)
        return company.get('company', '') if company else ''

    def get_group_org_rubbishes_mapping(self):
        if not hasattr(self, 'group_org_rubbishes_mapping'):
            org_group = OrgGroup.objects.filter(status=1,
                                                logout_status=0
                                                ).values_list('org_group_id', "rubbishes")
            self.group_org_rubbishes_mapping = [item[0] + "_" + item[1] for item in org_group]
        return self.group_org_rubbishes_mapping

    second_org_rubbishes = serializers.SerializerMethodField()

    def get_second_org_rubbishes(self, obj):
        map = self.get_group_org_rubbishes_mapping()
        if obj.rubbishes == NonresidentRubbishes.RESTAURANTS and obj.org_id + "_" + obj.rubbishes in map:
            return NonresidentRubbishes.RESTAURANTS
        elif obj.rubbishes == NonresidentRubbishes.OTHER and obj.org_id + "_" + obj.rubbishes in map:
            return NonresidentRubbishes.OTHER
        elif obj.rubbishes == NonresidentRubbishes.BOTH_NONRESIDENT_RUBBISHES:
            rubbishes = []
            if obj.org_id + "_" + NonresidentRubbishes.RESTAURANTS in map:
                rubbishes.append(NonresidentRubbishes.RESTAURANTS)
            if obj.org_id + "_" + NonresidentRubbishes.OTHER in map:
                rubbishes.append(NonresidentRubbishes.OTHER)
            return ",".join(rubbishes)

    def to_representation(self, obj):
        data = super().to_representation(obj)
        contacts = obj.contacts
        phone = obj.phone

        # data['address'] = desensitize(obj.address) if obj.address else ''
        # data['contacts'] = desensitize(contacts) if contacts else ''
        # data['phone'] = desensitize(phone) if phone else ''

        return data

    class Meta:
        model = Organization
        fields = '__all__'


class CityContractSer(serializers.ModelSerializer):
    clean_code = serializers.SerializerMethodField()

    def get_clean_code(self, obj):
        org = Organization.objects.filter(org_id=obj.org_id).first()
        return org.clean_code if org else ''

    clean_no = serializers.SerializerMethodField()

    def get_clean_no(self, obj):
        org = Organization.objects.filter(org_id=obj.org_id).first()
        return org.clean_no if org else ''

    # 服务期限
    service_period = serializers.SerializerMethodField()

    def get_service_period(self, obj):
        if obj.viald_time:
            t = json.loads(obj.viald_time)
            return f'{t[0]}-{t[1]}'
        else:
            return ''

    # 按时间计算得到的状态
    contract_new_status = serializers.SerializerMethodField()

    def get_contract_new_status(self, obj):
        if obj.contract_status == 1:
            return '正常'
        elif obj.contract_status == 2:
            return '过期'
        elif obj.contract_status == 3:
            return '作废'
        elif obj.contract_status == 4:
            return '顺延'
        else:
            return ''

    # 是否可查看详情
    can_see_detail = serializers.SerializerMethodField()

    def get_can_see_detail(self, obj):
        if TransportUnit.objects.using('transport_db').filter(company_id=obj.company_id).first():
            return True
        return False

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        org = get_org_mapping(self).get(obj.org_id)
        if not org:
            return ''
        region = get_org_id_region_mapping(self).get(org.comm_coding)
        return region.name if region else ''

    comm_coding = serializers.SerializerMethodField()

    def get_comm_coding(self, obj):
        org = get_org_mapping(self).get(obj.org_id)
        if not org:
            return ''
        return org.comm_coding

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        return __area_coding_to_name__.get(obj.service_area)

    rubbish_type_name = serializers.SerializerMethodField()

    def get_rubbish_type_name(self, obj):
        type_name = RubbishType.objects.filter(type_id=obj.rubbish_type).values_list('name', flat=True)
        return type_name[0] if type_name and type_name[0] else ''

    mam_type = serializers.SerializerMethodField()

    def get_mam_type(self, obj):
        org = get_org_mapping(self).get(obj.org_id)
        if not org:
            return ''
        return org.mam_type

    class Meta:
        # model = TransportContract
        model = ContractNew
        fields = '__all__'


class CarRecordSer(serializers.ModelSerializer):
    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        area_name = CityRegion.objects.filter(coding=obj.area_coding, is_deleted=0).first()
        return area_name.name if area_name else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        street_name = CityRegion.objects.filter(coding=obj.street_coding, is_deleted=0).first()
        return street_name.name if street_name else ''

    def get_org_info(self, obj):
        org = Organization.objects.filter(is_deleted=0, org_id=obj.org_id).values('name', 'clean_no', 'credit_code')
        name = org[0].get('name', '') if org and org[0] else ''
        clean_no = org[0].get('clean_no', '') if org and org[0] else ''
        credit_code = org[0].get('credit_code', '') if org and org[0] else ''
        return name, clean_no, credit_code

    org_name = serializers.SerializerMethodField()

    def get_org_name(self, obj):
        name, clean_no, credit_code = self.get_org_info(obj)
        return name

    clean_no = serializers.SerializerMethodField()

    def get_clean_no(self, obj):
        name, clean_no, credit_code = self.get_org_info(obj)
        return clean_no

    credit_code = serializers.SerializerMethodField()

    def get_credit_code(self, obj):
        name, clean_no, credit_code = self.get_org_info(obj)
        return credit_code

    rubbish_type_name = serializers.SerializerMethodField()

    def get_rubbish_type_name(self, obj):
        rubbish_obj = RubbishType.objects.filter(is_deleted=0, type_id=obj.type_id).first()
        return rubbish_obj.name if rubbish_obj else ''

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        transport_company_id = Car.objects.filter(is_deleted=0, car_num=obj.car_num). \
            values_list('transport_company_id', flat=True)
        company_id = transport_company_id[0] if transport_company_id and transport_company_id[0] else ''
        company_obj = TransportCompany.objects.filter(is_deleted=0, transport_company_id=company_id). \
            values_list('company', flat=True)
        company_name = company_obj[0] if company_obj and company_obj[0] else ''
        return company_name

    create_time_str = serializers.SerializerMethodField()

    def get_create_time_str(self, obj):
        time_ds = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.create_time))
        create_time = datetime.datetime.strptime(time_ds, "%Y-%m-%d %H:%M:%S")
        de = datetime.datetime.strftime(create_time, '%Y-%m-%d %H:%M:%S')
        return de

    weight_type_name = serializers.SerializerMethodField()

    def get_weight_type_name(self, obj):
        weight_type_name = ConstWeightType.get_name(obj.weight_type)
        return weight_type_name

    class Meta:
        model = CarRecordOrg
        fields = '__all__'


class OtherCarRecordSer(serializers.ModelSerializer):

    def get_org_mapping(self):
        if not hasattr(self, 'org_record_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            org_ids = set(i.org_id for i in items)
            org_queryset = Organization.objects.filter(is_deleted=0, org_id__in=org_ids)
            self.org_record_mapping = {obj.org_id: obj for obj in org_queryset}
        return self.org_record_mapping

    def get_record_flow_mapping(self):
        if not hasattr(self, 'record_org_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            car_record_ids = [i.car_record_id for i in items]
            record_org = CarRecordFlow.objects.using("tidb_ljfl_db").filter(is_deleted=0,
                                                                            car_record_id__in=car_record_ids)
            self.record_org_mapping = {obj.car_record_id: obj for obj in record_org}
        return self.record_org_mapping

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        area_name = get_region_mapping(self).get(obj.area_coding)
        return area_name.name if area_name else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        street_name = get_region_mapping(self).get(obj.street_coding)
        return street_name.name if street_name else ''

    def get_org_info(self, org_id):
        org = self.get_org_mapping().get(org_id)
        name = org.name if org else ""
        clean_no = org.clean_no if org else ""
        credit_code = org.credit_code if org else ""
        mam_type = org.mam_type if org else ""
        mam_subtype = org.mam_subtype if org else ""
        return name, clean_no, credit_code, mam_type, mam_subtype

    def get_record_org_info(self, car_record_id):
        record_org_mapping = self.get_record_flow_mapping()
        if record_org_mapping:
            record_org = record_org_mapping.get(car_record_id)
            return record_org
        else:
            return None

    rubbish_type_name = serializers.SerializerMethodField()

    def get_rubbish_type_name(self, obj):
        rubbish_obj = get_rubbish_mapping(self)
        return rubbish_obj.get(obj.type_id) if rubbish_obj else ''

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        transport_company_obj = get_transport_company_mapping(self)
        return transport_company_obj.get(obj.car_num) if transport_company_obj else ""

    create_time_str = serializers.SerializerMethodField()

    def get_create_time_str(self, obj):
        time_ds = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.create_time))
        create_time = datetime.datetime.strptime(time_ds, "%Y-%m-%d %H:%M:%S")
        de = datetime.datetime.strftime(create_time, '%Y-%m-%d %H:%M:%S')
        return de

    logout_status = serializers.SerializerMethodField()

    def get_logout_status(self, obj):
        org = get_org_mapping(self).get(obj.org_id)
        return org.logout_status if org else 0

    def to_representation(self, obj):
        data = super().to_representation(obj)
        record_org = self.get_record_org_info(obj.car_record_id)
        name, clean_no, credit_code, mam_type, mam_subtype = self.get_org_info(obj.org_id)
        data['org_name'] = name
        data['credit_code'] = credit_code
        data['clean_no'] = clean_no
        data['mam_type'] = mam_type
        data['mam_subtype'] = mam_subtype
        car_weight = record_org.car_weight if record_org and record_org.car_weight else 0.0
        data['modify_weight'] = 0 if obj.creator == "司机端补桶" else car_weight
        data['capacity_weight'] = record_org.capacity_weight if record_org and record_org.capacity_weight else 0.0
        data['bill_weight'] = record_org.bill_weight if record_org and record_org.bill_weight else 0.0
        data['car_weight'] = record_org.car_weight if record_org else 0
        data['size'] = record_org.size if record_org else ''
        weight_type = record_org.weight_type if record_org else 'CAR'
        data['weight_type_name'] = ConstWeightType.get_name(weight_type)
        return data

    class Meta:
        model = CarRecord
        fields = '__all__'


class OrgRfidSer(serializers.ModelSerializer):
    # 垃圾类型名称
    rubbish_name = serializers.SerializerMethodField()

    def get_rubbish_name(self, obj):
        return get_rubbish_mapping(self).get(obj.type_id, '')

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        region = get_region_mapping(self).get(obj.area_coding)
        return region.name if region else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        region = get_region_mapping(self).get(obj.street_coding)
        return region.name if region else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        region = get_region_mapping(self).get(obj.comm_coding)
        return region.name if region else ''

    rfid_type_name = serializers.SerializerMethodField()

    def get_rfid_type_name(self, obj):
        rfid_type_name = get_rfid_type_mapping(self).get(obj.rfid_type_id, '')
        return rfid_type_name

    tips = serializers.SerializerMethodField()

    def get_tips(self, obj):
        tips = ''
        org_group = org_group_mapping(self)
        if obj.org_id in org_group and obj.logout_status:
            tips = '主体是二级用户,且标签已被注销'
        elif obj.org_id in org_group:
            tips = '主体是二级用户,标签无效'
        elif obj.logout_status:
            tips = '标签已被注销'

        return tips

    def to_representation(self, obj):
        data = super().to_representation(obj)
        org_obj = get_org_mapping(self).get(obj.org_id)
        data['contacts'] = ''
        data['phone'] = ''
        data['address'] = ''
        data['official_org_name'] = ''
        data['name'] = ''
        data['cover'] = ''
        if org_obj:
            data['contacts'] = org_obj.contacts or ''
            data['phone'] = org_obj.phone or ''
            data['address'] = org_obj.address or ''
            data['official_org_name'] = org_obj.official_org_name or ''
            data['name'] = org_obj.name or ''
            data['cover'] = org_obj.cover or ''

        return data

    class Meta:
        model = OrgRfid
        fields = '__all__'


class OrgRfidSerList(serializers.ModelSerializer):
    # 垃圾类型名称
    rubbish_name = serializers.SerializerMethodField()

    def get_rubbish_name(self, obj):
        rubbish_id = obj.type_id
        if rubbish_id:
            try:
                rubbish_name = RubbishType.objects.get(type_id=rubbish_id).name
            except Exception as e:
                rubbish_name = ""
        else:
            rubbish_name = ""
        return rubbish_name

    # 主体名称
    org_name = serializers.SerializerMethodField()

    def get_org_name(self, obj):
        org_id = obj.org_id
        if org_id:
            try:
                org_name = Organization.objects.get(org_id=org_id).name
            except Exception as e:
                org_name = ""
        else:
            org_name = ""
        return org_name

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        try:
            area_name = CityRegion.objects.get(coding=obj.area_coding).name
        except Exception as e:
            area_name = ''
        return area_name

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        try:
            street_name = CityRegion.objects.get(coding=obj.street_coding).name
        except Exception as e:
            street_name = ''
        return street_name

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        try:
            comm_name = CityRegion.objects.get(coding=obj.comm_coding).name
        except Exception as e:
            comm_name = ''
        return comm_name

    rfid_type_name = serializers.SerializerMethodField()

    def get_rfid_type_name(self, obj):
        try:
            rfid_type_name = RfidType.objects.get(rfid_type_id=obj.rfid_type_id).name
        except  Exception as e:
            rfid_type_name = ''
        return rfid_type_name

    credit_code = serializers.SerializerMethodField()

    def get_credit_code(self, obj):
        org_id = obj.org_id
        if org_id:
            try:
                credit_code = Organization.objects.get(org_id=org_id).credit_code
            except Exception as e:
                credit_code = ""
        else:
            credit_code = ""
        return credit_code

    clean_code = serializers.SerializerMethodField()

    def get_clean_code(self, obj):
        org_id = obj.org_id
        if org_id:
            try:
                clean_code = Organization.objects.get(org_id=org_id).clean_code
            except Exception as e:
                clean_code = ""
        else:
            clean_code = ""
        return clean_code

    clean_no = serializers.SerializerMethodField()

    def get_clean_no(self, obj):
        org_id = obj.org_id
        if org_id:
            try:
                clean_no = Organization.objects.get(org_id=org_id).clean_no
            except Exception as e:
                clean_no = ""
        else:
            clean_no = ""
        return clean_no

    class Meta:
        model = OrgRfid
        fields = '__all__'


class RfidTypeSer(serializers.ModelSerializer):
    class Meta:
        model = RfidType
        fields = '__all__'


class OrganizationforRfid(serializers.ModelSerializer):
    area_name = serializers.SerializerMethodField()
    street_name = serializers.SerializerMethodField()
    comm_name = serializers.SerializerMethodField()
    transport_company = serializers.SerializerMethodField()
    restaurants_rfid = serializers.SerializerMethodField()
    other_rfid = serializers.SerializerMethodField()
    restaurant_trash = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        try:
            area_name = CityRegion.objects.get(coding=obj.area_coding).name
        except Exception as e:
            area_name = ''
        return area_name

    def get_street_name(self, obj):
        try:
            street_name = CityRegion.objects.get(coding=obj.street_coding).name
        except Exception as e:
            street_name = ''
        return street_name

    def get_comm_name(self, obj):
        try:
            comm_name = CityRegion.objects.get(coding=obj.comm_coding).name
        except Exception as e:
            comm_name = ''
        return comm_name

    def get_transport_company(self, obj):
        # 清运公司登录，或页面清运公司查询
        company_id = ""
        if hasattr(self, 'transport_company_id'):
            company_id = self.transport_company_id

        if not hasattr(self, '_company_mapping'):
            company_ids = set()
            for i in self.instance:
                if i.transport_company_id:
                    company_ids.add(i.transport_company_id)
                if i.other_transport_company_id:
                    company_ids.add(i.other_transport_company_id)

            companys = TransportCompany.objects.filter(
                is_deleted=0, transport_company_id__in=company_ids
            ).values('transport_company_id', 'company')

            self._company_mapping = {company['transport_company_id']: company['company'] for company in companys}

        if company_id:
            return self._company_mapping.get(company_id) or ""

        kitchen_name = self._company_mapping.get(obj.transport_company_id) or ""
        other_name = self._company_mapping.get(obj.other_transport_company_id) or ""

        restaurants_rfid = self.get_restaurants_rfid(obj)
        other_rfid = self.get_other_rfid(obj)
        company_name = ""
        if kitchen_name and restaurants_rfid == "未打签":
            company_name = kitchen_name
        if other_name and other_rfid == "未打签":
            company_name = company_name + ',' + other_name if company_name and other_name != company_name else other_name

        return company_name

    def get_restaurants_rfid(self, obj):
        if NonresidentRubbishes.RESTAURANTS not in obj.rubbishes:
            return "--"
        # company_id = ""
        # if hasattr(self, 'transport_company_id'):
        #     company_id = self.transport_company_id
        # if company_id and obj.transport_company_id and company_id != obj.transport_company_id:
        #     return '--'

        kitchen_rfid = org_rfid_mapper(self).get(obj.org_id + ConstRubbishType.KITCHEN) or False
        return "--" if kitchen_rfid else "未打签"

    def get_other_rfid(self, obj):
        if NonresidentRubbishes.OTHER not in obj.rubbishes:
            return "--"

        # company_id = ""
        # if hasattr(self, 'transport_company_id'):
        #     company_id = self.transport_company_id
        # if company_id and obj.other_transport_company_id and company_id != obj.other_transport_company_id:
        #     return '--'

        other_rfid = org_rfid_mapper(self).get(obj.org_id + ConstRubbishType.OTHER) or False
        return "--" if other_rfid else "未打签"

    def get_restaurant_trash(self, obj):
        # 未打签数量，前端取这个字段的

        type_id = ""
        if hasattr(self, 'type_id'):
            type_id = self.type_id

        # 已打签数量
        stats = OrgRfid.objects.filter(org_id=obj.org_id, is_deleted=0, is_declare=1, logout_status=0).aggregate(
            restaurant_qty=Count(Case(When(type_id=ConstRubbishType.KITCHEN, then="id"))),
            other_qty=Count(Case(When(type_id=ConstRubbishType.OTHER, then="id"))),
        )
        if NonresidentRubbishes.RESTAURANTS not in obj.rubbishes:
            restaurant_trash = 0
        else:
            restaurant_trash = obj.restaurant_trash - (stats.get("restaurant_qty") or 0)
        if NonresidentRubbishes.OTHER not in obj.rubbishes:
            other_trash = 0
        else:
            other_trash = obj.other_trash - (stats.get("other_qty") or 0)

        if type_id:
            if type_id == ConstRubbishType.OTHER:
                return other_trash if other_trash else 0
            elif type_id == ConstRubbishType.KITCHEN:
                return restaurant_trash if restaurant_trash else 0

        sum_rfid = 0
        restaurants_rfid = self.get_restaurants_rfid(obj)
        other_rfid = self.get_other_rfid(obj)
        if restaurants_rfid == "未打签" and other_rfid == "未打签":
            sum_rfid = restaurant_trash + other_trash
        elif restaurants_rfid == "未打签" and other_rfid != "未打签":
            sum_rfid = restaurant_trash
        elif restaurants_rfid != "未打签" and other_rfid == "未打签":
            sum_rfid = other_trash

        return sum_rfid if sum_rfid else 0

    class Meta:
        model = Organization
        fields = '__all__'


class ContractSer(serializers.ModelSerializer):
    class Meta:
        model = TransportContract
        fields = '__all__'


class OrganizationSearchSer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = ['org_id', 'name', 'official_org_name', 'clean_code', 'clean_no',
                  'org_sub_type_id', 'address', 'permission_code', 'cover',
                  'area_coding', 'street_coding', 'rubbishes']


class OpinionDetailsSer(serializers.ModelSerializer):
    # org_name = serializers.CharField()
    class Meta:
        model = OpinionDetails
        fields = '__all__'


class ApplyStationSer(serializers.ModelSerializer):
    type_name = serializers.SerializerMethodField()

    def get_type_name(self, obj):
        if obj.type_id == 'b84b760ec02a11eaa8a9000c29d3cc31':
            name = '餐厨垃圾'
        elif obj.type_id == 'b8c900bac02a11eaa8a9000c29d3cc31':
            name = '其他垃圾'
        else:
            name = ''
        return name

    def to_representation(self, instance):
        data = super().to_representation(instance)
        org = Organization.objects.filter(org_id=data['org_id']).values('name', 'area_coding', 'street_coding')
        name = org[0].get('name') if org and org[0] else ''
        area_coding = org[0].get('area_coding') if org and org[0] else ''
        street_coding = org[0].get('street_coding') if org and org[0] else ''
        area_name = CityRegion.objects.filter(coding=area_coding).values_list('name', flat=True)
        street_name = CityRegion.objects.filter(coding=street_coding).values_list('name', flat=True)
        area_name = area_name[0] if area_name and area_name[0] else ''
        street_name = street_name[0] if street_name and street_name[0] else ''
        data['area_name'] = area_name
        data['street_name'] = street_name
        data['org_name'] = name
        # 返回处理之后的数据
        return data

    class Meta:
        model = ApplyStationRecod
        fields = '__all__'


class ApplyOrgRfidSer(serializers.ModelSerializer):
    type_name = serializers.SerializerMethodField()
    create_time_str = serializers.SerializerMethodField()

    def get_type_name(self, obj):
        if obj.type_id == 'b84b760ec02a11eaa8a9000c29d3cc31':
            name = '餐厨垃圾'
        elif obj.type_id == 'b8c900bac02a11eaa8a9000c29d3cc31':
            name = '其他垃圾'
        else:
            name = ''
        return name

    def get_create_time_str(self, obj):
        time_ds = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.create_time))
        create_time = datetime.datetime.strptime(time_ds, "%Y-%m-%d %H:%M:%S")
        de = datetime.datetime.strftime(create_time, '%Y-%m-%d %H:%M:%S')
        return de

    class Meta:
        model = OrgRfid
        fields = '__all__'


class VersionModelSer(serializers.HyperlinkedModelSerializer):
    obj_id = serializers.IntegerField(source='id', read_only=True)

    class Meta:
        model = VersionModel
        fields = '__all__'


class AppealRecodSer(serializers.ModelSerializer):
    appeal_time_str = serializers.SerializerMethodField()
    re_appeal_time_str = serializers.SerializerMethodField()
    second_appeal = serializers.SerializerMethodField()
    collection_time = serializers.SerializerMethodField()
    appeal_status = serializers.SerializerMethodField()

    def get_appeal_time_str(self, obj):
        de = ''
        if obj.appeal_time:
            de = datetime.datetime.strftime(obj.appeal_time, '%Y-%m-%d %H:%M:%S')
        return de

    def get_re_appeal_time_str(self, obj):
        de = ''
        if obj.re_appeal_time:
            de = datetime.datetime.strftime(obj.re_appeal_time, '%Y-%m-%d %H:%M:%S')
        return de

    def get_second_appeal(self, obj):
        second = 1 if obj.status == 3 else 0
        return second

    def get_collection_time(self, obj):
        create_time = CarBillOrg.objects.filter(bill_factory_id=obj.bill_factory_id,
                                                bill_org_id=obj.bill_org_id, is_deleted=0). \
            values_list('start_time', flat=True)

        create_time = create_time[0] if create_time and create_time[0] else 0
        de = ''
        if create_time:
            time_ds = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(create_time))
            create_time = datetime.datetime.strptime(time_ds, "%Y-%m-%d %H:%M:%S")
            de = datetime.datetime.strftime(create_time, '%Y-%m-%d %H:%M:%S')
        return de

    def get_appeal_status(self, obj):
        if obj.status == 0 or obj.status == 1:
            appeal_status = '未处理'
        else:
            appeal_status = '已处理'
        return appeal_status

    def to_representation(self, instance):
        data = super().to_representation(instance)
        org = Organization.objects.filter(org_id=data['org_id']).values('name', 'area_coding', 'street_coding',
                                                                        'transport_company_id')
        name = org[0].get('name') if org and org[0] else ''
        area_coding = org[0].get('area_coding') if org and org[0] else ''
        street_coding = org[0].get('street_coding') if org and org[0] else ''
        transport_company_id = org[0].get('transport_company_id') if org and org[0] else ''
        area_name = CityRegion.objects.filter(coding=area_coding).values_list('name', flat=True)
        street_name = CityRegion.objects.filter(coding=street_coding).values_list('name', flat=True)
        area_name = area_name[0] if area_name and area_name[0] else ''
        street_name = street_name[0] if street_name and street_name[0] else ''
        company_name = TransportCompany.objects.filter(transport_company_id=transport_company_id). \
            values_list('company', flat=True)
        company_name = company_name[0] if company_name and company_name[0] else ''
        data['org_name'] = name
        data['area_name'] = area_name
        data['street_name'] = street_name
        data['company_name'] = company_name
        # 返回处理之后的数据
        return data

    class Meta:
        model = AppealRecod
        fields = '__all__'


class CarRecordFlowSer(serializers.ModelSerializer):
    def get_non_mapping(self):
        if not hasattr(self, 'org_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            org_ids = [i.org_id for i in items]
            org_queryset = Organization.objects.filter(is_deleted=0, org_id__in=org_ids)
            self.org_mapping = {obj.org_id: obj for obj in org_queryset}
        return self.org_mapping

    def _get_rubbish_name(self, type_id):
        if not hasattr(self, "_rubbish_mapping"):
            self._rubbish_mapping = {
                obj["type_id"]: obj["name"] for obj in RubbishType.objects.values("type_id", "name")
            }
        return self._rubbish_mapping.get(type_id)

    def get_car_record(self):
        if not hasattr(self, 'car_record_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            car_record_ids = set(i.car_record_id for i in items)
            record = CarRecord.objects.using("tidb_ljfl_db").filter(car_record_id__in=car_record_ids, is_deleted=0). \
                values('quality', 'rfid_num', 'create_time', 'car_record_id', 'remark', 'type_id')
            self.car_record_mapping = {obj.get('car_record_id'): obj for obj in record}
        return self.car_record_mapping

    def to_representation(self, instance):
        data = super().to_representation(instance)
        car_record_mapping = self.get_car_record()
        record = car_record_mapping.get(data['car_record_id'])
        quality = record.get('quality') if record else ''
        rfid_num = record.get('rfid_num') if record else ''
        create_time = record.get('create_time') if record else 0
        remark = record.get('remark') if record else ''
        type_id = record.get('type_id') if record else ''
        data['quality'] = quality or ''
        data['rfid_num'] = rfid_num or ''
        type_dict = {'0': '未知来源', '1': '司机端扫码或者选择主体', '2': '设备扫标签', '3': '管理人员画的主体电子围栏',
                     '4': '司机端补桶'}
        data['source'] = type_dict.get(remark) or ''
        data['type_name'] = self._get_rubbish_name(type_id)
        time_ds = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(create_time))
        create_time = datetime.datetime.strptime(time_ds, "%Y-%m-%d %H:%M:%S")
        de = datetime.datetime.strftime(create_time, '%Y-%m-%d %H:%M:%S')
        data['create_time'] = de
        org_map = self.get_non_mapping().get(data['org_id'])
        data['org_name'] = org_map.name if org_map else ''
        data['clean_no'] = org_map.clean_no if org_map else ''
        # 返回处理之后的数据
        return data

    class Meta:
        model = CarRecordFlow
        fields = '__all__'


class AppealDetailRecodSer(serializers.ModelSerializer):
    capacity_weight = serializers.SerializerMethodField()

    def get_capacity_weight(self, obj):
        flow = CarRecordFlow.objects.filter(car_record_id=obj.car_record_id).values_list('bill_weight', flat=True)
        return flow[0] if flow and flow[0] else 0

    def to_representation(self, instance):
        data = super().to_representation(instance)
        record = CarRecord.objects.filter(car_record_id=data['car_record_id'], is_deleted=0). \
            values('quality', 'rfid_num')
        quality = record[0].get('quality') if record and record[0] else ''
        rfid_num = record[0].get('rfid_num') if record and record[0] else ''
        data['quality'] = quality
        data['rfid_num'] = rfid_num

        appeal = AppealRecod.objects.filter(appeal_id=data['appeal_id'], is_deleted=0).values_list('status', flat=True)
        status = appeal[0] if appeal and appeal[0] else -1
        result, ls_result = '', ''
        if status == 0 or status == 1:
            if data['company_deal_status'] == 1:
                result = '通过'
            else:
                result = '未审批'
        elif status == 2:
            if data['company_deal_status'] == 1:
                result = '通过'
            elif data['company_deal_status'] == 2:
                result = '拒绝'
        elif status == 3:
            if data['company_deal_status'] == 1:
                ls_result = '通过'
            elif data['company_deal_status'] == 2:
                ls_result = '拒绝'
            if data['area_deal_status'] == 2:
                result = '通过'
            elif data['area_deal_status'] == 3:
                result = '拒绝'
        data['result'] = result
        data['ls_result'] = ls_result
        # 返回处理之后的数据
        return data

    class Meta:
        model = AppealDetailRecod
        fields = '__all__'


class AppealDealRecordSer(serializers.ModelSerializer):

    def to_representation(self, instance):
        data = super().to_representation(instance)
        record = CarRecord.objects.filter(car_record_id=data['car_record_id'], is_deleted=0). \
            values('quality', 'rfid_num')
        quality = record[0].get('quality') if record and record[0] else ''
        rfid_num = record[0].get('rfid_num') if record and record[0] else ''
        data['quality'] = quality
        data['rfid_num'] = rfid_num
        record = AppealDetailRecod.objects.filter(bill_factory_id=data['bill_factory_id'],
                                                  car_record_id=data['car_record_id']). \
            values('a_type', 'deal_weight', 'company_deal_status', 'company_confirmer', 'area_confirmer',
                   'area_deal_status', 'record_weight')
        if record and record[0]:
            a_type = record[0].get('a_type')
            if a_type == 0:
                type_name = '桶重不符'
            else:
                type_name = '主体不符'
            company_deal_status = record[0].get('company_deal_status')
            area_confirmer = record[0].get('area_confirmer')
            company_confirmer = record[0].get('company_confirmer')
            area_deal_status = record[0].get('area_deal_status')
            deal_weight = record[0].get('deal_weight')
            record_weight = record[0].get('record_weight', 0)
            if area_confirmer:
                confirmer = area_confirmer
                if area_deal_status == 2:
                    area_status = '通过'
                elif area_deal_status == 3:
                    area_status = '拒绝'
                else:
                    area_status = ''
                sort_record = 3
                com_deal_status = ''
            else:
                sort_record = 2
                confirmer = company_confirmer
                if company_deal_status == 2 and area_deal_status == 1:
                    area_status = '未处理'
                    sort_record = 3
                else:
                    area_status = ''
                if company_deal_status == 1:
                    com_deal_status = '通过'
                elif company_deal_status == 2:
                    com_deal_status = '拒绝'
                else:
                    com_deal_status = '未处理'
        else:
            type_name = ''
            sort_record = 1
            com_deal_status = ''
            area_status = ''
            confirmer = ''
            deal_weight = ''
            record_weight = ''
        data['type_name'] = type_name
        data['sort_record'] = sort_record
        data['com_result'] = com_deal_status
        data['confirmer'] = confirmer
        data['area_result'] = area_status
        data['deal_weight'] = deal_weight
        data['record_weight'] = record_weight

        # 返回处理之后的数据
        return data

    class Meta:
        model = CarRecordFlow
        fields = '__all__'


class NonResidentBaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = NonResidentBase
        exclude = ['created_at', 'updated_at']


class NonresidentChargeSer(serializers.ModelSerializer):
    class Meta:
        model = NonresidentCharge
        fields = '__all__'


class AppointmentCollectionSer(serializers.ModelSerializer):
    def get_org_mapping(self):
        if not hasattr(self, 'org_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            org_ids = set(i.org_id for i in items)
            org_query = Organization.objects.filter(org_id__in=org_ids).values('org_id', 'name', 'street_coding',
                                                                               'comm_coding', 'address', 'phone',
                                                                               'contacts')
            self.org_mapping = {obj.get('org_id'): obj for obj in org_query}
        return self.org_mapping

    def get_region_mapping(self):
        if not hasattr(self, 'region_mapping'):
            region_codes = set()
            items = self.get_org_mapping()
            for item in items.values():
                region_codes.add(item.get('area_coding'))
                region_codes.add(item.get('street_coding'))
                region_codes.add(item.get('comm_coding'))
            regions = CityRegion.objects.filter(coding__in=region_codes, is_deleted=0).values('coding', 'name')
            self.region_mapping = {r.get('coding'): r.get('name') for r in regions}
        return self.region_mapping

    def to_representation(self, instance):
        data = super().to_representation(instance)
        org_info = self.get_org_mapping().get(data['org_id'])
        area_coding = org_info.get('area_coding')
        comm_coding = org_info.get('comm_coding')
        street_coding = org_info.get('street_coding')
        data['area_name'] = self.get_region_mapping().get(area_coding)
        data['street_name'] = self.get_region_mapping().get(street_coding)
        data['comm_name'] = self.get_region_mapping().get(comm_coding)
        data['org_name'] = org_info.get('name')
        data['address'] = org_info.get('address')
        data['phone'] = org_info.get('phone')
        data['org_name'] = org_info.get('name')
        data['contacts'] = org_info.get('contacts')
        data['price'] = round(float(data['weight']) * 300, 2)
        return data

    class Meta:
        model = MyNonresidentAppointmentCollection
        fields = '__all__'


class OrgSecondSer(serializers.ModelSerializer):

    def get_org_region_mapping(self):
        if not hasattr(self, 'org_region_mapping'):
            region_codes = set()
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            org_ids = [i.org_id for i in items]
            org_info = Organization.objects.filter(org_id__in=org_ids)
            for org in org_info:
                if hasattr(org, 'area_coding'):
                    region_codes.add(org.area_coding)
                if hasattr(org, 'street_coding'):
                    region_codes.add(org.street_coding)
                if hasattr(org, 'comm_coding'):
                    region_codes.add(org.comm_coding)
            regions = CityRegion.objects.filter(coding__in=region_codes, is_deleted=0)
            self.org_region_mapping = {r.coding: r.name for r in regions}
        return self.org_region_mapping

    def to_representation(self, instance):
        data = super().to_representation(instance)
        org = Organization.objects.filter(org_id=data['org_id']).values('contacts', 'phone',
                                                                        'restaurant_predict_weight',
                                                                        'restaurant_trash_120', 'restaurant_trash_240',
                                                                        'name', 'credit_code', 'area_coding',
                                                                        'street_coding', 'comm_coding', "rubbishes")
        group_org = Organization.objects.filter(org_id=data['org_group_id']).values('name').first()
        if org and org[0]:
            name = org[0].get('name')
            contacts = org[0].get('contacts')
            phone = org[0].get('phone')
            restaurant_predict_weight = org[0].get('restaurant_predict_weight')
            restaurant_trash_240 = org[0].get('restaurant_trash_240')
            restaurant_trash_120 = org[0].get('restaurant_trash_120')
            credit_code = org[0].get('credit_code')
            area_coding = org[0].get('area_coding')
            comm_coding = org[0].get('comm_coding')
            street_coding = org[0].get('street_coding')
            rubbishes_name = "其他垃圾" if data.get('rubbishes') == NonresidentRubbishes.OTHER else "餐厨垃圾"
        else:
            name = ''
            contacts = ''
            phone = ''
            restaurant_predict_weight = ''
            restaurant_trash_240 = ''
            restaurant_trash_120 = ''
            credit_code = ''
            area_coding = ''
            comm_coding = ''
            street_coding = ''
            rubbishes_name = ''
        org_region_mapping = self.get_org_region_mapping()
        data['area_name'] = org_region_mapping.get(area_coding)
        data['street_name'] = org_region_mapping.get(street_coding)
        data['comm_name'] = org_region_mapping.get(comm_coding)
        data['org_group_name'] = group_org.get("name") if group_org else ""
        data['name'] = name
        # data['contacts'] = desensitize(contacts) if contacts else ''
        # data['phone'] = desensitize(phone) if phone else ''
        data['restaurant_predict_weight'] = restaurant_predict_weight
        data['restaurant_trash_120'] = restaurant_trash_120
        data['restaurant_trash_240'] = restaurant_trash_240
        data['credit_code'] = credit_code
        data['rubbishes_name'] = rubbishes_name
        return data

    class Meta:
        model = OrgGroup
        fields = '__all__'


class CapacityCarRecordSer(serializers.ModelSerializer):

    def get_org_mapping(self):
        if not hasattr(self, 'org_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            org_ids = [i.org_id for i in items]
            org_queryset = Organization.objects.filter(is_deleted=0, org_id__in=org_ids)
            self.org_mapping = {obj.org_id: obj for obj in org_queryset}
        return self.org_mapping

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        area_name = get_region_mapping(self).get(obj.area_coding)
        return area_name.name if area_name else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        street_name = get_region_mapping(self).get(obj.street_coding)
        return street_name.name if street_name else ''

    def get_org_info(self, org_id):
        org = self.get_org_mapping().get(org_id)
        name = org.name if org else ""
        clean_no = org.clean_no if org else ""
        credit_code = org.credit_code if org else ""
        return name, clean_no, credit_code

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        transport_company_obj = get_transport_company_mapping(self)
        return transport_company_obj.get(obj.car_num) if transport_company_obj else ""

    create_time_str = serializers.SerializerMethodField()

    def get_create_time_str(self, obj):
        time_ds = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.create_time))
        create_time = datetime.datetime.strptime(time_ds, "%Y-%m-%d %H:%M:%S")
        de = datetime.datetime.strftime(create_time, '%Y-%m-%d %H:%M:%S')
        return de

    def to_representation(self, obj):
        data = super().to_representation(obj)
        name, clean_no, credit_code = self.get_org_info(obj.org_id)
        data['org_name'] = name
        data['credit_code'] = credit_code
        data['clean_no'] = clean_no
        return data

    class Meta:
        model = CarRecordFlow
        fields = '__all__'


class ExportOrgNonresidentSer(serializers.ModelSerializer):
    org_sub_type_name = serializers.SerializerMethodField()

    def get_org_sub_type_name(self, obj):
        org_type = get_org_type_mapping(self).get(obj.org_sub_type_id)
        return org_type.name if org_type else ''

    org_type_name = serializers.SerializerMethodField()

    def get_org_type_name(self, obj):
        org_type = get_org_type_mapping(self).get(obj.org_type_id)
        return org_type.name if org_type else ''

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        region = get_region_mapping(self).get(obj.area_coding)
        return region.name if region else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        region = get_region_mapping(self).get(obj.street_coding)
        return region.name if region else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        region = get_region_mapping(self).get(obj.comm_coding)
        return region.name if region else ''

    def get_transport_contract(self, obj):
        contract_obj = TransportContract.objects.filter(transport_company_id=obj.transport_company_id,
                                                        org_id=obj.org_id, contract_status=1, is_deleted=0).first()
        contract_num = contract_obj.contract_num if contract_obj else ''
        viald_time = contract_obj.viald_time if contract_obj else ''
        return contract_num, viald_time

    transport_contract_num = serializers.SerializerMethodField()

    def get_transport_contract_num(self, obj):
        contract_num, viald_time = self.get_transport_contract(obj)
        return contract_num

    transport_contract_viald_time = serializers.SerializerMethodField()

    def get_transport_contract_viald_time(self, obj):
        contract_num, viald_time = self.get_transport_contract(obj)
        return viald_time

    def get_transport_company(self, obj):
        company_obj = TransportCompany.objects.filter(transport_company_id=obj.transport_company_id).first()
        company = company_obj.company if company_obj else ''
        admin = company_obj.admin if company_obj else ''
        phone = company_obj.phone if company_obj else ''
        clean_code = company_obj.clean_code if company_obj else ''
        return company, admin, phone, clean_code

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return company

    transport_company_admin = serializers.SerializerMethodField()

    def get_transport_company_admin(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return admin

    transport_company_phone = serializers.SerializerMethodField()

    def get_transport_company_phone(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return desensitize(phone) if phone else ""

    transport_company_clean_code = serializers.SerializerMethodField()

    def get_transport_company_clean_code(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return clean_code

    def get_org_detail(self, obj):
        org_detail = OrgDetail.objects.filter(org_id=obj.org_id, is_deleted=0).values('floor_area', 'loating')
        floor_area = org_detail[0].get('floor_area', '') if org_detail and org_detail[0] else ''
        loating = org_detail[0].get('loating', '') if org_detail and org_detail[0] else ''
        return floor_area, loating

    floor_area = serializers.SerializerMethodField()

    def get_floor_area(self, obj):
        floor_area, loating = self.get_org_detail(obj)
        return floor_area

    loating = serializers.SerializerMethodField()

    def get_loating(self, obj):
        floor_area, loating = self.get_org_detail(obj)
        return loating

    factory_name = serializers.SerializerMethodField()

    def get_factory_name(self, obj):
        return ''

    contract_num = serializers.SerializerMethodField()

    def get_contract_num(self, obj):
        return ''

    # 乙方统一信用代码
    second_credit_code = serializers.SerializerMethodField()

    def get_second_credit_code(self, obj):
        second_transport_company_mapping = get_second_transport_company_mapping(self)
        transport_company = second_transport_company_mapping.get(obj.transport_company_id)
        return transport_company.credit_code if transport_company else ''

    # 乙方单位性质
    second_company_type = serializers.SerializerMethodField()

    def get_second_company_type(self, obj):
        second_transport_company_mapping = get_second_transport_company_mapping(self)
        transport_company = second_transport_company_mapping.get(obj.transport_company_id)
        return transport_company.get_company_type_str() if transport_company else ''

    # 非居民信息是否补全
    complete_base = serializers.SerializerMethodField()

    def get_complete_base(self, obj):
        complete_base_obj = get_non_resident_base_mapping(self).get(obj.org_id)
        if complete_base_obj:
            return '是'
        return '否'

    other_complete_base = serializers.SerializerMethodField()

    def get_other_complete_base(self, obj):
        complete_base_obj = get_non_resident_base_mapping(self, rubbishes=NonresidentRubbishes.OTHER).get(obj.org_id)
        if complete_base_obj:
            return '是'
        return '否'

    # 注册时间(初次审核通过)
    register_time = serializers.SerializerMethodField()

    def get_register_time(self, obj):
        declare = org_nonresident_declare_mapping(self).get(obj.org_id)
        if declare:
            return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(declare.audit_time))
        return ''

    # 是否签署过合同
    is_sign_contract = serializers.SerializerMethodField()

    def get_is_sign_contract(self, obj):
        contract_new = get_contract_new_mapping(self).get(obj.org_id)
        if contract_new:
            return '是'
        return '否'

    def get_logout_time_and_type(self, org_id, rubbishes=NonresidentRubbishes.RESTAURANTS):
        declare = OrgNonresidentDeclare.objects.filter(
            check_type__in=[2, 3], status=2, org_id=org_id, rubbishes=rubbishes
        ).values('audit_time', 'check_type')
        audit_time = declare[0].get('audit_time') if declare and declare[0] else 0
        check_type = declare[0].get('check_type') if declare and declare[0] else -1
        if audit_time:
            time_ds = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(audit_time))
            create_time = datetime.datetime.strptime(time_ds, "%Y-%m-%d %H:%M:%S")
            logout_time = datetime.datetime.strftime(create_time, '%Y-%m-%d %H:%M:%S')
        else:
            logout_time = ''
        return logout_time, get_logout_type_name(check_type)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        org_id = data['org_id']
        logout_time, logout_type = self.get_logout_time_and_type(org_id)
        data['logout_time'] = logout_time
        data['logout_type'] = logout_type
        other_logout_time, other_logout_type = self.get_logout_time_and_type(org_id, NonresidentRubbishes.OTHER)
        data['other_logout_time'] = other_logout_time
        data['other_logout_type'] = other_logout_type
        return data

    def get_org_group_mapping(self):
        if not hasattr(self, 'org_group_mapping'):
            org_group = OrgGroup.objects.filter(status=1).values_list('org_group_id', flat=True)
            return org_group

    org_group = serializers.SerializerMethodField()

    def get_org_group(self, obj):
        org_ids = self.get_org_group_mapping()
        if obj.org_id in list(org_ids):
            return '是'
        else:
            return ' 否'

    has_rfid = serializers.SerializerMethodField()

    def get_has_rfid(self, obj):
        has_rfid = '否'
        org_dict = get_org_rfid_mapping_by_time(self)
        if obj.org_id in org_dict.keys():
            has_rfid = '是'
        return has_rfid

    rfid_last_create_time = serializers.SerializerMethodField()

    def get_rfid_last_create_time(self, obj):
        last_time = ''
        org_rfid_mapping = get_org_rfid_mapping_by_time(self)
        last_time = org_rfid_mapping.get(obj.org_id)
        last_time = time.strftime("%Y-%m-%d", time.localtime(last_time)) if last_time else ""
        return last_time

    def _get_contract_is_upload(self, obj, rubbishes=NonresidentRubbishes.RESTAURANTS):
        org_contract_new_info = get_contract_new_mapping_by_time(self, rubbishes).get(obj.org_id)
        return 1 if org_contract_new_info and org_contract_new_info.contract_picture else 0

    contract_is_upload = serializers.SerializerMethodField()

    def get_contract_is_upload(self, obj):
        return self._get_contract_is_upload(obj)

    other_contract_is_upload = serializers.SerializerMethodField()

    def get_other_contract_is_upload(self, obj):
        return self._get_contract_is_upload(obj, NonresidentRubbishes.OTHER)

    def _get_contract_upload_time(self, obj, rubbishes=NonresidentRubbishes.RESTAURANTS):
        org_contract_new_info = get_contract_new_mapping_by_time(self, rubbishes).get(obj.org_id)
        return str(
            org_contract_new_info.create_time) if org_contract_new_info and org_contract_new_info.create_time else ""

    contract_upload_time = serializers.SerializerMethodField()

    def get_contract_upload_time(self, obj):
        return self._get_contract_upload_time(obj)

    other_contract_upload_time = serializers.SerializerMethodField()

    def get_other_contract_upload_time(self, obj):
        return self._get_contract_upload_time(obj, NonresidentRubbishes.OTHER)

    def _get_contract_overdue_status(self, obj, rubbishes=NonresidentRubbishes.RESTAURANTS):
        org_contract_new_info = get_contract_new_mapping_by_time(self, rubbishes).get(obj.org_id)
        stats_mapping = {
            1: "正常",
            2: "过期",
            3: "作废",
        }
        if org_contract_new_info and org_contract_new_info.contract_status:
            return stats_mapping.get(org_contract_new_info.contract_status) or "未知"
        return "未知"

    contract_overdue_status = serializers.SerializerMethodField()

    def get_contract_overdue_status(self, obj):
        return self._get_contract_overdue_status(obj)

    other_contract_overdue_status = serializers.SerializerMethodField()

    def get_other_contract_overdue_status(self, obj):
        return self._get_contract_overdue_status(obj, NonresidentRubbishes.OTHER)

    def _get_contract_viald_time(self, obj, rubbishes=NonresidentRubbishes.RESTAURANTS):
        org_contract_new_info = get_contract_new_mapping_by_time(self, rubbishes).get(obj.org_id)

        return [org_contract_new_info.service_start_date,
                org_contract_new_info.service_end_date] if org_contract_new_info else ["", ""]

    contract_viald_time = serializers.SerializerMethodField()

    def get_contract_viald_time(self, obj):
        return self._get_contract_viald_time(obj)

    other_contract_viald_time = serializers.SerializerMethodField()

    def get_other_contract_viald_time(self, obj):
        return self._get_contract_viald_time(obj, NonresidentRubbishes.OTHER)

    is_weight_record = serializers.SerializerMethodField()

    def get_is_weight_record(self, obj):
        org_weight_obj = get_org_has_car_record_mapping(self).get(obj.org_id)

        return 1 if org_weight_obj else 0

    weight_last_time = serializers.SerializerMethodField()

    def get_weight_last_time(self, obj):
        org_weight_obj = get_org_has_car_record_mapping(self).get(obj.org_id)

        return time.strftime("%Y-%m-%d", time.localtime(
            org_weight_obj.get("last_time"))) if org_weight_obj and org_weight_obj.get("last_time") else ""

    weight_farthest_time = serializers.SerializerMethodField()

    def get_weight_farthest_time(self, obj):
        org_weight_obj = get_org_has_car_record_mapping(self).get(obj.org_id)

        return time.strftime("%Y-%m-%d", time.localtime(
            org_weight_obj.get("farthest_time"))) if org_weight_obj and org_weight_obj.get("farthest_time") else ""

    class Meta:
        model = Organization
        fields = '__all__'


class NonresidentPayOrderSer(serializers.ModelSerializer):

    def get_non_mapping(self):
        if not hasattr(self, 'org_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            org_ids = [i.org_id for i in items]
            org_queryset = Organization.objects.filter(is_deleted=0, org_id__in=org_ids)
            self.org_mapping = {obj.org_id: obj for obj in org_queryset}
        return self.org_mapping

    def _get_rubbish_name(self, type_id):
        if not hasattr(self, "_rubbish_mapping"):
            self._rubbish_mapping = {
                obj["type_id"]: obj["name"] for obj in RubbishType.objects.values("type_id", "name")
            }
        return self._rubbish_mapping.get(type_id)

    def get_pay_config(self):
        if not hasattr(self, 'pay_config'):
            pay_configs = PayConfig.objects.filter(is_deleted=0, status=0).values('pay_config_id', 'name')
            self.pay_config = {obj.get('pay_config_id'): obj.get('name') for obj in pay_configs}
        return self.pay_config

    def to_representation(self, instance):
        data = super().to_representation(instance)
        bill_factory_id = data['bill_factory_id']
        bill_org_id = data['bill_org_id']
        car_org = CarBillOrg.objects.filter(is_deleted=0, bill_org_id=bill_org_id, bill_org_id__isnull=False,
                                            bill_factory_id__isnull=False,
                                            bill_factory_id=bill_factory_id).exclude(bill_org_id="",
                                                                                     bill_factory_id="").first()
        type_id = car_org.type_id if car_org else ''
        weight = car_org.bill_weight if car_org else 0
        start_time = car_org.start_time if car_org else 0
        create_time = car_org.create_time if car_org else 0
        car_num = car_org.car_num if car_org else 0
        bill_count = car_org.bill_count if car_org else 0
        data['start_time'] = datetime.datetime.fromtimestamp(start_time). \
            strftime("%Y-%m-%d %H:%M:%S") if start_time else ""
        data['weight'] = weight
        data['car_num'] = car_num
        data['bill_count'] = bill_count
        data['type_name'] = self._get_rubbish_name(type_id)
        data['create_time'] = datetime.datetime.fromtimestamp(create_time). \
            strftime("%Y-%m-%d %H:%M:%S") if create_time else ''
        org_map = self.get_non_mapping().get(data['org_id'])
        data['org_name'] = org_map.name if org_map else ''
        data['clean_no'] = org_map.clean_no if org_map else ''
        pay_config_id = data.get('pay_config_id')
        pay_config = PayConfig.objects.filter(is_deleted=0, status=0, pay_config_id=pay_config_id). \
            values('charge_rules', 'charge_price', 'corporate_account', 'open_bank').first()
        data['charge_price'] = pay_config.get('charge_price') if pay_config else ''
        data['charge_rules_config'] = pay_config.get('charge_rules') if pay_config else ''
        # if data.get('pay_type') in ('预付金额', '公对公支付'):
        #     data['actual_payment'] = 0
        # else:
        #     data['actual_payment'] = data.get('pay_price')
        data['actual_payment'] = data.get('total_fee')
        # if data.get('pay_type') in ('预付金额', '公对公支付', '预付订单', '公对公支付订单'):
        #     transport_company_id = data.get('transport_company_id')
        #     org_balance = NonOrgBalance.objects.filter(is_deleted=0, org_id=data.get('org_id'),
        #                                                transport_company_id=transport_company_id).first()
        #     data['balance'] = org_balance.pre_pay_balance if org_balance else 0
        # else:
        #     data['balance'] = 0
        data['open_bank'] = pay_config.get('open_bank') if pay_config else ''
        data['corporate_account'] = pay_config.get('corporate_account') if pay_config else ''
        pay_config_id = data.get('pay_config_id')
        data['pay_config_name'] = self.get_pay_config().get(pay_config_id)
        # 返回处理之后的数据
        return data

    class Meta:
        model = NonresidentPayOrder
        fields = '__all__'


class OtherOrgNonresidentDeclareSer(serializers.ModelSerializer):
    class Meta:
        model = OtherOrgNonresidentDeclare
        fields = '__all__'

    def __get_transport_company(self, obj):
        if not hasattr(self, '_transport_company_cache'):
            self._transport_company_cache = dict()
        company = self._transport_company_cache.get(obj.transport_company_id)
        if not company:
            company = OtherTransportCompany.objects.filter(company_id=obj.transport_company_id).first()
            if company:
                company = dict(name=company.name,
                               clean_code=company.clean_code,
                               admin=company.admin,
                               phone=company.phone)
                self._transport_company_cache[obj.transport_company_id] = company
        return company or dict(
            name='',
            admin='',
            phone='',
            clean_code=''
        )

    org_sub_type_name = serializers.SerializerMethodField()

    def get_org_sub_type_name(self, obj):
        org_sub_type_name = OrgType.objects.filter(org_type_id=obj.org_sub_type_id, is_deleted=0).first()
        return org_sub_type_name.name if org_sub_type_name else '未知'

    org_type_name = serializers.SerializerMethodField()

    def get_org_type_name(self, obj):
        org_type_name = OrgType.objects.filter(org_type_id=obj.org_type_id, is_deleted=0).first()
        return org_type_name.name if org_type_name else '未知'

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        area_name = CityRegion.objects.filter(coding=obj.area_coding, is_deleted=0).first()
        return area_name.name if area_name else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        street_name = CityRegion.objects.filter(coding=obj.street_coding, is_deleted=0).first()
        return street_name.name if street_name else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        comm_name = CityRegion.objects.filter(coding=obj.comm_coding, is_deleted=0).first()
        return comm_name.name if comm_name else ''

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        company = self.__get_transport_company(obj)
        return company.get('name', '') if company else ''

    transport_company_clean_code = serializers.SerializerMethodField()

    def get_transport_company_clean_code(self, obj):
        return self.__get_transport_company(obj).get('clean_code', '')

    transport_company_admin = serializers.SerializerMethodField()

    def get_transport_company_admin(self, obj):
        return self.__get_transport_company(obj).get('admin', '')

    transport_company_phone = serializers.SerializerMethodField()

    def get_transport_company_phone(self, obj):
        return self.__get_transport_company(obj).get('phone', '')

    is_admin = serializers.SerializerMethodField()

    def get_is_admin(self, obj):
        if 'request' in self.context and hasattr(self.context['request'], 'manager'):
            creator = self.context['request'].manager.get('relation_id')
            org_id = obj.org_id
            authorized_org = AuthorizedOrgRelationId.objects.filter(authorized_org_relation_id=creator,
                                                                    org_id=org_id).first()
            if authorized_org:
                return authorized_org.is_admin
            else:
                _org = AuthorizedOrgRelationId.objects.filter(org_id=org_id).first()
                return 0 if _org else 1
        return 0

    reson_str = serializers.SerializerMethodField()

    def get_reson_str(self, obj):
        reson_str = settings.RESON_DATA.get(obj.logout_reson, '')
        return reson_str

    check_type_str = serializers.SerializerMethodField()

    def get_check_type_str(self, obj):
        if obj.check_type == 0:
            check_type_str = '非居民单位注册信息'
        elif obj.check_type == 1:
            check_type_str = '非居民单位修改信息'
        elif obj.check_type == 2:
            check_type_str = '非居民单位注销信息'
        else:
            check_type_str = ''
        return check_type_str

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if not data["transport_company_id"] and data["transport_company_info"]:
            company_info = json.loads(data["transport_company_info"]) if data["transport_company_info"] else {}
            data["transport_company_info"] = company_info
            data["transport_company_name"] = company_info.get("company")
            data["transport_company_credit_code"] = company_info.get("credit_code")
            data["transport_company_admin"] = company_info.get("admin")
            data["transport_company_phone"] = company_info.get("transport_company_phone")
        return data


class OtherOrgNonresidentSer(serializers.ModelSerializer):
    org_sub_type_name = serializers.SerializerMethodField()

    def get_org_sub_type_name(self, obj):
        org_type = get_org_type_mapping(self).get(obj.org_sub_type_id)
        return org_type.name if org_type else ''

    org_type_name = serializers.SerializerMethodField()

    def get_org_type_name(self, obj):
        org_type = get_org_type_mapping(self).get(obj.org_type_id)
        return org_type.name if org_type else ''

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        region = get_region_mapping(self).get(obj.area_coding)
        return region.name if region else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        region = get_region_mapping(self).get(obj.street_coding)
        return region.name if region else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        region = get_region_mapping(self).get(obj.comm_coding)
        return region.name if region else ''

    def get_transport_contract(self, obj):
        contract_obj = OtherOrgNonresidentDeclare.objects.filter(transport_company_id=obj.transport_company_id,
                                                                 org_id=obj.org_id, status=2, check_type__in=[0, 1],
                                                                 is_status_succeed=1).first()
        return contract_obj

    contract_pic = serializers.SerializerMethodField()

    def get_contract_pic(self, obj):
        contract_obj = self.get_transport_contract(obj)

        return contract_obj.contract_pic if contract_obj else ""

    contract_start_date = serializers.SerializerMethodField()

    def get_contract_start_date(self, obj):
        contract_obj = self.get_transport_contract(obj)

        return contract_obj.contract_start_date if contract_obj else ""

    contract_end_date = serializers.SerializerMethodField()

    def get_contract_end_date(self, obj):
        contract_obj = self.get_transport_contract(obj)

        return contract_obj.contract_end_date if contract_obj else ""

    def get_transport_company(self, obj):
        company_obj = OtherTransportCompany.objects.filter(company_id=obj.transport_company_id, is_delete=0,
                                                           status=1).first()
        company = company_obj.name if company_obj else ''
        admin = company_obj.admin if company_obj else ''
        phone = company_obj.phone if company_obj else ''
        clean_code = company_obj.clean_code if company_obj else ''
        return company, admin, phone, clean_code

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return company

    transport_company_admin = serializers.SerializerMethodField()

    def get_transport_company_admin(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return admin

    transport_company_phone = serializers.SerializerMethodField()

    def get_transport_company_phone(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return phone

    transport_company_clean_code = serializers.SerializerMethodField()

    def get_transport_company_clean_code(self, obj):
        company, admin, phone, clean_code = self.get_transport_company(obj)
        return clean_code

    def get_org_detail(self, obj):
        org_detail = OtherOrgNonresidentDeclare.objects.filter(org_id=obj.org_id, status=2,
                                                               check_type=2).values('floor_area', 'loating')
        floor_area = org_detail[0].get('floor_area', '') if org_detail and org_detail[0] else ''
        loating = org_detail[0].get('loating', '') if org_detail and org_detail[0] else ''
        return floor_area, loating

    floor_area = serializers.SerializerMethodField()

    def get_floor_area(self, obj):
        floor_area, loating = self.get_org_detail(obj)
        return floor_area

    loating = serializers.SerializerMethodField()

    def get_loating(self, obj):
        floor_area, loating = self.get_org_detail(obj)
        return loating

    factory_name = serializers.SerializerMethodField()

    def get_factory_name(self, obj):
        return ''

    contract_num = serializers.SerializerMethodField()

    def get_contract_num(self, obj):
        return ''

    # 乙方统一信用代码
    second_credit_code = serializers.SerializerMethodField()

    def get_second_credit_code(self, obj):
        second_transport_company_mapping = get_second_transport_company_mapping(self)
        transport_company = second_transport_company_mapping.get(obj.transport_company_id)
        return transport_company.credit_code if transport_company else ''

    # 乙方单位性质
    second_company_type = serializers.SerializerMethodField()

    def get_second_company_type(self, obj):
        second_transport_company_mapping = get_second_transport_company_mapping(self)
        transport_company = second_transport_company_mapping.get(obj.transport_company_id)
        return transport_company.get_company_type_str() if transport_company else ''

    # 非居民信息是否补全
    complete_base = serializers.SerializerMethodField()

    def get_complete_base(self, obj):
        complete_base_obj = get_non_resident_base_mapping(self).get(obj.org_id)
        if complete_base_obj:
            return '是'
        return '否'

    # 注册时间(初次审核通过)
    register_time = serializers.SerializerMethodField()

    def get_register_time(self, obj):
        declare = other_org_nonresident_declare_mapping(self).get(obj.org_id)
        if declare:
            return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(declare.audit_time))
        return ''

    # 是否签署过合同
    is_sign_contract = serializers.SerializerMethodField()

    def get_is_sign_contract(self, obj):
        if obj.have_contract:
            return '是'
        return '否'

    def to_representation(self, instance):
        data = super().to_representation(instance)
        declare = OtherOrgNonresidentDeclare.objects.filter(check_type__in=[2, 3], status=2, org_id=data['org_id']). \
            values('audit_time', 'check_type')
        audit_time = declare[0].get('audit_time') if declare and declare[0] else 0
        check_type = declare[0].get('check_type') if declare and declare[0] else -1
        if audit_time:
            time_ds = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(audit_time))
            create_time = datetime.datetime.strptime(time_ds, "%Y-%m-%d %H:%M:%S")
            logout_time = datetime.datetime.strftime(create_time, '%Y-%m-%d %H:%M:%S')
        else:
            logout_time = ''
        data['logout_time'] = logout_time
        data['logout_type'] = get_logout_type_name(check_type)
        return data

    def get_org_group_mapping(self):
        if not hasattr(self, 'org_group_mapping'):
            org_group = OrgGroup.objects.filter(status=1).values_list('org_group_id', flat=True)
            return org_group

    org_group = serializers.SerializerMethodField()

    def get_org_group(self, obj):
        org_ids = self.get_org_group_mapping()
        if obj.org_id in list(org_ids):
            return '是'
        else:
            return '否'

    has_rfid = serializers.SerializerMethodField()

    def get_has_rfid(self, obj):
        has_rfid = '否'
        org_list = org_rfid_list(self)
        if obj.org_id in org_list:
            has_rfid = '是'
        return has_rfid

    rfid_last_create_time = serializers.SerializerMethodField()

    def get_rfid_last_create_time(self, obj):
        last_time = ''
        org_rfid_mapping = get_org_rfid_mapping_by_time(self)
        last_time = org_rfid_mapping.get(obj.org_id)
        last_time = time.strftime("%Y-%m-%d", time.localtime(last_time)) if last_time else ""
        return last_time

    class Meta:
        model = OrganizationOther
        fields = '__all__'


class OrgNonresidentQuotaSer(serializers.ModelSerializer):
    org_sub_type_name = serializers.SerializerMethodField()

    def get_org_sub_type_name(self, obj):
        org_type = get_org_type_mapping(self).get(obj.org_sub_type_id)
        return org_type.name if org_type else ''

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        region = get_region_mapping(self).get(obj.area_coding)
        return region.name if region else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        region = get_region_mapping(self).get(obj.street_coding)
        return region.name if region else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        region = get_region_mapping(self).get(obj.comm_coding)
        return region.name if region else ''

    def quota_info(self):
        if not hasattr(self, 'quota_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            org_ids = [i.org_id for i in items]
            year_current = get_current_year()
            quota = OrgNonresidentQuota.objects.filter(org_id__in=org_ids, quota_date=year_current, is_deleted=0)
            self.quota_mapping = {i.org_id: i for i in quota}
        return self.quota_mapping

    def to_representation(self, instance):
        data = super().to_representation(instance)
        quota_info = self.quota_info().get(data['org_id'])
        data['quota'] = quota_info.quota if quota_info else ''
        data['status'] = quota_info.status if quota_info else 0
        # status_dict = {0: '待定额', 1: '待确认', 2: '申诉中|街道申诉中', 3: '区级审核中', 4: '定额完成'}
        data['quota_date'] = quota_info.quota_date if quota_info else ''
        return data

    class Meta:
        model = Organization
        fields = ['org_id', 'name', 'clean_no', 'org_sub_type_name',
                  'area_name', 'street_name', 'comm_name', 'mam_type']


class BillCarOrgSer(serializers.ModelSerializer):
    class Meta:
        model = CarBillOrg
        # fields = '__all__'
        exclude = ["id", "is_deleted", "update_time", "remark", "create_time"]

    # pay_price = serializers.SerializerMethodField()
    #
    # def get_pay_price(self, obj):
    #
    #     return (obj.bill_weight / 1000) * 30000

    def _get_coding_name(self, coding):
        if not coding:
            return ""
        if not hasattr(self, "coding_mapping"):
            regions = CityRegion.objects.filter(is_deleted=0).values("coding", "name")
            self.coding_mapping = {a.get("coding"): a.get("name") for a in regions}
        return self.coding_mapping.get(coding, "")

    def _get_org(self, org_id):
        if not org_id:
            return dict()
        if not hasattr(self, "org_mapping"):
            self.org_mapping = dict()
        if org_id not in self.org_mapping:
            org = Organization.objects.filter(is_deleted=0, org_id=org_id).first()
            self.org_mapping[org_id] = model_to_dict(org) if org else dict()
        return self.org_mapping.get(org_id, dict())

    def _get_transport_company_name(self, transport_company_id):
        if not transport_company_id:
            return ""
        if not hasattr(self, "transport_company_mapping"):
            self.transport_company_mapping = dict()
        if transport_company_id in self.transport_company_mapping:
            return self.transport_company_mapping.get(transport_company_id)
        company = TransportCompany.objects.filter(transport_company_id=transport_company_id, is_deleted=0).first()
        company_name = company.company if company else ""
        self.transport_company_mapping[transport_company_id] = company_name
        return company_name

    def _get_is_appeal(self, bill_factory_id, bill_org_id):
        appeal_record = AppealRecod.objects.filter(
            bill_factory_id=bill_factory_id, bill_org_id=bill_org_id, is_deleted=0
        )
        if appeal_record.exists():
            return 0
        else:
            return 1

    @staticmethod
    def _parse_timestamp(timestamp):
        return datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S") if timestamp else ""

    def _get_rubbish_name(self, type_id):
        if not hasattr(self, "_rubbish_mapping"):
            self._rubbish_mapping = {
                obj["type_id"]: obj["name"] for obj in RubbishType.objects.values("type_id", "name")
            }
        return self._rubbish_mapping.get(type_id)

    def get_non_mapping(self):
        if not hasattr(self, 'org_pay_mapping'):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            bill_org_ids = [i.bill_org_id for i in items]
            org_queryset = NonresidentPayOrder.objects.filter(is_deleted=0, bill_org_id__in=bill_org_ids)
            self.org_pay_mapping = {obj.bill_org_id: obj.status for obj in org_queryset}
        return self.org_pay_mapping

    def to_representation(self, obj):
        data = super().to_representation(obj)
        for field in ["start_time", "finish_time"]:
            data[field] = self._parse_timestamp(getattr(obj, field, 0))

        org = self._get_org(data["org_id"])
        data["area_coding"] = org.get("area_coding", "")
        data["street_coding"] = org.get("street_coding", "")
        data["comm_coding"] = org.get("comm_coding", "")
        data["area_name"] = self._get_coding_name(data["area_coding"])
        data["street_name"] = self._get_coding_name(data["street_coding"])
        data["comm_name"] = self._get_coding_name(data["comm_coding"])
        data["org_name"] = org.get("name", "")
        data["clean_no"] = org.get("clean_no", "")
        data["official_org_name"] = org.get("official_org_name", "")
        data["type_name"] = self._get_rubbish_name(data["type_id"])
        data["transport_company_name"] = self._get_transport_company_name(org.get("transport_company_id"))
        data["is_appeal"] = self._get_is_appeal(data["bill_factory_id"], data["bill_org_id"])  # 非居民小程序是否可以申诉
        org_map = self.get_non_mapping().get(data['bill_org_id'])
        data["pay_status"] = org_map or 0
        return data


class BillCarOrgConfirmSerializer(serializers.Serializer):
    """主体联单确认"""

    bill_org_id = serializers.CharField(required=True)

    def validate(self, data):
        bill_org_id = data.get("bill_org_id")
        bill_org = CarBillOrg.objects.filter(is_deleted=0, bill_org_id=bill_org_id).first()
        if not bill_org:
            raise serializers.ValidationError("联单数据不存在.")
        return data


class AppointmentRecyclingSer(serializers.ModelSerializer):
    class Meta:
        model = AppointmentRecycling
        fields = '__all__'


class BagBreakingResidentRecordSer(serializers.ModelSerializer):
    create_time = serializers.SerializerMethodField()
    street_name = serializers.SerializerMethodField()
    comm_name = serializers.SerializerMethodField()
    org_name = serializers.SerializerMethodField()
    resident_name = serializers.SerializerMethodField()
    card_num = serializers.SerializerMethodField()
    # weight = serializers.SerializerMethodField()
    reward_coin = serializers.SerializerMethodField()
    type_name = serializers.SerializerMethodField()
    coin_type = serializers.SerializerMethodField()
    # quality = serializers.SerializerMethodField()
    # cover = serializers.SerializerMethodField()
    rtu_device_name = serializers.SerializerMethodField()
    weight = serializers.SerializerMethodField()

    def get_create_time(self, obj):
        return datetime.datetime.fromtimestamp(obj.create_time).strftime("%Y-%m-%d %H:%M:%S")

    def get_street_name(self, obj):
        return CityRegion.objects.filter(coding=obj.street_coding).first().name

    def get_comm_name(self, obj):
        try:
            comm_name = CityRegion.objects.filter(coding=obj.comm_coding).first().name
        except Exception:
            comm_name = ""
        return comm_name

    def get_org_name(self, obj):
        return obj.org_name

    def get_resident_name(self, obj):
        if not obj.resident_id:
            return ""
        return Resident.objects.using("tidb_ljfl_db").filter(resident_id=obj.resident_id).first().name

    def get_card_num(self, obj):
        if not obj.resident_id:
            return ""
        return Resident.objects.using("tidb_ljfl_db").filter(resident_id=obj.resident_id).first().card_num

    def get_type_name(self, obj):
        return RubbishType.objects.filter(type_id=obj.type_id).first().name

    def get_coin_type(self, obj):
        # null、空字符串、"无桶"、"空桶"等均当作"差"
        coin_type = obj.coin_type
        if coin_type not in ["优", "良", "中"]:
            coin_type = "差"
        return coin_type

    def get_rtu_device_name(self, obj):
        if not obj.device_id:
            return ""
        else:
            device = TrashStationModel.objects.using("bagbreak_db").filter(rtu_device_id=obj.device_id).first()
            return device.device_name if device else ""

    def get_reward_coin(self, obj):
        if obj.reward_coin:
            return obj.reward_coin

        coin_type = obj.quality
        if coin_type == "优":
            reward_coin = 4
        elif coin_type == "良":
            reward_coin = 3
        elif coin_type == "中":
            reward_coin = 2
        else:
            reward_coin = 1
        return reward_coin

    def get_weight(self, obj):
        # 数据存储单位是克
        weight = obj.weight
        return round(weight / 1000, 3)

    class Meta:
        model = ResidentRecord
        fields = [
            "create_time", "street_coding", "street_name", "comm_coding", "comm_name", "org_name", "resident_name",
            "card_num", "weight", "reward_coin", "type_name", "quality", "cover", "coin_type", "creator",
            "rtu_device_name", "resident_record_id"
        ]


class TrashCanAlarmRecordSer(serializers.ModelSerializer):
    alarm_type = serializers.SerializerMethodField()

    def get_alarm_type(self, obj):
        alarm_type_dict = TrashCanAlarmRecord.TrashCanAlarmType.AlarmTypeDict
        return alarm_type_dict.get(obj.alarm_type, "")

    class Meta:
        model = TrashCanAlarmRecord
        fields = '__all__'


class OilWaterSeparationDeviceRecordSer(serializers.ModelSerializer):
    class Meta:
        model = OilWaterSeparationDeviceRecord
        fields = '__all__'


class CarRecordwarningSer(serializers.ModelSerializer):
    class Meta:
        model = CarRecord
        fields = '__all__'

    def _get_coding_name(self, coding):
        if not coding:
            return ""
        if not hasattr(self, "coding_mapping"):
            regions = CityRegion.objects.filter(is_deleted=0, grade__in=(3, 4)).values("coding", "name")
            self.coding_mapping = {a.get("coding"): a.get("name") for a in regions}
        return self.coding_mapping.get(coding, "")

    def _get_org(self, org_id):
        if not org_id:
            return dict()
        if not hasattr(self, "org_mapping"):
            self.org_mapping = dict()
        if org_id not in self.org_mapping:
            org = Organization.objects.filter(is_deleted=0, org_id=org_id).first()
            self.org_mapping[org_id] = model_to_dict(org) if org else dict()
        return self.org_mapping.get(org_id, dict())

    def to_representation(self, obj):
        data = super().to_representation(obj)
        data['create_time'] = datetime.datetime.fromtimestamp(data["create_time"]).strftime("%Y-%m-%d %H:%M:%S") \
            if data["create_time"] else ""
        org = self._get_org(data["org_id"])
        data["street_coding"] = org.get("street_coding", "")
        data["comm_coding"] = org.get("comm_coding", "")
        data["street_name"] = self._get_coding_name(data["street_coding"])
        data["comm_name"] = self._get_coding_name(data["comm_coding"])
        data["org_name"] = org.get("name", "")
        data["org_address"] = org.get("address", "")
        return data


class PendingCompanyRegistrationSer(serializers.ModelSerializer):
    status = serializers.IntegerField(read_only=True)
    is_delete = serializers.IntegerField(read_only=True)
    create_time = serializers.DateTimeField(read_only=True)
    update_time = serializers.DateTimeField(read_only=True)

    status_name = serializers.SerializerMethodField()

    def get_status_name(self, obj):
        if obj.status and obj.status == 1:
            return '已处理'
        else:
            return '未处理'

    def validate(self, data):
        data = super().to_internal_value(data)
        area_name = data.get('area_name')
        if not area_name:
            area_name = CityRegion.objects.filter(is_deleted=0, coding=data['area_coding']).values_list('name',
                                                                                                        flat=True).first()
            if not area_name:
                raise serializers.ValidationError("区域编码不存在.")
            data['area_name'] = area_name
        street_name = data.get('street_name')
        if not street_name:
            street_name = CityRegion.objects.filter(is_deleted=0, coding=data['street_coding']).values_list('name',
                                                                                                            flat=True).first()
            if not street_name:
                raise serializers.ValidationError("街道编码不存在.")
            data['street_name'] = street_name
        return data

    def to_representation(self, obj):
        data = super().to_representation(obj)
        if obj.company_contact_person:
            data['company_contact_person'] = desensitize(obj.company_contact_person)

        if obj.company_contact_phone:
            data['company_contact_phone'] = desensitize(obj.company_contact_phone)

        if obj.org_manager_name:
            data['org_manager_name'] = desensitize(obj.org_manager_name)

        if obj.org_contact_person:
            data['org_contact_person'] = desensitize(obj.org_contact_person)

        if obj.org_contact_phone:
            data['org_contact_phone'] = desensitize(obj.org_contact_phone)

        return data

    class Meta:
        model = PendingCompanyRegistration
        fields = '__all__'


class PendCompanyStatisticSer(serializers.Serializer):
    company_name = serializers.CharField(read_only=True)
    company_count = serializers.IntegerField(read_only=True)
    deal_count = serializers.IntegerField(read_only=True)
    min_id = serializers.IntegerField(read_only=True)


class NonresidentIsOnlineOrgSer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = '__all__'
        # fields = ['name', 'rubbishes', 'longitude_g', 'latitude_g', 'cover', 'contacts', 'phone']


class NonresidentIsOnlineOrgMapSer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = ['org_id', 'name', 'area_coding', 'longitude', 'latitude']


class NonresidentIsOnlineCarSer(serializers.ModelSerializer):
    car_type_name = serializers.SerializerMethodField()

    def get_car_type_name(self, obj):
        car_type_name = get_car_type_mapping(self).get(obj.car_type_id)
        return car_type_name or ""

    rubbish_type_name = serializers.SerializerMethodField()

    def get_rubbish_type_name(self, obj):
        rubbish_obj = get_rubbish_mapping(self)
        return rubbish_obj.get(obj.type_id) if rubbish_obj else ''

    stag = serializers.SerializerMethodField()

    def get_stag(self, obj):
        gps_car_map = get_car_gps_mapping(self)
        return 1 if gps_car_map.get(obj.car_num) else 0

    class Meta:
        model = Car
        fields = '__all__'


class NonresidentIsOnlineCleaningPointSer(serializers.ModelSerializer):
    class Meta:
        model = CleaningPoint
        fields = '__all__'


class NonresidentOutboundRecordSer(serializers.ModelSerializer):

    def get_area_name(self, obj):
        region = get_region_mapping(self).get(obj.area_coding)
        return region.name if region else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        region = get_region_mapping(self).get(obj.street_coding)
        return region.name if region else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        region = get_region_mapping(self).get(obj.comm_coding)
        return region.name if region else ''

    rubbish_type_name = serializers.SerializerMethodField()

    def get_rubbish_type_name(self, obj):
        rubbish_obj = get_rubbish_mapping(self)
        return rubbish_obj.get(obj.type_id) if rubbish_obj else ''

    class Meta:
        model = CleaningPointTransRecord
        fields = '__all__'


class NonresidentCleaningPointRecordSer(serializers.ModelSerializer):
    def __get_transport_company(self, transport_company_id):
        if not hasattr(self, '_transport_company_cache'):
            self._transport_company_cache = dict()
        company = self._transport_company_cache.get(transport_company_id)
        if not company:
            company = TransportCompany.objects.filter(transport_company_id=transport_company_id).first()
            if company:
                company = dict(name=company.company,
                               clean_code=company.clean_code,
                               admin=company.admin,
                               phone=desensitize(company.phone))
                self._transport_company_cache[transport_company_id] = company
        return company or dict()

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        region = get_region_mapping(self).get(obj.area_coding)
        return region.name if region else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        region = get_region_mapping(self).get(obj.street_coding)
        return region.name if region else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        region = get_region_mapping(self).get(obj.comm_coding)
        return region.name if region else ''

    rubbish_type_name = serializers.SerializerMethodField()

    def get_rubbish_type_name(self, obj):
        rubbish_obj = get_rubbish_mapping(self)
        return rubbish_obj.get(obj.type_id) if rubbish_obj else ''

    cleaning_point_name = serializers.SerializerMethodField()

    def get_cleaning_point_name(self, obj):
        cleaning_point = get_cleaning_point_name_mapping(self).get(obj.cleaning_point_id)
        return cleaning_point.name if cleaning_point else ''

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        cleaning_point = get_cleaning_point_name_mapping(self).get(obj.cleaning_point_id)
        if not cleaning_point:
            return ""
        return self.__get_transport_company(cleaning_point.transport_company_id).get('name', '')

    create_time = serializers.SerializerMethodField()

    def get_create_time(self, obj):
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(obj.create_time)) if obj.create_time else ''

    def to_representation(self, obj):
        data = super().to_representation(obj)
        flow_map = get_cleaning_point_flow_mapping(self)
        flow_obj = flow_map.get(obj.cleaning_point_record_id)
        data["source_weight"] = flow_obj.source_weight if flow_obj else obj.weight
        data["capacity_weight"] = (flow_obj.capacity_weight if flow_obj else 0) or 0
        data["weight_type_name"] = ConstWeightType.get_name(flow_obj.weight_type) if flow_obj else "称重"
        data["size"] = flow_obj.size if flow_obj else ""

        org_map = get_org_mapping(self)
        org_obj = org_map.get(obj.org_id)
        data["clean_no"] = org_obj.clean_no if org_obj else ""
        return data

    class Meta:
        model = CleaningPointRecord
        fields = '__all__'


class NonresidentCarRecordSer(serializers.ModelSerializer):
    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        region = get_region_mapping(self).get(obj.area_coding)
        return region.name if region else ''

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        region = get_region_mapping(self).get(obj.street_coding)
        return region.name if region else ''

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        region = get_region_mapping(self).get(obj.comm_coding)
        return region.name if region else ''

    rubbish_type_name = serializers.SerializerMethodField()

    def get_rubbish_type_name(self, obj):
        rubbish_obj = get_rubbish_mapping(self)
        return rubbish_obj.get(obj.type_id) if rubbish_obj else ''

    garbage_where = serializers.SerializerMethodField()

    def get_garbage_where(self, obj):
        flow_obj = get_car_record_flow_mapping(self).get(obj.car_record_id)
        return flow_obj.factory_location_name if flow_obj else ''

    def to_representation(self, obj):
        data = super().to_representation(obj)
        return data

    class Meta:
        model = CarRecord
        fields = '__all__'


class CityRegionSer(serializers.ModelSerializer):
    """行政区划"""

    class Meta:
        model = CityRegion
        fields = "__all__"

    parent_name = serializers.SerializerMethodField()

    def get_parent_name(self, obj):
        if not hasattr(self, "_region_mapping"):
            try:
                items = iter(self.instance)
            except TypeError:
                items = [self.instance]
            parent_ids = {e.parent_id for e in items if e.parent_id}
            qs = CityRegion.objects.filter(region_id__in=parent_ids).values("region_id", "name")
            self._region_mapping = {e["region_id"]: e["name"] for e in qs}
        return self._region_mapping.get(obj.parent_id, "")
