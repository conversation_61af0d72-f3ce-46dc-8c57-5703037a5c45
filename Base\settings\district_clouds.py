#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import * # noqa
from .clouds_environment import * # noqa

DEBUG = False

ALLOWED_HOSTS = [
    '*'
]
DATABASES = {
    'default': {},
    'ljfl_declare_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_declare_db',
        "USER": ENV_DB_USER,
        "PASSWORD": ENV_DB_PASSWORD,
        "HOST": ENV_DB_HOST,
        "PORT": ENV_DB_PORT,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        "USER": ENV_DB_USER,
        "PASSWORD": ENV_DB_PASSWORD,
        "HOST": ENV_DB_HOST,
        "PORT": ENV_DB_PORT,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'ljfl_db_replica': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        "USER": ENV_DB_USER,
        "PASSWORD": ENV_DB_PASSWORD,
        "HOST": ENV_DB_HOST,
        "PORT": ENV_DB_PORT,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'transport_db': {
        'ENGINE': 'django.db.backends.mysql',
        "USER": ENV_DB_USER,
        "PASSWORD": ENV_DB_PASSWORD,
        "HOST": ENV_DB_HOST,
        "PORT": ENV_DB_PORT,
        'NAME': 'transport_db',
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'solid': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'solid_logistics',
        "USER": ENV_DB_USER,
        "PASSWORD": ENV_DB_PASSWORD,
        "HOST": ENV_DB_HOST,
        "PORT": ENV_DB_PORT,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    "jfpt": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "bj_jfpt_admin",
        "USER": ENV_DB_USER,
        "PASSWORD": ENV_DB_PASSWORD,
        "HOST": ENV_DB_HOST,
        "PORT": ENV_DB_PORT,
    },
    "tidb_ljfl_db": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "ljfl_db",
        "USER": ENV_DB_USER,
        "PASSWORD": ENV_DB_PASSWORD,
        "HOST": ENV_DB_HOST,
        "PORT": ENV_DB_PORT,
        "OPTIONS": {"charset": "utf8mb4"},
    },
    "other_transport_db": {
        'ENGINE': 'django.db.backends.mysql',
        "USER": ENV_DB_USER,
        "PASSWORD": ENV_DB_PASSWORD,
        "HOST": ENV_DB_HOST,
        "PORT": ENV_DB_PORT,
        'NAME': 'other_transport_db',
        'OPTIONS': {'charset': 'utf8mb4'},
    },
}

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'KEY_PREFIX': 'apiProduction:collectManageAPPV3',
        'LOCATION': f'redis://{ENV_REDIS_HOST}:{ENV_REDIS_PORT}/4',
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": ENV_REDIS_PASSWORD,
        },
    },
}

#################
# 负载均衡IP
#################
PROXY_IP_A = f"http://{ENV_PROXY_IP}"
PROXY_IP_B = ENV_PROXY_IP

# 管理授权
AUTH_APPID = *********
AUTH_APPSECRET = 'jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7'
AUTH_HOST = f'{PROXY_IP_B}/inskip/v3/auth'
# AUTH_HOST = 'auth.ztbory.com'
# 基础服务
BASE_HOST = f'{PROXY_IP_B}/ljflbasedata'
# OSS服务
OSS_HOST = 'filemanager.ztbory.com'
# 资质审核服务
QUALIFI_HOST = f'{PROXY_IP_B}/v3/transport'

COMPANY_SYS_IP = f'{PROXY_IP_A}/v3/transport'

# 市级服务
CITY_IP = f'{PROXY_IP_A}/v3/city'
