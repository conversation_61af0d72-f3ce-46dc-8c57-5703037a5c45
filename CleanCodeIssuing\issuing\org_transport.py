#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from django.core.cache import cache
from django.db import transaction

from ..logger import logger
from .base import BaseCleanCodeGenerator

from CollectManageApp.models_base import TransportCompany
from CollectManageApp.models_transport import Company


class OrgTransportCleanCodeGenerator(BaseCleanCodeGenerator):
    """
    排放登记编码生成器: 主体-收运单位
    """
    CLEAN_TYPE = 'ORG'
    CLEAN_SUBTYPE = 'TRANSPORT'

    def generate(self, clean_id):
        # 1. 获取编码前缀
        transport = TransportCompany.objects.filter(transport_company_id=clean_id,
                                                    is_declare=1,
                                                    is_deleted=0).first()
        if not transport:
            return dict(code=400, msg='收运公司不存在.')
        if transport.clean_code:
            return dict(code=200, msg='排放登记编码已生成.', data=dict(
                clean_code=transport.clean_code
            ))

        clean_code_prefix = f'{transport.company_type}'

        # 2. 锁定编码发号器
        with cache.lock(self.CLEAN_CODE_LOCK_KEY):
            # 2.2 发号逻辑
            try:
                with transaction.atomic(using='ljfl_db'):
                    with transaction.atomic(using='transport_db'):
                        clean_code, clean_no = self._get_clean_code(clean_code_prefix, clean_id, with_no=False)

                        # 修改正式库
                        TransportCompany.objects.filter(transport_company_id=clean_id, is_declare=1, is_deleted=0) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))

                        # 修改申报库
                        Company.objects.filter(company_id=clean_id) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))


            except Exception as e:
                logger.exception(e)
                return dict(code=500, msg='排放登记编码发放失败.')

        return dict(code=200, msg='排放登记编码发放成功.', data=dict(
            clean_code=clean_code,
            clean_no=clean_no
        ))
