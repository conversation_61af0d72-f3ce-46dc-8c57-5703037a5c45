import math

from django.db.models import F, Q

from Base.utils.const import ConstRubbishType, NonresidentRubbishes
from CollectManageApp.models_base import TerminalFactoryRecord, CarFlowRecord, CarRecordFlow, CarBillOrg, CarRecord
import time,uuid

#末端厂car_flow_record创建逻辑

# 时间转时间戳
def time2timestamps(date_):
    timeArray = time.strptime(date_, "%Y-%m-%d %H:%M:%S")
    timestamp = int(time.mktime(timeArray))
    return timestamp

#uuid
def get_uuid():
    return str(uuid.uuid4()).replace('-', '')

def make_sure_car_flow_record(car_obj,start_time):
    car_num,car_id,area_coding,street_coding=car_obj.car_num,car_obj.car_id,car_obj.area_coding,car_obj.street_coding
    last_termnal_obj=TerminalFactoryRecord.objects.filter(platenumber=car_num).order_by('-weightptime').first()
    weightptime=time2timestamps(str(last_termnal_obj.weightptime))
    #查询在此weightptime和start_time是否有收运流程数据,如果有取出没有创建
    flow_obj=CarFlowRecord.objects.filter(create_time__gte=weightptime,create_time__lte=start_time).first() #注意一下是否加status 状态
    if flow_obj:
        return flow_obj.car_flow_id
    uuid_=get_uuid()
    CarFlowRecord.objects.create(
        car_flow_id=uuid_,
        car_id=car_id,
        car_num=car_num,
        area_coding=area_coding,
        street_coding=street_coding,
        start_time=start_time,
        create_time=start_time,
        update_time=start_time,
        remark='扫码创建',
        status=0
    )
    return  uuid_


__area_coding_to_name__ = {
    '110119000000': '延庆区',
    '110101000000': '东城区',
    '110102000000': '西城区',
    '110105000000': '朝阳区',
    '110106000000': '丰台区',
    '110107000000': '石景山区',
    '110108000000': '海淀区',
    '110109000000': '门头沟区',
    '110111000000': '房山区',
    '110112000000': '通州区',
    '110113000000': '顺义区',
    '110114000000': '昌平区',
    '110115000000': '大兴区',
    '110116000000': '怀柔区',
    '110117000000': '平谷区',
    '110118000000': '密云区',
    '110301000000': '经开区',
    '110401000000': '重点站区管委会'
}


def export_status(num):
    if num == 0:
        name = '否'
    elif num == 1:
        name = '是'
    else:
        name = ''
    return name


#wgs坐标系转高德工具
def transformlat(lng, lat):
    PI = 3.1415926535897932384626
    ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * \
          lat + 0.1 * lng * lat + 0.2 * math.sqrt(abs(lng))
    ret += (20.0 * math.sin(6.0 * lng * PI) + 20.0 *
            math.sin(2.0 * lng * PI)) * 2.0 / 3.0
    ret += (20.0 * math.sin(lat * PI) + 40.0 *
            math.sin(lat / 3.0 * PI)) * 2.0 / 3.0
    ret += (160.0 * math.sin(lat / 12.0 * PI) + 320 *
            math.sin(lat * PI / 30.0)) * 2.0 / 3.0
    return ret

#wgs坐标系转高德工具
def transformlng(lng, lat):
    PI = 3.1415926535897932384626
    ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + \
          0.1 * lng * lat + 0.1 * math.sqrt(abs(lng))
    ret += (20.0 * math.sin(6.0 * lng * PI) + 20.0 *
            math.sin(2.0 * lng * PI)) * 2.0 / 3.0
    ret += (20.0 * math.sin(lng * PI) + 40.0 *
            math.sin(lng / 3.0 * PI)) * 2.0 / 3.0
    ret += (150.0 * math.sin(lng / 12.0 * PI) + 300.0 *
            math.sin(lng / 30.0 * PI)) * 2.0 / 3.0
    return ret

#原始数据转高德
def wgs_to_gaode(lng, lat):
    PI = 3.1415926535897932384626
    ee = 0.00669342162296594323
    a = 6378245.0
    dlat = transformlat(lng - 105.0, lat - 35.0)
    dlng = transformlng(lng - 105.0, lat - 35.0)
    radlat = lat / 180.0 * PI
    magic = math.sin(radlat)
    magic = 1 - ee * magic * magic
    sqrtmagic = math.sqrt(magic)
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
    dlng = (dlng * 180.0) / (a / sqrtmagic * math.cos(radlat) * PI)
    mglat = lat + dlat
    mglng = lng + dlng

    return round(mglng,8), round(mglat,8)


##高德Gcj转百度bd09
def gcj02_to_bd09(gcj02_lng,gcj02_lat):
    x_pi = 3.14159265358979324 * 3000.0 / 180.0
    x = gcj02_lng
    y = gcj02_lat
    z = math.sqrt(x * x + y * y) + 0.00002 * math.sin(y * x_pi)
    theta = math.atan2(y, x) + 0.000003 * math.cos(x * x_pi)
    bd09_Lng = z * math.cos(theta) + 0.0065
    bd09_Lat = z * math.sin(theta) + 0.006
    return round(bd09_Lng,8),round(bd09_Lat,8)

#返回最小级别的coding
def fing_coding(area_coding,street_coding,comm_coding):
    coding=''
    if comm_coding:
        return comm_coding
    if not comm_coding and street_coding:
        return street_coding
    if not comm_coding and not street_coding and area_coding:
        return  area_coding
    return  coding

#根据comm_coding反推街道coding 和area_coding
def check_coding(area_coding,street_coding,comm_coding):
    coding=fing_coding(area_coding,street_coding,comm_coding)
    str_coding=str(coding)
    if not coding or  len(str_coding)!=12:
        return '','',''
    area_=str_coding[6:]
    street_=str_coding[9:]
    if int (street_)>0:  #社区coding
        area_coding=str_coding[0:6]+'000000'
        street_coding=str_coding[0:9]+'000'
        comm_coding=str_coding
    else:
        if int(area_)>0: #街道coding
            area_coding=str_coding[0:6]+'000000'
            street_coding=str_coding
            comm_coding=''
        else:            #区coding
            area_coding=str_coding
            street_coding=''
            comm_coding=''
    return  area_coding,street_coding,comm_coding


def rfid_type_id_filter(type_id):
    type_id_dict={
        '25a7185abf5611eaa8a9000c29d3cc31':'d7904dfa17364eccb4db652116869dd0', #厨余垃圾
        'b84b760ec02a11eaa8a9000c29d3cc31':'9991aedf2c36452b97e89e16ab49e096', #餐厨垃圾
        'b8c900bac02a11eaa8a9000c29d3cc31':'73db639d03bc4e5e9ff7b3c1654a8167', #其他垃圾
        'b88403d4c02a11eaa8a9000c29d3cc31':'ea687db594c04adead315b6656fb2d1a', #可回收物
    }

    return  type_id_dict.get(type_id) or ''


def solid_trans_type_id(base_type_id):
    type_dict={
        '25a7185abf5611eaa8a9000c29d3cc31':0,
        'b8c900bac02a11eaa8a9000c29d3cc31':1,
        'b84b760ec02a11eaa8a9000c29d3cc31':2,
        'b88403d4c02a11eaa8a9000c29d3cc31':3
    }
    gufei_rubbish_type=type_dict.get(base_type_id)
    if gufei_rubbish_type==0:
        return 0
    return gufei_rubbish_type or 2


def deal_appeal_capacity(org_id, bill_factory_id, bill_org_id, car_record_id, capacity, deal_size):
    flow = CarRecordFlow.objects.filter(car_record_id=car_record_id, is_deleted=0)
    record = CarRecord.objects.filter(car_record_id=car_record_id, is_deleted=0)
    car_flow = flow.values('size', 'car_weight', 'capacity_weight', 'bill_weight')
    size = car_flow[0].get('size') if car_flow and car_flow[0] else ''
    capacity_weight = car_flow[0].get('capacity_weight', 0) if car_flow and car_flow[0] else 0
    car_weight = car_flow[0].get('car_weight', 0) if car_flow and car_flow[0] else 0
    car_weight = 0 if not car_weight else car_weight
    capacity_weight = 0 if not capacity_weight else capacity_weight
    bill_weight = car_flow[0].get('bill_weight', 0) if car_flow and car_flow[0] else 0
    bill_weight = 0 if not bill_weight else bill_weight
    bill_org = CarBillOrg.objects.filter(bill_factory_id=bill_factory_id, bill_org_id=bill_org_id,
                                         org_id=org_id, is_deleted=0)
    bill_org.update(bill_weight=F('bill_weight') - bill_weight)
    if size == '120L':
        bill_org.update(capacity_120_weight=F('capacity_120_weight') - capacity_weight,
                        car_120_weight=F('car_120_weight') - car_weight)
    elif size == '240L':
        bill_org.update(capacity_240_weight=F('capacity_240_weight') - capacity_weight,
                        car_240_weight=F('car_240_weight') - car_weight)
    if capacity == 1:  # 1半桶
        if deal_size == '120L':
            bill_org.update(capacity_120_weight=F('capacity_120_weight') + 55, bill_weight=F('bill_weight') + 55)
            flow.update(capacity_weight=55, bill_weight=55, size=deal_size, car_weight=0)
            record.update(weight=55)
        elif deal_size == '240L':
            bill_org.update(capacity_240_weight=F('capacity_240_weight') + 110, bill_weight=F('bill_weight') + 110)
            flow.update(capacity_weight=110, bill_weight=110, size=deal_size, car_weight=0)
            record.update(weight=110)
    elif capacity == 0:  # 0 整桶
        if deal_size == '120L':
            bill_org.update(capacity_120_weight=F('capacity_120_weight') + 110, bill_weight=F('bill_weight') + 110)
            flow.update(capacity_weight=110, bill_weight=110, size=deal_size, car_weight=0)
            record.update(weight=110)
        elif deal_size == '240L':
            bill_org.update(capacity_240_weight=F('capacity_240_weight') + 220, bill_weight=F('bill_weight') + 220)
            flow.update(capacity_weight=220, bill_weight=220, size=deal_size, car_weight=0)
            record.update(weight=220)
    
        
def deal_appeal_car(org_id, bill_factory_id, bill_org_id, car_record_id):
    flow = CarRecordFlow.objects.filter(car_record_id=car_record_id, is_deleted=0)
    record = CarRecord.objects.filter(car_record_id=car_record_id, is_deleted=0)
    car_flow = flow.values('size', 'car_weight', 'bill_weight', 'capacity_weight')
    size = car_flow[0].get('size') if car_flow and car_flow[0] else ''
    car_weight = car_flow[0].get('car_weight', 0) if car_flow and car_flow[0] else 0
    bill_weight = car_flow[0].get('bill_weight', 0) if car_flow and car_flow[0] else 0
    capacity_weight = car_flow[0].get('capacity_weight', 0) if car_flow and car_flow[0] else 0
    car_weight = 0 if not car_weight else car_weight
    capacity_weight = 0 if not capacity_weight else capacity_weight
    bill_weight = 0 if not bill_weight else bill_weight
    bill_org = CarBillOrg.objects.filter(bill_factory_id=bill_factory_id, bill_org_id=bill_org_id,
                                         org_id=org_id, is_deleted=0)
    bill_org.update(bill_count=F('bill_count') - 1, bill_weight=F('bill_weight') - bill_weight)
    if size == '120L':
        bill_org.update(car_120_count=F('car_120_count') - 1)
        bill_org.update(car_120_weight=F('car_120_weight') - car_weight)
        bill_org.update(capacity_120_weight=F('capacity_120_weight') - capacity_weight)
    elif size == '240L':
        bill_org.update(car_240_count=F('car_240_count') - 1)
        bill_org.update(car_240_weight=F('car_240_weight') - car_weight)
        bill_org.update(capacity_240_weight=F('capacity_240_weight') - capacity_weight)
    flow.update(is_deleted=1)
    record.update(is_deleted=1)
    
    
def windingNumber(point, poly):
    """
    点位是否在围栏内判断
    :param point: (float(longitude), float(latitude))  经纬度
    :param poly: 围栏列表
    :return:
    """
    poly.append(poly[0])
    px = point[0]
    py = point[1]
    sum = 0
    length = len(poly) - 1

    for index in range(0, length):
        sx = poly[index][0]
        sy = poly[index][1]
        tx = poly[index + 1][0]
        ty = poly[index + 1][1]

        # 点与多边形顶点重合或在多边形的边上
        if (
            (sx - px) * (px - tx) >= 0
            and (sy - py) * (py - ty) >= 0
            and (px - sx) * (ty - sy) == (py - sy) * (tx - sx)
        ):
            return "on"
        # 点与相邻顶点连线的夹角
        angle = math.atan2(sy - py, sx - px) - math.atan2(ty - py, tx - px)

        # 确保夹角不超出取值范围（-π 到 π）
        if angle >= math.pi:
            angle = angle - math.pi * 2
        elif angle <= -math.pi:
            angle = angle + math.pi * 2
        sum += angle

        # 计算回转数并判断点和多边形的几何关系
    result = "out" if int(sum / math.pi) == 0 else "in"
    return result

def is_tongzhou_other_rubbishes(area_coding, rubbishes):
    """判断是否是通州区非居民其他垃圾类型"""
    # return area_coding == "110112000000" and NonresidentRubbishes.RESTAURANTS not in rubbishes
    return False


def other_rubbishes_db(area_coding, rubbishes):
    """通州区非居民其他垃圾 使用单独数据库"""
    # if is_tongzhou_other_rubbishes(area_coding, rubbishes):
    #     return 'ljfl_declare_db'
    return 'ljfl_db'


def filter_company_with_rubbishes(queryset, transport_company_id, rubbishes=NonresidentRubbishes.RESTAURANTS):
    """根据垃圾类型 过滤清运公司相关的主体"""
    
    if rubbishes == NonresidentRubbishes.RESTAURANTS:
        queryset = queryset.filter(transport_company_id=transport_company_id)
    elif rubbishes == NonresidentRubbishes.OTHER:
        queryset = queryset.filter(other_transport_company_id=transport_company_id)
    elif rubbishes == 'ALL':
        queryset = queryset.filter(Q(transport_company_id=transport_company_id) | Q(other_transport_company_id=transport_company_id))
    else:
        queryset = queryset.filter(transport_company_id=transport_company_id)
    return queryset


def car_filter_rubbishes(queryset, rubbishes):
    """车辆表过滤垃圾类型"""
    if rubbishes:
        if rubbishes == NonresidentRubbishes.OTHER:
            filter_ = {'type_id': ConstRubbishType.OTHER}
        else:
            filter_ = {'type_id__in': [ConstRubbishType.KITCHEN, ConstRubbishType.CHUYU]}
        queryset = queryset.filter(**filter_)
    return queryset


def get_declare_data_from_org_table(org_data, org_detail, rubbishes=NonresidentRubbishes.RESTAURANTS):
    """从org表获取declare表通过审核的数据
    organization 表的数据转换成 org_nonresident_declare 表的数据
    """
    same_fields = [
        'address', 'area_coding', 'comm_coding', 'contacts', 'cover', 'credit_code', 'declare_type', 'is_local_device', 'is_reduce_device', 'latitude',
        'liabler', 'local_capacity', 'local_factory', 'local_out_dregs_weight', 'local_out_solid_weight', 'local_out_water_weight', 'local_process', 'longitude', 'mam_subtype',
        'mam_type', 'name', 'official_address', 'official_org_name', 'org_sub_type_id', 'permission_code', 'phone', 'reduce_oil_device',
        'reduce_water_device', 'restaurant_predict_weight', 'street_coding',
    ]
    diff_fields = {
        NonresidentRubbishes.RESTAURANTS: {},
        NonresidentRubbishes.OTHER: {
            'other_trash_120': 'restaurant_trash_120',
            'other_trash_240': 'restaurant_trash_240',
            'other_trash': 'restaurant_trash',
            'other_trash_rfid': 'restaurant_trash_rfid',
            'other_predict_weight': 'restaurant_predict_weight',
            # 'other_transport_company_id': 'transport_company_id',
            # 'other_have_contract': 'have_contract',
        },
    }
    rubbishes_fields = {
        NonresidentRubbishes.RESTAURANTS: [
            'restaurant_trash_120',
            'restaurant_trash_240',
            'restaurant_trash',
            'restaurant_trash_rfid',
            'restaurant_predict_weight',
            # 'transport_company_id',
            # 'have_contract',
        ],
    }
    same_fields.extend(rubbishes_fields.get(rubbishes, []))
    declare_data = {}
    for field in same_fields:
        declare_data[field] = org_data.get(field)
    
    for org_field, declare_field in diff_fields.get(rubbishes, {}).items():
        declare_data[declare_field] = org_data.get(org_field)
    
    org_detail_fields = ['floor_area', 'loating']
    
    for _field in org_detail_fields:
        declare_data[_field] = org_detail.get(_field)
    
    return declare_data
