#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import time
import uuid
import datetime
import isoweek
import logging
import copy
from math import sin, asin, cos, radians, fabs, sqrt

from Base.utils.cryptor import sm4_cryptor


def get_current_timestamp():
    """
    获取当前时间戳 秒级别
    :return:
    """
    return int(time.time())


def get_uuid_str():
    """
    获取字符类型uuid,不含-
    :return:
    """
    return str(uuid.uuid1()).replace('-', '')


def auto_instance_load(instance, data):
    """
    自动加载model
    :param instance:
    :param data:
    :return:
    """
    for k, v in data.items():
        if hasattr(instance, k):
            setattr(instance, k, v)


def get_timestamp_from_date(day):
    """
    日期转时间戳
    :param day:
    :return:
    """
    day = datetime.datetime.strptime(str(day), '%Y-%m-%d')
    return int(day.timestamp())


def get_timestamp_range(start_day, end_day=None):
    """
    获取时间戳区间
    :param start_day:
    :type start_day: datetime.date
    :param end_day:
    :type end_day: datetime.date or None
    :return:
    """
    if not end_day:
        end_day = start_day + datetime.timedelta(days=1)
    return get_timestamp_from_date(start_day), get_timestamp_from_date(end_day)


def get_coding(coding, grade=3):
    """
    获取对应级别coding,没有则返回空
    :param coding:
    :param grade:
    :return:
    """
    if not coding:
        return ''

    mee = (4 - grade) * 3
    if isinstance(coding, str):
        coding = int(coding)

    coding = int(coding / 10 ** mee) * 10 ** mee

    _grade = 4
    for i in range(3, 0, -1):
        if coding % (10 ** (3 * i)) == 0:
            _grade = 4 - i
            break
    return str(coding) if _grade == grade else ''


def str_to_date(val, fmt='%Y-%m-%d', default=None, offset_days=0):
    """
    日期字符串转日期
    :param val:
    :param fmt:
    :param default:
    :param offset_days: 日期偏移
    :return:
    """
    if not val:
        return default
    try:
        result = datetime.datetime.strptime(val, fmt) + datetime.timedelta(days=offset_days)
        return result.date()
    except Exception as e:
        return default


def str_week_to_start_date(val, offset_weeks=0):
    """
    根据周字符串获取开始日期
    :param val:
    :param offset_weeks:
    :return:
    """
    dt = datetime.datetime.now().date()
    if val:
        try:
            year, week = [int(itm) for itm in val.split('-')]
            dt = isoweek.Week(year, week).monday()
        except Exception as e:
            pass
    dt = dt + datetime.timedelta(offset_weeks * 7)
    year, week, _ = dt.isocalendar()
    start_date = isoweek.Week(year, week).monday()
    return start_date


def str_month_to_start_date(val, offset_months=0):
    """
    根据月字符串获取开始日期
    :param val:
    :param offset_months:
    :type offset_months: 0 or 1
    :return:
    """
    dt = datetime.datetime.now().date().replace(day=1)
    if val:
        try:
            year, month = [int(itm) for itm in val.split('-')]
            dt = datetime.datetime(year, month, 1).date()
        except Exception as e:
            pass
    dt = dt + datetime.timedelta(32 * offset_months)
    return dt.replace(day=1)


def str_quarter_to_start_date(val, offset_quarter=0):
    """
    根据季度字符串获取开始日期
    :param val:
    :param offset_quarter:
    :type offset_quarter: 0 or 1
    :return:
    """
    dt = datetime.datetime.now().date()
    if val:
        try:
            year, quarter = [int(itm) for itm in val.split('-')]
            dt = datetime.datetime(year, (quarter - 1) * 3 + 1, 1).date()
        except Exception as e:
            pass
    year, quarter = dt.year, int((dt.month - 1) / 3) + 1
    if offset_quarter == 1:
        if quarter == 4:
            year += 1
        else:
            quarter += 1
    return datetime.date(year, (quarter - 1) * 3 + 1, 1)


def str_year_to_start_date(val, offset_year=0):
    """
    根据年字符串获取开始日期
    :param val:
    :param offset_year:
    :type offset_year: 0 or 1
    :return:
    """
    dt = datetime.datetime.now().date()
    if val:
        try:
            year = int(val)
            dt = datetime.datetime(year, 1, 1).date()
        except Exception as e:
            pass
    year = dt.year + offset_year
    return datetime.date(year, 1, 1)


def get_logger(logger_name='django'):
    """
    获取日志句柄
    :param logger_name:
    :return:
    """
    return logging.getLogger(logger_name)


#判断qs创建或者修改 参数中有没有该类的属性
def get_model_view(model_class,params):
    params_dict=copy.deepcopy(params)
    filds=[pa_k for pa_k,pa_v in params.items()]
    for fld in filds:
        if not hasattr(model_class, fld):
            del(params_dict[fld])

    return   params_dict


EARTH_RADIUS = 6371  # 地球平均半径，6371km


def get_distance_hav(lat0, lng0, lat1, lng1):
    """
    计算两经纬度之前距离
    :param lat0: 维度0
    :param lng0: 经度0
    :param lat1: 维度1
    :param lng1: 经度1
    :return:
    """
    # 经纬度转换成弧度
    lat0 = radians(lat0)
    lat1 = radians(lat1)
    lng0 = radians(lng0)
    lng1 = radians(lng1)

    dlng = fabs(lng0 - lng1)
    dlat = fabs(lat0 - lat1)
    h = hav(dlat) + cos(lat0) * cos(lat1) * hav(dlng)
    distance = 2 * EARTH_RADIUS * asin(sqrt(h))
    return distance


def hav(theta):
    s = sin(theta / 2)
    return s * s


def get_current_year():
    current = datetime.datetime.now()
    y = current.year
    m = current.month
    if m == 12:
        y += 1
    year_current = str(y) + '年'
    return year_current


def desensitize(content):
    """脱敏 - sm4加解密"""
    cipher = sm4_cryptor.encrypt(content)
    plain = sm4_cryptor.decrypt(cipher)
    # print(cipher, plain, content)
    return cipher


def desensitize_hide_center(content):
    """脱敏 - 中间替换为*"""
    if not content:
        return content
    length = len(content)
    if length > 4:
        content = content[:2] + "*" * (length - 4) + content[-2:]
    return content
