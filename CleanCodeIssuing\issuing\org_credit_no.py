#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from django.core.cache import cache
from django.db import transaction

from Base.utils.const import ConstDeclareType
from ..logger import logger
from .base import BaseCleanCodeGenerator

from CollectManageApp.models_base import TransportCompany, Organization, CityRegion
from CollectManageApp.models import OrgNonresidentDeclare, OtherOrgNonresidentDeclare
from CollectManageApp.models_transport import Company


class OrgCreditNoGenerator(BaseCleanCodeGenerator):
    """
    主体街道虚拟社会统一信用代码生成
    """
    CLEAN_TYPE = 'ORG'
    CLEAN_SUBTYPE = 'CREDIT_NO'

    def generate(self, clean_id):
        # 1. 获取编码前缀
        declare = OrgNonresidentDeclare.objects.filter(declare_id=clean_id, status=1).first()
        if not declare:
            return dict(code=400, msg='主体不存在.')

        if declare.declare_type == ConstDeclareType.NoCredit:
            # 1.1 获取区位编码
            org_region = CityRegion.objects.filter(coding=declare.street_coding, grade=3, is_deleted=0).first()
            if not org_region or not org_region.clean_code:
                return dict(code=400, msg='获取区位编码失败.')
            prefix = org_region.clean_code
        elif declare.declare_type == ConstDeclareType.RepeatCredit:
            prefix = declare.credit_code
        else:
            return dict(code=200, msg='生成成功.', data=dict(credit_no=declare.credit_code))

        clean_code_prefix = f'{prefix}'

        # 2. 锁定编码发号器
        # 2.2 发号逻辑
        credit_no, _ = self._get_clean_code(clean_code_prefix, clean_id,
                                            with_no=False,
                                            code_format='d',
                                            middle_fix='-')

        return dict(code=200, msg='虚拟社会统一信用代码发放成功.', data=dict(
            credit_no=credit_no
        ))

    def other_generate(self, clean_id):
        # 开发区使用
        # 1. 获取编码前缀
        declare = OtherOrgNonresidentDeclare.objects.filter(declare_id=clean_id, status=1).first()
        print("dddeclare",declare)
        if not declare:
            return dict(code=400, msg='主体不存在.')

        if declare.declare_type == ConstDeclareType.NoCredit:
            # 1.1 获取区位编码
            org_region = CityRegion.objects.filter(coding=declare.street_coding, grade=3, is_deleted=0).first()
            if not org_region or not org_region.clean_code:
                return dict(code=400, msg='获取区位编码失败.')
            prefix = org_region.clean_code
        elif declare.declare_type == ConstDeclareType.RepeatCredit:
            prefix = declare.credit_code
        else:
            return dict(code=200, msg='生成成功.', data=dict(credit_no=declare.credit_code))

        clean_code_prefix = f'{prefix}'

        # 2. 锁定编码发号器
        # 2.2 发号逻辑
        credit_no, _ = self._get_clean_code(clean_code_prefix, clean_id,
                                            with_no=False,
                                            code_format='d',
                                            middle_fix='-')

        return dict(code=200, msg='虚拟社会统一信用代码发放成功.', data=dict(
            credit_no=credit_no
        ))