#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import
import os
import sys

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone


class User(AbstractUser):
    """用户（管理员）表"""
    role_id = models.IntegerField(null=True, blank=True, verbose_name=('角色'))
    picture = models.CharField(max_length=160, null=True, blank=True, verbose_name=('图像照片'))
    nickname = models.CharField(max_length=24, null=True, blank=True, verbose_name=('姓名'))
    relation_id = models.CharField(max_length=54, null=True, blank=True, verbose_name=('关联id'))
    user_status = models.IntegerField(default=0, verbose_name=('用户状态，禁用=0，正常=1'))
    company_uid = models.CharField(max_length=50, null=True, blank=True, verbose_name=('公司id'))
    create_time = models.DateTimeField(default=timezone.now, verbose_name=('创建时间'))
    update_time = models.DateTimeField(auto_now=True, verbose_name=('修改时间'))

    class Meta:
        db_table = 'user'
        verbose_name = "用户信息"
        verbose_name_plural = verbose_name
        app_label = "other_transport_db"


class Company(models.Model):
    """公司信息"""
    company_id = models.CharField(max_length=45, verbose_name="唯一id")
    name = models.CharField(blank=True, null=True, max_length=50, verbose_name='名称')
    admin = models.CharField(blank=True, null=True, max_length=32, verbose_name='负责人/法人姓名')
    id_card = models.CharField(blank=True, null=True, max_length=32, verbose_name='负责人身份证')
    phone = models.CharField(blank=True, null=True, max_length=32, verbose_name='负责人联系电话')
    address = models.CharField(blank=True, null=True, max_length=120, verbose_name='地址, 注册地址')
    credit_code = models.CharField(blank=True, null=True, max_length=32, verbose_name='社会统一信用代码')
    company_type = models.CharField(blank=True, null=True, max_length=5, verbose_name='企业类型 401')
    service_area = models.CharField(blank=True, null=True, max_length=120, verbose_name='服务区域')
    email = models.CharField(blank=True, null=True, max_length=120, verbose_name='邮箱')
    company_picture = models.TextField(blank=True, null=True, verbose_name='营业执照')
    quali_file = models.TextField(blank=True, null=True, verbose_name='资质附件照片')
    contract_file = models.TextField(blank=True, null=True, verbose_name='合同附件照片')
    c_area_coding = models.CharField(max_length=45, blank=True, null=True, verbose_name='注册区域ID')
    c_street_coding = models.CharField(max_length=45, blank=True, null=True, verbose_name='注册街道coding')
    c_street_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='注册街道名称')
    c_comm_coding = models.CharField(max_length=45, blank=True, null=True, verbose_name='注册街道coding')
    c_comm_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='注册街道名称')
    rubbish_type = models.CharField(max_length=255, null=True, blank=True, default='', verbose_name='垃圾类型id')
    rubbish_type_name = models.CharField(max_length=255, null=True, blank=True, default='', verbose_name='垃圾类型名称')
    viald_time = models.CharField(max_length=45, blank=True, null=True, verbose_name=u'证书有效期')
    approval_author = models.CharField(blank=True, null=True, max_length=50, default='', verbose_name='行政许可审批机关')
    license_number = models.CharField(blank=True, null=True, max_length=25, default='', verbose_name='行政许可证号')
    office_address = models.CharField(blank=True, null=True, max_length=120, default='', verbose_name='办公地址')
    operate_address = models.CharField(blank=True, null=True, max_length=120, default='', verbose_name='运营地址')
    status_type = models.IntegerField(default=1, verbose_name='审核类型：1新增, 2变更, 3注销申请')
    create_time = models.DateTimeField(default=timezone.now, verbose_name=u'创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name=u'编辑时间')
    is_delete = models.IntegerField(default=0, verbose_name='删除状态  0 未删除  1删除')
    add_status = models.IntegerField(default=0, verbose_name='添加账号，信息：0 否 1 是')
    clean_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='公司代码')
    clean_no = models.CharField(max_length=12, blank=True, null=True, verbose_name='公司代码简写')
    status = models.IntegerField(default=0, verbose_name='审核状态：0 未通过， 1已通过')

    class Meta:
        db_table = 'company'
        ordering = ['-id']
        verbose_name = u"公司信息"
        app_label = "other_transport_db"


class CompanyArea(models.Model):
    """公司服务区域"""
    transport_company_area_id = models.CharField(max_length=45, blank=True, null=True, default='')
    area_coding = models.CharField(max_length=45, blank=True, null=True, verbose_name='区域ID')
    street_coding = models.TextField(blank=True, null=True, verbose_name='街道coding')
    street_name = models.TextField(blank=True, null=True, verbose_name='街道名称')
    company_id = models.CharField(max_length=45, verbose_name='公司id')
    enter_type = models.IntegerField(default=0, verbose_name=('1入围'))  # 1入围
    enter_time = models.DateTimeField(blank=True, null=True, verbose_name=(u'入围审核不通过时间'))
    selected_type = models.IntegerField(default=0, verbose_name=('1入选'))
    selected_time = models.DateTimeField(blank=True, null=True, verbose_name=(u'入选时间'))
    no_reject = models.CharField(blank=True, null=True, max_length=200, verbose_name=('不通过原因'))
    enter_operater = models.CharField(blank=True, null=True, max_length=32, verbose_name=('审核操作人'))
    created_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_time = models.DateTimeField(auto_now=True, blank=True, null=True, verbose_name='更新时间')
    is_delete = models.BooleanField(default=False, verbose_name='是否删除')
    status = models.IntegerField(default=1, verbose_name=('审核状态：1待处理，2未通过， 3已通过'))
    clean_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='公司代码')
    clean_no = models.CharField(max_length=12, blank=True, null=True, verbose_name='公司代码简写')

    class Meta:
        db_table = 'company_area'
        verbose_name = u"公司服务区域"
        app_label = "other_transport_db"