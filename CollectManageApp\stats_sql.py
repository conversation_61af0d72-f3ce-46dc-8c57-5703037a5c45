#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import
import os
import sys
other_rubbish_register_stats = """
        SELECT
                city. NAME as 所属区,
                IFNULL(org.org_count,0) 非居民其他单位数量,
                -- org.group_count as 集团用户,
                -- org.second_count as 二级用户,
                IFNULL(org.other_have_contract_count,0) 合同上传数量,
                IFNULL(c.c_count,0) as '备案运输单位（家）',
                IFNULL(car_s.car_count,0) '运输车辆（辆）',
                IFNULL(clean.count,0) '运营垃圾楼（座）'
        FROM
                city_region AS city
        LEFT JOIN (
                SELECT
                        o.area_coding,
                        count(*) AS org_count,
                        SUM(o.other_have_contract) AS other_have_contract_count,
                        SUM(IFNULL(group_stats.group_count,0)) AS group_count,
                        SUM(IFNULL(group_stats.second_count,0)) AS second_count
        
                FROM
                    organization as o
                LEFT JOIN (
                    SELECT
                        org_group_id,
                        1 as group_count,
                        count(*) AS second_count
                    FROM
                        ljfl_declare_db.org_group
                    WHERE
                        STATUS = 1
                    AND logout_status = 0
                    AND rubbishes = "OTHER"
                    GROUP BY
                        org_group_id
                ) as group_stats on group_stats.org_group_id = o.org_id
                WHERE
                        o.is_deleted = 0
                AND o.is_declare = 1
                AND o.org_type_id = '60b5ef4bef5311ebbe73fa163e3babe8'
                AND o.logout_status = 0
                AND o.org_id NOT IN (
                        SELECT
                                org_id
                        FROM
                                ljfl_declare_db.org_group
                        WHERE
                                STATUS = 1
                        AND logout_status = 0
                )
                AND o.rubbishes LIKE "%OTHER%"
                GROUP BY o.area_coding
        ) as org ON city.coding = org.area_coding
        LEFT JOIN (SELECT area_coding,count(DISTINCT(transport_company_id)) as c_count from transport_company_area where is_deleted=0 and rubbishes= "OTHER" GROUP BY area_coding) as c on c.area_coding=city.coding
        LEFT JOIN (SELECT area_coding,count(car_num) as car_count  FROM car  where standing_book=1 and is_declare=1 and type_id = "b8c900bac02a11eaa8a9000c29d3cc31" GROUP BY area_coding) as car_s on car_s.area_coding = city.coding
        LEFT JOIN (SELECT area_coding,count(DISTINCT(cleaning_point_id)) as count  FROM cleaning_point  where  is_deleted in (0,1) and hidden=0 and transport_company_id <> "" GROUP BY area_coding) as clean on clean.area_coding = city.coding
        
        
        WHERE
        NAME IN (
                '东城区',
                '西城区',
                '朝阳区',
                '海淀区',
                '丰台区',
                '石景山区',
                '通州区',
                '门头沟区',
                '房山区',
                '顺义区',
                '昌平区',
                '大兴区',
                '怀柔区',
                '平谷区',
                '密云区',
                '延庆区',
                '经开区'
        )
        ORDER BY
        FIELD(
                NAME,
                '东城区',
                '西城区',
                '朝阳区',
                '海淀区',
                '丰台区',
                '石景山区',
                '通州区',
                '门头沟区',
                '房山区',
                '顺义区',
                '昌平区',
                '大兴区',
                '怀柔区',
                '平谷区',
                '密云区',
                '延庆区',
                '经开区'
        );
        """