from __future__ import absolute_import

import json
import logging
import random
import time
import datetime
import uuid

from django.forms import model_to_dict
import requests
from django.core.cache import cache
from django.db.models.aggregates import Count
from django.db.models.expressions import RawSQL
from django.db.models import Count, Q
from django.conf import settings
from django.db import connections

from Base.utils.base import get_current_year
from CollectManageApp.auto_audit_organization_declare import audit
from CollectManageApp.models import OrgNonresidentDeclare, NonresidentOrder, NonresidentOrderWarning, \
    NonresidentStatist, NonresidentPayOrder, AuthorizedOrg, AuthorizedOrgRelationId, OtherOrgNonresidentDeclare, \
    QuotaEditRecord, OrgNonresidentQuota

from CollectManageApp.models_base import OrgGroup, OrgGroupBase, TerminalRecord, Organization, TransportCompany, TransportCompanyArea, Car, \
    AppealRecod, AppealDetailRecod, CarRecord, CarBillOrg, OrganizationOther
from CleanCodeIssuing import issuing
from redis.exceptions import LockError

from CollectManageApp.models_transport import ContractNew, PayConfig
from CollectManageApp.scripts import deal_appeal_capacity, deal_appeal_car
from CollectManageApp.views import create_pay_order



def close_old_connections():
    """
    关闭不可用mysql连接
    :return:
    """
    for conn in connections.all():
        conn.close_if_unusable_or_obsolete()


def handle_db_connections(func):
    """
    自动管理Mysql连接
    :param func:
    :return:
    """

    def func_wrapper(*args, **kwargs):
        close_old_connections()
        result = func(*args, **kwargs)
        close_old_connections()
        return result

    return func_wrapper


def get_logger(logger_name='django'):
    """
    获取日志句柄
    :param logger_name:
    :return:
    """
    return logging.getLogger(logger_name)


logger = get_logger('scheduler')


def timed_task_update_terminal_record_status():
    """
    定时任务修改清运记录确认状态
    :return:
    """
    try:
        with cache.lock('scheduler_update_terminal_record_status', blocking_timeout=0.1):
            cache.expire('scheduler_update_terminal_record_status', 300)
            handler = update_terminal_record_status()
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('scheduler_update_terminal_record_status', 0)


@handle_db_connections
def update_terminal_record_status():
    """
    确认过期的状态
    """
    now_time = int(time.time())
    terminal_record = TerminalRecord.objects.filter(is_deleted=0, expiration_time__lte=now_time, confirm_status=0)
    try:
        terminal_record.update(**dict(confirm_status=1, confirm_time=now_time, grade_score=5))
    except Exception as e:
        logger.info('修改失败')
        logger.exception(e)


@handle_db_connections
def clean_code_no_generate():
    logger.info('clean_code_no_generate开始')
    try:
        with cache.lock('scheduler_clean_code_no_generate', blocking_timeout=0.1):
            cache.expire('scheduler_clean_code_no_generate', 300)
            # 收运公司
            logger.info('收运公司开始')
            company = TransportCompany.objects.filter(is_declare=1, is_deleted=0, clean_code='').order_by('id').all()
            for c in company:
                issuing.get_clean_code('ORG', 'TRANSPORT', c.transport_company_id)

            # 收运公司-收运范围
            logger.info('收运公司-收运范围开始')
            area = TransportCompanyArea.objects.filter(is_deleted=0, clean_code='').order_by('id').all()
            for a in area:
                issuing.get_clean_code('ORG', 'TRANSPORT-AREA', a.transport_company_area_id)

            # 收运公司-车
            logger.info('收运公司-车开始')
            car = Car.objects.filter(is_declare=1, is_deleted=0, clean_code='').order_by('id').all()
            for c in car:
                issuing.get_clean_code('ATTACH', 'CAR', c.car_id)

            # 非居民责任主体排放登记编码发放
            logger.info('非居民责任主体排放登记编码发放开始')
            # 20230602改为二级用户不生成排放等级编码
            org = Organization.objects.filter(is_declare=1, is_deleted=0, clean_code='', logout_status=0).exclude(
                source_type='SECOND').order_by('id').all()
            for o in org:
                issuing.get_clean_code('ORG', 'NONRESIDENT', o.org_id)

            # 非居民责任主体-其他垃圾排放登记编码发放
            # logger.info('非居民责任主体-其他垃圾排放登记编码发放开始')
            # org = Organization.objects.using("ljfl_declare_db").filter(is_declare=1, is_deleted=0, clean_code='', logout_status=0).exclude(
            #     source_type='SECOND').order_by('id').all()
            # for o in org:
            #     issuing.get_clean_code('ORG', 'NONRESIDENT', o.org_id)
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('scheduler_clean_code_no_generate', 0)
    logger.info('clean_code_no_generate结束')


@handle_db_connections
def organization_declare_auto_audit():
    try:
        with cache.lock('scheduler_organization_declare_auto_audit', blocking_timeout=0.1):
            cache.expire('scheduler_organization_declare_auto_audit', 300)

            declares = OrgNonresidentDeclare.objects.filter(status=1, auto_audit=1).all()
            for d in declares:
                data = dict(
                    declare_id=d.declare_id,
                    status=2,
                    reason='自动审核通过'
                )
                role = 'Interface'
                relation_id = '20dd6806784147009847639c5d722cc3'
                username = 'interface'
                area_coding = ''
                street_coding = ''
                audit(data, role, area_coding, street_coding, relation_id, username, True)
                pass
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('scheduler_organization_declare_auto_audit', 0)


def timed_task_update_appeal_status():
    """
    定时任务修改24小时自动审核通过
    :return:
    """
    try:
        with cache.lock('scheduler_update_appeal_status', blocking_timeout=0.1):
            cache.expire('scheduler_update_appeal_status', 300)
            handler = update_appeal_status()
            update_re_appeal()
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('scheduler_update_appeal_status', 0)


@handle_db_connections
def update_appeal_status():
    """
    非居民申诉24小时自动审核通过
    """
    confirm_appeal = AppealRecod.objects.filter(is_deleted=0, status__in=[0, 1])
    current_time = datetime.datetime.now()
    for appeal in confirm_appeal:
        appeal_id = appeal.appeal_id
        org_id = appeal.org_id
        bill_factory_id = appeal.bill_factory_id
        bill_org_id = appeal.bill_org_id
        record = AppealDetailRecod.objects.filter(appeal_id=appeal_id)
        if appeal.status == 0:
            appeal_time = appeal.appeal_time
            confirm_appeal_time = appeal_time + datetime.timedelta(hours=24)
            if current_time >= confirm_appeal_time:
                record = record.filter(company_deal_status=0)
                for info in record:
                    a_type = info.a_type
                    car_record_id = info.car_record_id
                    if a_type == 0:
                        deal_appeal_capacity(org_id, bill_factory_id, bill_org_id, car_record_id, info.capacity,
                                             info.size)
                    elif a_type == 1:
                        deal_appeal_car(org_id, bill_factory_id, bill_org_id, car_record_id)
                    record.filter(car_record_id=car_record_id).\
                        update(company_confirm_time=current_time, company_confirmer='自动确认', company_deal_status=1)
                confirm_appeal.filter(appeal_id=appeal_id).update(status=2, company_deal_time=datetime.datetime.now())
        elif appeal.status == 1:
            re_appeal_time = appeal.re_appeal_time
            confirm_appeal_time = re_appeal_time + datetime.timedelta(hours=24)
            if current_time >= confirm_appeal_time:
                record = record.filter(appeal_id=appeal_id, area_deal_status=1)
                for info in record:
                    a_type = info.a_type
                    car_record_id = info.car_record_id
                    if a_type == 0:
                        deal_appeal_capacity(org_id, bill_factory_id, bill_org_id, car_record_id, info.capacity,
                                             info.size)
                    elif a_type == 1:
                        deal_appeal_car(org_id, bill_factory_id, bill_org_id, car_record_id)
                    record.filter(car_record_id=car_record_id). \
                        update(area_confirm_time=current_time, area_confirmer='自动确认', area_deal_status=2)
                confirm_appeal.filter(appeal_id=appeal_id).update(status=3)


@handle_db_connections
def update_re_appeal():
    """
    非居民申诉24小时是否可以再次申诉
    """
    confirm_appeal = AppealRecod.objects.filter(is_deleted=0, status=2, re_appeal=1)
    current_time = datetime.datetime.now()
    for appeal in confirm_appeal:
        appeal_id = appeal.appeal_id
        confirm_appeal_time = appeal.company_deal_time + datetime.timedelta(hours=24)
        if current_time >= confirm_appeal_time:
            confirm_appeal.filter(appeal_id=appeal_id).update(re_appeal=2)



@handle_db_connections
def order_warning(now=datetime.datetime.now()):
    """
    非居民调度订单预警
    """
    queryset = NonresidentOrder.objects.filter(is_deleted=0, status__in=[0, 1, 2])
    res_list = list()
    for item in queryset:
        days = (now - item.create_time).days
        if days >= 1:
            objs = NonresidentOrderWarning.objects.filter(order_id=item.order_id, warning_date=now.date())
            if objs:
                continue
            insert_data = dict()
            insert_data['uuid'] = uuid.uuid4().hex
            insert_data['order_id'] = item.order_id
            insert_data['area_coding'] = item.area_coding
            insert_data['street_coding'] = item.street_coding
            insert_data['comm_coding'] = item.comm_coding
            insert_data['org_id'] = item.org_id
            insert_data['apply_clean_time'] = item.apply_clean_time
            insert_data['order_create_time'] = item.create_time
            insert_data['warning_date'] = now.date()
            data = NonresidentOrderWarning(**insert_data)
            res_list.append(data)
    NonresidentOrderWarning.objects.bulk_create(res_list)


def timed_task_order_warning():
    """
    非居民调度订单预警 定时任务
    :return:
    """
    try:
        with cache.lock('timed_task_order_warning', blocking_timeout=0.1):
            cache.expire('timed_task_order_warning', 300)
            order_warning()
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('timed_task_order_warning', 0)

@handle_db_connections
def handle_apply_clean_order():
    """
    非居民调度订单关联收运记录
    """
    # 调度订单关联收运记录
    for item in NonresidentOrder.objects.filter(~Q(car_record_id=''), is_deleted=0, status__in=[2]):
        car_record_id = item.car_record_id
        if item.org_id and item.car_num and item.apply_clean_time:
            car_record_queryset = CarRecord.objects.annotate(
                date=RawSQL('DATE(FROM_UNIXTIME(create_time))', [])).filter(is_deleted=0,
                                                                            org_id=item.org_id,
                                                                            car_num=item.car_num,
                                                                            date=item.apply_clean_time,
                                                                            type_id=item.rubbish_type_id
                                                                            )
            car_record_id_new = [i.car_record for i in car_record_queryset]
            if car_record_id_new and car_record_id != car_record_id_new:
                # 更新 调度订单 car_record_id
                item.car_record_id = car_record_id_new
                item.save()


def timed_task_handle_apply_clean_order():
    """
    处理非居民调度订单
    :return:
    """
    try:
        with cache.lock('timed_task_handle_apply_clean_order', blocking_timeout=0.1):
            cache.expire('timed_task_handle_apply_clean_order', 300)
            handle_apply_clean_order()
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('timed_task_handle_apply_clean_order', 0)
        

@handle_db_connections
def organization_statist():
    try:
        with cache.lock('scheduler_organization_statist', blocking_timeout=0.1):
            cache.expire('scheduler_organization_statist', 300)
            current = datetime.datetime.now()
            create_date = current - datetime.timedelta(days=1)
            end_time = datetime.datetime.strftime(current, '%Y-%m-%d')
            timeArray_de = time.strptime(end_time, "%Y-%m-%d")
            timestamp_de = time.mktime(timeArray_de)
            org_obj = Organization.objects.filter(is_declare=1, org_type_id='60b5ef4bef5311ebbe73fa163e3babe8',
                                                  create_time__lt=timestamp_de, is_deleted=0, logout_status=0)
                
            org_obj = org_obj.filter(is_group_second=0).values('area_coding').\
                annotate(count=Count('org_id')).values('area_coding', 'count')
            org_list = []
            for org in org_obj:
                org_count = org.get('count')
                area_coding = org.get('area_coding')
                if not area_coding:
                    continue
                if not NonresidentStatist.objects.filter(create_date=create_date, area_coding=area_coding).exists():
                    org_list.append(NonresidentStatist(count=org_count, create_date=create_date, area_coding=area_coding))
            NonresidentStatist.objects.bulk_create(org_list)
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('scheduler_organization_declare_auto_audit', 0)


def timed_task_non_bill_confirm():
    """
    非居民收运联单24小时自动确认,生成代付款订单
    :return:
    """
    try:
        with cache.lock('scheduler_non_bill_confirm', blocking_timeout=0.1):
            cache.expire('scheduler_non_bill_confirm', 300)
            logger.info("scheduler_non_bill_confirm start")
            car_bill_org_confirm()

    except Exception as e:
        logger.exception(e)
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('scheduler_non_bill_confirm', 0)
    logger.info("scheduler_non_bill_confirm end")


@handle_db_connections
def car_bill_org_confirm():
    """
    非居民收运联单自动确认,生成代付款订单
    """

    current_timestamp = int(datetime.datetime.now().timestamp()) - 60 * 60 * 24
    queryset = CarBillOrg.objects.filter(is_deleted=0, is_confirm=0, finish_time__lte=current_timestamp)
    ENVIRONMENT = settings.ENVIRONMENT
    for index,bill in enumerate(queryset):
        bill_factory_id = bill.bill_factory_id
        bill_org_id = bill.bill_org_id
        org_confirm = CarBillOrg.objects.filter(is_deleted=0, bill_factory_id=bill_factory_id,
                                                bill_org_id=bill_org_id, is_confirm=1).first()
        if org_confirm:
            continue
        finish_time = bill.finish_time
        if current_timestamp >= finish_time:
            queryset.filter(bill_org_id=bill_org_id, bill_factory_id=bill_factory_id).\
                update(is_confirm=1, confirm_time=current_timestamp, remark="自动确认.")
            # if ENVIRONMENT in ['production', 'production_local']:
            #     continue
            org_id = bill.org_id
            contract = ContractNew.objects.using('transport_db').filter(is_delete=0, status=3,
                                                                        contract_status__in=(1, 4),
                                                                        contract_picture__isnull=False,
                                                                        org_id=org_id). \
                values('pay_config_id', 'company_id', 'company_name').first()
            pay_config_id = contract.get('pay_config_id') if contract else ''
            transport_company_id = contract.get('company_id') if contract else ''
            company_name = contract.get('company_name') if contract else ''
            pay_config = PayConfig.objects.filter(is_deleted=0, status=0, pay_config_id=pay_config_id). \
                values('pay_type', 'charge_rules', 'charge_price').first()
            if pay_config:
                pay_type_id = pay_config.get('pay_type') if pay_config else ''
                if pay_type_id == 0:
                    pay_type = '预付金额'
                elif pay_type_id == 1:
                    pay_type = '即时支付'
                elif pay_type_id == 2:
                    pay_type = '公对公支付'
                else:
                    pay_type = ''
                charge_rules_id = pay_config.get('charge_rules')
                if charge_rules_id == 0:
                    charge_rules = '次缴费'
                    pay_price = pay_config.get('charge_price') * 100
                    content = '%s元/次' % pay_config.get('charge_price')
                elif charge_rules_id == 1:
                    charge_rules = '重量缴费'
                    pay_price = float(pay_config.get('charge_price')) * bill.bill_weight * 100
                    content = '%s元/kg' % pay_config.get('charge_price')
                else:
                    charge_rules = ''
                    pay_price = 0
                    content = ''
                if charge_rules_id != 2:
                    create_pay_order(pay_price, org_id, transport_company_id, pay_type, charge_rules, pay_config_id,
                                     company_name, content, bill_factory_id, bill_org_id)


def create_bill_order(obj, obj_list):
    confirm_time = datetime.datetime.now()
    date_order = datetime.datetime.strftime(confirm_time, "%Y%m%d")
    ran = random.sample('0123456789', 5)
    order_num = 'fjmjf' + str(date_order) + ''.join(ran)
    bill_factory_id = obj.bill_factory_id
    bill_org_id = obj.bill_org_id
    pay_price = (obj.bill_weight / 1000) * 30000
    org_id = obj.org_id
    car_num = obj.car_num
    car = Car.objects.filter(car_num=car_num).values_list('transport_company_id', flat=True)
    transport_company_id = car[0] if car and car[0] else ''
    obj_list.append(NonresidentPayOrder(order_num=order_num, bill_factory_id=bill_factory_id, bill_org_id=bill_org_id,
                                        confirm_time=confirm_time, pay_price=pay_price, org_id=org_id,
                                        transport_company_id=transport_company_id))
    return obj_list


def timed_task_non_pay_info():
    """
    非居民待支付超过1分钟，消息推送
    :return:
    """
    try:
        with cache.lock('scheduler_send_info_pay', blocking_timeout=0.1):
            cache.expire('scheduler_send_info_pay', 300)
            ENVIRONMENT = settings.ENVIRONMENT
            # if ENVIRONMENT not in ['production']:
            send_info_pay()
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('scheduler_send_info_pay', 0)


@handle_db_connections
def send_info_pay():
    logger.info('待支付推送消息')
    orders = NonresidentPayOrder.objects.filter(is_deleted=0, status=0, send_info=0)
    current_time = datetime.datetime.now()
    for order in orders:
        confirm_time = order.confirm_time
        if confirm_time + datetime.timedelta(minutes=2) < current_time:
            logger.info('待支付推送消息:%s' % order.order_num)
            t_company = TransportCompany.objects.filter(transport_company_id=order.transport_company_id). \
                values_list('company', flat=True)
            company_name = t_company[0] if t_company and t_company[0] else ''
            get_send(order.order_num, company_name, confirm_time, order.org_id, '(0.3元/kg)')
            NonresidentPayOrder.objects.filter(is_deleted=0, status=0, send_info=0,
                                               order_num=order.order_num).update(send_info=1)


def get_send(order_num, company_name, confirm_time, org_id, content):
    appid = 'wx03651d5d9b9d7f08'
    appSecret = 'ec9a2c53ff752dd9e2fa14ca3ae4aad8'
    template_id = 'mhzjZqCpd2Rhmn65WK7F7SR_C8f61q-qikSumi0y-qQ'
    token = cache.get('non_pay_access_token')
    url_access_token = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' + appid + '&secret=' + appSecret
    if not token:
        tokenRes = requests.get(url_access_token)
        token = json.loads(tokenRes.text).get('access_token')
        cache.set('non_pay_access_token', token)
    auth_org_queryset = AuthorizedOrg.objects.all()
    auth_mapping = AuthorizedOrgRelationId.objects.filter(org_id=org_id, is_admin=1).first()
    auth_org = auth_org_queryset.filter(relation_id=auth_mapping.authorized_org_relation_id).first()
    open_id = auth_org.open_id
    logger.info('待支付推送open_id:%s' % open_id)
    confirm_time = datetime.datetime.strftime(confirm_time, "%Y-%m-%d %H:%M:%S")
    url_msg = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s' % token
    body = {
        "touser": open_id,  # open_id
        "template_id": template_id,
        "page": "pages/billPay/billPay",  # 点击模板卡片后的跳转页面
        "miniprogram_state": "trial",  # developer为开发版；trial为体验版；formal为正式版；默认为正式版
        "lang": "zh_CN",
        "data": {
            "character_string15": {
                "value": order_num
            },
            "date9": {
                "value": confirm_time
            },
            "thing3": {
                "value": "非居民计量收费"
            },
            "thing7": {
                "value": company_name
            },
            "thing16": {
                "value": "非居民餐厨垃圾计量收费%s" % content
            },
        }
    }
    res = requests.post(url=url_msg, data=json.dumps(body))  # 商品描述
    logger.error('待支付推送消息结果coding:%s' % json.loads(res.text).get('errcode'))
    if json.loads(res.text).get('errcode') == 40001:
        tokenRes = requests.get(url_access_token)
        token = json.loads(tokenRes.text).get('access_token')
        url_msg = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s' % token
        cache.set('non_pay_access_token', token)
        res = requests.post(url=url_msg, data=json.dumps(body))
    logger.error('待支付推送消息结果:%s' % res.text)
    print(res.text, 33333333333333333333)  # 非居民餐厨垃圾计量收费，收费标准0.3元/kg


@handle_db_connections
def other_clean_code_no_generate():
    try:
        with cache.lock('other_scheduler_clean_code_no_generate', blocking_timeout=0.1):
            cache.expire('other_scheduler_clean_code_no_generate', 300)

            # 其他垃圾 非居民责任主体排放登记编码发放
            org = OrganizationOther.objects.filter(Q(Q(clean_code='') | Q(clean_code=None)), is_declare=2,
                                                   is_deleted=0).order_by('id').all()
            for o in org:
                issuing.get_other_clean_code('ORG', 'NONRESIDENT', o.org_id)
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('other_scheduler_clean_code_no_generate', 0)


@handle_db_connections
def other_nonresident_auto_failed():
    logger.info("other_nonresident_auto_failed start")
    try:
        """其他垃圾非居民选择未注册的清运公司 72小时候后自动审核失败"""
        now_time = datetime.datetime.now().timestamp()

        end_time = now_time - 72 * 60 * 60
        objs = OtherOrgNonresidentDeclare.objects.filter(
            Q(Q(transport_company_id="") | Q(transport_company_id__isnull=True)),
            check_type=0,
            status__in=[0, 1],
            create_time__lt=end_time,
        )
        for item in objs:
            company_info = json.loads(item.transport_company_info) if item.transport_company_info else {}
            company = company_info.get("company")
            item.status = 3
            item.audit_time = now_time
            item.auditor_username = "系统自动审核"
            item.reason = f"您选择的收运公司'{company}'未注册请重新申请"
            item.save()
    except Exception as e:
        logger.exception(f"other_nonresident_auto_failed error:{e}")
    finally:
        logger.info("other_nonresident_auto_failed end")


def timed_task_quota_appeal():
    """
    7天未处理自动定额完成
    :return:
    """
    try:
        with cache.lock('scheduler_quota_appeal', blocking_timeout=0.1):
            cache.expire('scheduler_quota_appeal', 300)
            handler = quota_appeal()
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('scheduler_quota_appeal', 0)


@handle_db_connections
def quota_appeal():
    """
    7天未处理自动定额完成
    """
    year_current = get_current_year()
    last_time = datetime.datetime.now() - datetime.timedelta(days=7)
    
    quota_query = OrgNonresidentQuota.objects.filter(quota_date=year_current, status=1,
                                                     is_deleted=0, create_time__lt=last_time)
    
    for query in quota_query:
        OrgNonresidentQuota.objects.filter(quota_id=query.quota_id, is_deleted=0).update(status=4)
        QuotaEditRecord.objects.filter(quota_id=query.quota_id).update(status=1)


def timed_task_month_pay():
    """
    月付款非居民每月一号生成待支付月付款订单
    :return:
    """
    try:
        with cache.lock('scheduler_month_pay', blocking_timeout=0.1):
            cache.expire('scheduler_month_pay', 300)
            # ENVIRONMENT = settings.ENVIRONMENT
            # if ENVIRONMENT not in ['production']:
            non_org_month_pay()
    except Exception as e:
        if isinstance(e, LockError):
            pass
        else:
            logger.exception(e)
    finally:
        cache.expire('scheduler_month_pay', 0)


@handle_db_connections
def non_org_month_pay():
    pay_config = PayConfig.objects.filter(is_deleted=0, status=0, charge_rules=2). \
        values('pay_type', 'pay_config_id', 'charge_price')
    config_dict = {config.get('pay_config_id'): [config.get('pay_type'), config.get('charge_price')]
                   for config in pay_config}
    pay_config_ids = config_dict.keys()
    month_pays = ContractNew.objects.using('transport_db').filter(is_delete=0, status=3, contract_status__in=(1, 4),
                                                                  contract_picture__isnull=False,
                                                                  pay_config_id__in=list(pay_config_ids)). \
        values('pay_config_id', 'org_id', 'company_name', 'company_id')
    print(month_pays, 33333333333333)
    for org in month_pays:
        org_id = org.get('org_id')
        transport_company_id = org.get('company_id')
        company_name = org.get('company_name')
        pay_config_id = org.get('pay_config_id')
        config = config_dict.get(pay_config_id) if config_dict else []
        pay_price = config[1] if config else 0
        pay_type = config[0] if config else 0
        if pay_type == 0:
            pre_pay_type = '预付金额'
        elif pay_type == 1:
            pre_pay_type = '即时支付'
        elif pay_type == 2:
            pre_pay_type = '公对公支付'
        else:
            pre_pay_type = ''
        charge_rules = '包月'
        content = '%s/月' % pay_price
        bill_factory_id = ''
        bill_org_id = ''
        create_pay_order(pay_price * 100, org_id, transport_company_id, pre_pay_type, charge_rules, pay_config_id,
                         company_name, content, bill_factory_id, bill_org_id)



@handle_db_connections
def org_group_tranfer():
    org_groups = list(OrgGroup.objects.values_list("id", flat=True))
    OrgGroupBase.objects.exclude(id__in=org_groups).delete()
    for og in OrgGroup.objects.all():
        data = model_to_dict(og, exclude=("id", ))
        OrgGroupBase.objects.update_or_create(id=og.id, defaults=data)