import datetime
import time

import django_filters
from django.db.models import Q
from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON>, NumberFilter

from Base.utils.base import get_current_year
from Base.utils.const import BOTH_NONRESIDENT_RUBBISHES, NonresidentRubbishes
from CollectManageApp.models_base import CleaningPoint, PendingCompanyRegistration, TerminalRecord, TransportCompany, \
    Car, Organization, OrgRfid, RfidType, \
    RubbishType, TransportCompanyArea, TransportContract, CarRecord, CarRecordOrg, OpinionDetails, ApplyStationRecod, \
    AppealRecod, \
    CarRecordFlow, NonResidentBase, NonResidentLogout, VirtualAreaStreet, OrgGroup, OrganizationOther, RtuDevice, \
    CleaningPointRecord, CleaningPointTransRecord, CleaningPointRecordFlow, CityRegion
from CollectManageApp.models import NonresidentOrderWarning, NonresidentOrder, OrgNonresidentDeclare, \
    MyNonresidentAppointmentCollection, NonresidentPayOrder, OtherOrgNonresidentDeclare, OrgNonresidentQuota, \
    AppointmentRecycling, TrashCanAlarmRecord, OilWaterSeparationDeviceRecord, ResidentRecord, Resident, ResidentAccount
from CollectManageApp.models_solid import RepairWorkingOrder
from CollectManageApp.models_transport import ContractNew
from dateutil.relativedelta import relativedelta

from CollectManageApp.scripts import car_filter_rubbishes



'''清运记录'''


def org_name_filter(queryset, name, value):
    """
    主体名过滤
    """

    org = Organization.objects.filter(name=value)
    org_ids = set([i.org_id for i in org if i])
    if org_ids:
        queryset = queryset.filter(org_id__in=org_ids)
    else:
        queryset = queryset.none()
    return queryset


def org_name_search(queryset, name, value):
    """
    主体名查询
    """

    org = Organization.objects.filter(name__icontains=value)
    org_ids = set([i.org_id for i in org if i])
    if org_ids:
        queryset = queryset.filter(org_id__in=org_ids)
    else:
        queryset = queryset.none()
    return queryset


def order_status_filter(queryset, name, value):
    """
    订单状态查询
    """
    order_status = value.split(',')
    queryset = queryset.filter(status__in=order_status)
    return queryset


class TerminalRecordFilter(django_filters.FilterSet):
    # 新增已确认未确认状态过滤
    confirm_status = django_filters.CharFilter(method='confirm_status_filter')

    def confirm_status_filter(self, queryset, name, value):
        now_time = int(time.time())
        if value == '0':
            # 未确认，过期时间大于当前时间
            return queryset.filter(expiration_time__gt=now_time, confirm_status=0)
        elif value == '1':
            return queryset.filter(Q(expiration_time__lte=now_time) | Q(confirm_status=1))
        else:
            return queryset

    class Meta:
        model = TerminalRecord
        fields = '__all__'


class CompanyFilter(django_filters.FilterSet):
    company = django_filters.CharFilter(field_name='company', lookup_expr='icontains')
    admin = django_filters.CharFilter(field_name='admin', lookup_expr='icontains')
    transport_company_id = django_filters.CharFilter(field_name='transport_company_id', lookup_expr='icontains')
    company_type = django_filters.CharFilter(field_name='company_type', lookup_expr='icontains')
    clean_code = django_filters.CharFilter(field_name='clean_code', lookup_expr='icontains')
    credit_code = django_filters.CharFilter(field_name='credit_code', lookup_expr='icontains')
    fact_name = django_filters.CharFilter(method='fact_name_filter')

    def fact_name_filter(self, queryset, name, value):
        rubbishes = self.request.query_params.get('rubbishes')
        car_queryset = Car.objects.filter(receiving_unit__icontains=value, standing_book=1)
        car_queryset = car_filter_rubbishes(car_queryset, rubbishes)
        car = car_queryset.values_list('transport_company_id', flat=True)
        company_ids = car if car else []
        return queryset.filter(transport_company_id__in=company_ids)

    factory_location_id = django_filters.CharFilter(method='factory_location_id_filter')

    def factory_location_id_filter(self, queryset, name, value):
        rubbishes = self.request.query_params.get('rubbishes')
        car_queryset = Car.objects.filter(factory_location_id__icontains=value, standing_book=1)
        car_queryset = car_filter_rubbishes(car_queryset, rubbishes)
        car = car_queryset.values_list('transport_company_id', flat=True)
        company_ids = car if car else []
        return queryset.filter(transport_company_id__in=company_ids)
    
    service_content_type = django_filters.CharFilter(method='service_content_type_filter')
    
    def service_content_type_filter(self, queryset, name, value):
        """其他垃圾服务内容
        仅运营车、仅运营密闭式清洁站、运营车辆和密闭式清洁站
        """
        if value:
            rubbishes = self.request.query_params.get('rubbishes')
            car_queryset = Car.objects.filter(standing_book=1, is_declare=1)
            car_queryset = car_filter_rubbishes(car_queryset, rubbishes)
            car_company_list = car_queryset.values_list('transport_company_id', flat=True)
            car_company_set = car_company_list and set(car_company_list) or set()
            
            cleaning_point_company_list = CleaningPoint.objects.values_list('transport_company_id', flat=True)
            cleaning_point_company_set = cleaning_point_company_list and set(cleaning_point_company_list) or set()
            if value == '1':
                # 仅运营车
                company_list = list(car_company_set - cleaning_point_company_set)
            elif value == '2':
                # 仅运营密闭式清洁站
                company_list = list(cleaning_point_company_set - car_company_set)
            elif value == '3':
                company_list = list(cleaning_point_company_set & car_company_set)
            elif value == '4':
                # 未运营车也未运行密闭式清洁站
                company_list = list(car_company_set | cleaning_point_company_set)
                queryset = queryset.exclude(transport_company_id__in=company_list)
                return queryset
            else:
                company_list = []
            queryset = queryset.filter(transport_company_id__in=company_list)
        return queryset
    
    
    class Meta:
        model = TransportCompany
        fields = ['company', 'admin', 'fact_name', 'factory_location_id', 'transport_company_id', 'company_type', 'clean_code', 'credit_code']


class CompanyAreaFilter(django_filters.FilterSet):
    street_coding = django_filters.CharFilter(lookup_expr='icontains')
    
    class Meta:
        model = TransportCompanyArea
        fields = ['area_coding', 'street_coding', 'rubbishes']



class CompanyCarFilter(django_filters.FilterSet):
    factory_location_id = django_filters.CharFilter(field_name='factory_location_id', lookup_expr='icontains')
    fact_name = django_filters.CharFilter(field_name='receiving_unit', lookup_expr='icontains')
    street_coding = django_filters.CharFilter(field_name='street_coding', lookup_expr='icontains')
    car_num = django_filters.CharFilter(field_name='car_num', lookup_expr='icontains')
    is_weight = django_filters.CharFilter(field_name='is_weight', lookup_expr='icontains')
    power_type = django_filters.CharFilter(field_name='power_type', lookup_expr='icontains')
    transport_type = django_filters.CharFilter(field_name='transport_type', lookup_expr='iexact')
    type_id = django_filters.CharFilter(field_name='type_id', lookup_expr='icontains')
    more_area = django_filters.CharFilter(method='more_area_filter')
    trans_company = django_filters.CharFilter(method='trans_company_filter')

    def more_area_filter(self, queryset, name, value):
        if value and int(value) == 1:
            return queryset.filter(car_id=value)
        else:
            return queryset

    def trans_company_filter(self, queryset, name, value):
        transport_company_ids = TransportCompany.objects.filter(company__icontains=value,
                                                                is_deleted=0). \
            values_list('transport_company_id', flat=True)
        return queryset.filter(transport_company_id__in=transport_company_ids)

    virtual_area = django_filters.CharFilter(method='virtual_area_filter')

    def virtual_area_filter(self, queryset, name, value):
        if value:
            streets = VirtualAreaStreet.objects.filter(
                virtual_area_code=value).values_list("street_code", flat=True)
            car_filter = Q()
            for street in streets:
                car_filter = car_filter | Q(street_coding__contains=street)
            queryset = queryset.filter(car_filter)
        return queryset

    class Meta:
        model = Car
        fields = '__all__'


class OrgNoResidentFilter(django_filters.FilterSet):
    trans_company = django_filters.CharFilter(method='trans_company_filter')
    org_type_id = django_filters.CharFilter(field_name='org_type_id', lookup_expr='icontains')
    org_id = django_filters.CharFilter(field_name='org_id')
    credit_code = django_filters.CharFilter(field_name='credit_code', lookup_expr='icontains')
    name = django_filters.CharFilter(field_name='name', lookup_expr='icontains')
    official_org_name = django_filters.CharFilter(field_name='official_org_name', lookup_expr='icontains')
    source_type = django_filters.CharFilter(field_name='source_type', lookup_expr='icontains')
    mam_type = django_filters.CharFilter(field_name='mam_type', lookup_expr='icontains')
    clean_code = django_filters.CharFilter(field_name='clean_code', lookup_expr='icontains')
    clean_no = django_filters.CharFilter(field_name='clean_no', lookup_expr='icontains')
    declare_type = django_filters.CharFilter(field_name='declare_type', lookup_expr='icontains')
    restaurant_trash = django_filters.CharFilter(field_name='restaurant_trash', lookup_expr='iexact')
    complete_base = django_filters.CharFilter(method='complete_base_filter')
    qu_logout_status = django_filters.CharFilter(method='qu_logout_status_filter')
    street_logout_status = django_filters.CharFilter(method='street_logout_status_filter')
    street_audit_logout_status = django_filters.CharFilter(method='street_audit_logout_status_filter')
    area_audit = django_filters.CharFilter(method='area_audit_filter')
    is_org_group = django_filters.CharFilter(method='is_org_group_filter')
    register_time = django_filters.CharFilter(method='register_time_filter')
    rubbishes = django_filters.CharFilter(method='rubbishes_filter')
    have_contract = django_filters.CharFilter(method='have_contract_filter')


    def register_time_filter(self, queryset, name, value):
        if value:
            times = value.split(',')
            start = int(time.mktime(time.strptime(times[0] + " 00:00:00", "%Y-%m-%d %H:%M:%S")))
            end = int(time.mktime(time.strptime(times[1] + " 23:59:59", "%Y-%m-%d %H:%M:%S")))
            queryset = queryset.filter(create_time__gte=start, create_time__lte=end)
        return queryset

    def trans_company_filter(self, queryset, name, value):
        transport_company_ids = TransportCompany.objects.filter(company__icontains=value, is_deleted=0). \
            values_list('transport_company_id', flat=True)
        return queryset.filter(transport_company_id__in=transport_company_ids)

    # 是否补全基本信息
    def complete_base_filter(self, queryset, name, value):
        non_resident_base_queryset = NonResidentBase.objects.all()
        rubbishes = self.request.query_params.get('rubbishes')
        if rubbishes:
            non_resident_base_queryset = non_resident_base_queryset.filter(rubbishes=rubbishes)
        org_id_list = non_resident_base_queryset.values_list("org_id", flat=True)
        if value == '是':
            return queryset.filter(org_id__in=org_id_list)
        else:
            return queryset.exclude(org_id__in=org_id_list)


    def qu_logout_status_filter(self, queryset, name, value):
        declare_queryset = OrgNonresidentDeclare.objects.all()
        rubbishes = self.request.query_params.get('rubbishes')
        if rubbishes:
            declare_queryset = declare_queryset.filter(rubbishes=rubbishes)
        if value and int(value) == 1:  # 待审核
            org_id_list = declare_queryset.filter(check_type=3, status=1).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 2:  # 非居民注销
            org_id_list = declare_queryset.filter(check_type=2, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 3:  # 区审核注销通过
            org_id_list = declare_queryset.filter(check_type=3, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 4:  # 街道审核注销通过
            org_id_list = declare_queryset.filter(check_type=4, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 5:  # 二级用户注销通过
            org_id_list = OrgGroup.objects.filter(status=1, logout_status=1).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        else:  # 正常
            org_id_list = declare_queryset.filter(check_type=3, status=1).values_list('org_id', flat=True)
            return queryset.filter(logout_status=0).exclude(org_id__in=list(org_id_list))

    def street_audit_logout_status_filter(self, queryset, name, value):
        declare_queryset = OrgNonresidentDeclare.objects.all()
        rubbishes = self.request.query_params.get('rubbishes')
        if rubbishes:
            declare_queryset = declare_queryset.filter(rubbishes=rubbishes)
        if value and int(value) == 0:  # 正常
            return queryset.filter(logout_status=0)
        elif value and int(value) == 1:  # 待审核
            org_id_list = declare_queryset.filter(check_type=4, status=1).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 2:  # 非居民注销
            org_id_list = declare_queryset.filter(check_type=2, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 3:  # 区审核注销通过
            org_id_list = declare_queryset.filter(check_type=3, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 4:  # 街道审核注销通过
            org_id_list = declare_queryset.filter(check_type=4, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 5:  # 二级用户注销通过
            org_id_list = OrgGroup.objects.filter(status=1, logout_status=1).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 6:  # 全部 0 + 1
            return queryset
        else:  # 全部 0 + 1
            return queryset

    def street_logout_status_filter(self, queryset, name, value):
        declare_queryset = OrgNonresidentDeclare.objects.all()
        rubbishes = self.request.query_params.get('rubbishes')
        if rubbishes:
            declare_queryset = declare_queryset.filter(rubbishes=rubbishes)
        if value and int(value) == 1:  # 非居民注销
            org_id_list = declare_queryset.filter(check_type=2, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 2:  # 区审核注销通过
            org_id_list = declare_queryset.filter(check_type=3, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 4:  # 街道审核注销通过
            org_id_list = declare_queryset.filter(check_type=4, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 5:  # 二级用户注销通过
            org_id_list = OrgGroup.objects.filter(status=1, logout_status=1).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        else:  # 正常
            return queryset.filter(logout_status=0)
    
    logout_status = django_filters.CharFilter(method='logout_status_filter')
    
    def logout_status_filter(self, queryset, name, value):
        if value and int(value) == 1:  # 非居民注销
            org_id_list = OrgNonresidentDeclare.objects.filter(check_type=2, status=2).values_list('org_id', flat=True)
        elif value and int(value) == 2:  # 区审核注销通过
            org_id_list = OrgNonresidentDeclare.objects.filter(check_type=3, status=2).values_list('org_id', flat=True)
        elif value and int(value) == 4:  # 街道审核注销通过
            org_id_list = OrgNonresidentDeclare.objects.filter(check_type=4, status=2).values_list('org_id', flat=True)
        else:  # 正常
            return queryset.filter(logout_status=0)
        return queryset.filter(org_id__in=list(org_id_list))
    
    def area_audit_filter(self, queryset, name, value):
        declare_queryset = OrgNonresidentDeclare.objects.filter(check_type=4)
        rubbishes = self.request.query_params.get('rubbishes')
        if rubbishes:
            declare_queryset = declare_queryset.filter(rubbishes=rubbishes)
        if value and int(value) == 1:  # 待审核
            org_id_list = declare_queryset.filter(status=1).values_list('org_id', flat=True)
        elif value and int(value) == 2:  # 区审核注销通过
            org_id_list = declare_queryset.filter(status=2).values_list('org_id', flat=True)
        else:
            org_id_list = declare_queryset.filter(status=3).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=list(org_id_list))

    virtual_area = django_filters.CharFilter(method='virtual_area_filter')

    def virtual_area_filter(self, queryset, name, value):
        if value:
            streets = VirtualAreaStreet.objects.filter(
                virtual_area_code=value).values_list("street_code", flat=True)
            queryset = queryset.filter(street_coding__in=streets)
        return queryset

    def is_org_group_filter(self, queryset, name, value):
        org_group_ids = OrgGroup.objects.filter(status=1, logout_status=0).values_list('org_group_id', flat=True)
        if value and int(value) == 1:
            return queryset.filter(org_id__in=list(org_group_ids))
        else:
            return queryset.exclude(org_id__in=list(org_group_ids))

    is_sign_contract = django_filters.CharFilter(method='is_sign_contract_filter')

    def is_sign_contract_filter(self, queryset, name, value):
        contract_queryset = ContractNew.objects.filter(is_delete=0, status=3)
        rubbishes = self.request.query_params.get('rubbishes')
        if rubbishes:
            contract_queryset = contract_queryset.filter(rubbishes=rubbishes)
        org_ids = contract_queryset.values_list("org_id", flat=True)
        if value and int(value) == 1:
            queryset = queryset.filter(org_id__in=list(org_ids))
        else:
            queryset = queryset.exclude(org_id__in=list(org_ids))
        return queryset
    
    def rubbishes_filter(self, queryset, name, value):
        if value:
            value = value.upper()
            if value == 'ALL':
                queryset = queryset
            else:
                queryset = queryset.filter(rubbishes__in=[value, BOTH_NONRESIDENT_RUBBISHES])
        return queryset
    
    transport_company_id = django_filters.CharFilter(method='transport_company_id_filter')
    
    def transport_company_id_filter(self, queryset, name, value):
        if value:
            return queryset.filter(Q(transport_company_id=value) | Q(other_transport_company_id=value))
        return queryset
    
    
    def have_contract_filter(self, queryset, name, value):
        if value:
            rubbishes = self.request.query_params.get('rubbishes')
            if rubbishes == NonresidentRubbishes.OTHER:
                filter_field = 'other_have_contract'
            else:
                filter_field = 'have_contract'
            if int(value) == 3:
                contract_queryset = ContractNew.objects.filter(is_delete=0, status=3, contract_status=4)
                if rubbishes:
                    contract_queryset = contract_queryset.filter(rubbishes=rubbishes)
                yan_org_ids = contract_queryset.values_list("org_id", flat=True)
                queryset = queryset.filter(Q(**{filter_field: 0}) | Q(org_id__in=list(yan_org_ids)))
            else:
                queryset = queryset.filter(**{filter_field: int(value)})
        return queryset
    
    transport_company_name = django_filters.CharFilter(method='transport_company_name_filter')
    
    def transport_company_name_filter(self, queryset, name, value):
        transport_company_ids = TransportCompany.objects.filter(company__icontains=value, is_deleted=0). \
            values_list('transport_company_id', flat=True)
        rubbishes = self.request.query_params.get('rubbishes')
        if rubbishes == NonresidentRubbishes.OTHER:
            filter_field = 'other_transport_company_id__in'
        elif rubbishes == NonresidentRubbishes.RESTAURANTS:
            filter_field = 'transport_company_id__in'
        else:
            return queryset.filter(Q(transport_company_id__in=transport_company_ids) | Q(other_transport_company_id__in=transport_company_ids))
        queryset = queryset.filter(**{filter_field: transport_company_ids})
        return queryset


    class Meta:
        model = Organization
        fields = '__all__'



class OrgRfidFilter(django_filters.FilterSet):
    """过滤主体标签"""
    type_name = django_filters.CharFilter(method="type_name_filter")

    # 桶/车类型
    def type_name_filter(self, queryset, name, value):
        return queryset.filter(bind_name__contains=value)

    search_name = django_filters.CharFilter(method='search_name_filter')

    def search_name_filter(self, queryset, name, value):
        return queryset.filter(name__contains=value)

    search_rfid_num = django_filters.CharFilter(method='search_rfid_num_filter')

    def search_rfid_num_filter(self, queryset, name, value):
        return queryset.filter(rfid_num__contains=value)

    virtual_area = django_filters.CharFilter(method='virtual_area_filter')

    def virtual_area_filter(self, queryset, name, value):
        if value:
            streets = VirtualAreaStreet.objects.filter(
                virtual_area_code=value).values_list("street_code", flat=True)
            queryset = queryset.filter(street_coding__in=streets)
        return queryset

    class Meta:
        model = OrgRfid
        fields = '__all__'


class RfidTypeFilter(django_filters.FilterSet):
    class Meta:
        model = RfidType
        fields = '__all__'


class RubbishTypeFilter(django_filters.FilterSet):
    grade = django_filters.CharFilter(method='get_grade_filter')

    def get_grade_filter(self, queryset, name, value):
        return queryset.filter(remark=value)

    class Meta:
        model = RubbishType
        fields = '__all__'


class OrgRfidFilterList(django_filters.FilterSet):
    """过滤主体标签"""
    type_name = django_filters.CharFilter(method="type_name_filter")

    # 桶/车类型
    def type_name_filter(self, queryset, name, value):
        return queryset.filter(bind_name__contains=value)

    search_name = django_filters.CharFilter(method='search_name_filter')

    def search_name_filter(self, queryset, name, value):
        return queryset.filter(name__contains=value)

    search_rfid_num = django_filters.CharFilter(method='search_rfid_num_filter')

    def search_rfid_num_filter(self, queryset, name, value):
        return queryset.filter(rfid_num__contains=value)

    class Meta:
        model = OrgRfid
        fields = '__all__'


class CityContractFilter(django_filters.FilterSet):
    # transport_company_id = django_filters.CharFilter(field_name='transport_company_id', lookup_expr='icontains')
    transport_company_id = django_filters.CharFilter(field_name='company_id', lookup_expr='icontains')
    # transport_company_name = django_filters.CharFilter(field_name='transport_company_name', lookup_expr='icontains')
    transport_company_name = django_filters.CharFilter(field_name='company_name', lookup_expr='icontains')
    street_coding = django_filters.CharFilter(field_name='street_coding', lookup_expr='icontains')
    # area_coding = django_filters.CharFilter(field_name='area_coding', lookup_expr='icontains')
    area_coding = django_filters.CharFilter(field_name='service_area', lookup_expr='icontains')
    org_type = django_filters.CharFilter(field_name='org_type', lookup_expr='icontains')
    rubbish_type = django_filters.CharFilter(field_name='rubbish_type', lookup_expr='icontains')
    organization = django_filters.CharFilter(field_name='organization', lookup_expr='icontains')
    org_id = django_filters.CharFilter(field_name='org_id', lookup_expr='icontains')
    belong_to = django_filters.CharFilter(field_name='belong_to', lookup_expr='exact')

    start_date = django_filters.CharFilter(method='start_date_filter')

    def start_date_filter(self, queryset, name, value):
        queryset = queryset.filter(create_time__gte=value)
        return queryset

    end_date = django_filters.CharFilter(method='end_date_filter')

    def end_date_filter(self, queryset, name, value):
        tomorrow = datetime.datetime.strptime(value, "%Y-%m-%d") + datetime.timedelta(days=1)
        queryset = queryset.filter(create_time__lt=tomorrow)
        return queryset

    clean_code = django_filters.CharFilter(method='clean_code_filter')

    def clean_code_filter(self, queryset, name, value):
        org_id_list = list(
            Organization.objects.filter(clean_code=value).values_list('org_id', flat=True))
        queryset = queryset.filter(org_id__in=org_id_list)
        return queryset

    clean_no = django_filters.CharFilter(method='clean_no_filter')

    def clean_no_filter(self, queryset, name, value):
        org_id_list = list(
            Organization.objects.filter(clean_no=value).values_list('org_id', flat=True))
        queryset = queryset.filter(org_id__in=org_id_list)
        return queryset

    app_clean_no = django_filters.CharFilter(method='app_clean_no_filter')

    def app_clean_no_filter(self, queryset, name, value):
        org_id_list = list(
            Organization.objects.filter(clean_no__contains=value).values_list('org_id', flat=True))
        queryset = queryset.filter(org_id__in=org_id_list)
        return queryset

    credit_code = django_filters.CharFilter(method='credit_code_filter')

    def credit_code_filter(self, queryset, name, value):
        org_id_list = list(
            Organization.objects.filter(credit_code=value).values_list('org_id', flat=True))
        queryset = queryset.filter(org_id__in=org_id_list)
        return queryset

    comm_coding = django_filters.CharFilter(method='comm_coding_filter')

    def comm_coding_filter(self, queryset, name, value):
        org_id_list = list(
            Organization.objects.filter(comm_coding=value).values_list('org_id', flat=True))
        queryset = queryset.filter(org_id__in=org_id_list)
        return queryset


    # 服务临期
    service_temporary = django_filters.CharFilter(method='service_temporary_filter')

    def service_temporary_filter(self, queryset, name, value):
        # 已过期
        if value == '0':
            queryset = queryset.filter(contract_status=2)
        # 三个月
        elif value == '1':
            queryset = queryset.filter(contract_status=4, service_end_date__lte=datetime.datetime.now() + relativedelta(months=+3))
        return queryset
    

    mam_type = django_filters.CharFilter(method='mam_type_filter')


    def mam_type_filter(self, queryset, name, value):
        org_id_list = list(
            Organization.objects.filter(mam_type=value).values_list('org_id', flat=True))
        queryset = queryset.filter(org_id__in=org_id_list)
        return queryset


    class Meta:
        # model = TransportContract
        model = ContractNew
        fields = ['area_coding', 'transport_company_id', 'transport_company_name', 'street_coding', 'org_type',
                  'rubbish_type', 'organization', 'org_id', 'belong_to', 'service_temporary',
                  'start_date', 'end_date', 'collect_method']


class CarRecordFilter(django_filters.FilterSet):
    area_coding = django_filters.CharFilter(field_name='area_coding', lookup_expr='icontains')
    street_coding = django_filters.CharFilter(field_name='street_coding', lookup_expr='icontains')
    org_id = django_filters.CharFilter(field_name='org_id', lookup_expr='icontains')
    car_num = django_filters.CharFilter(field_name='car_num', lookup_expr='icontains')
    rfid_num = django_filters.CharFilter(field_name='rfid_num', lookup_expr='icontains')
    type_id = django_filters.CharFilter(field_name='type_id', lookup_expr='icontains')
    quality = django_filters.CharFilter(field_name='quality', lookup_expr='icontains')
    size = django_filters.CharFilter(field_name='size', lookup_expr='icontains')
    capacity = django_filters.CharFilter(field_name='capacity', lookup_expr='icontains')
    weight_type = django_filters.CharFilter(field_name='weight_type', lookup_expr='icontains')
    is_confirm = django_filters.CharFilter(field_name='is_confirm', lookup_expr='iexact')
    org_name = django_filters.CharFilter(method='org_name_filter')
    clean_no = django_filters.CharFilter(method='clean_no_filter')
    credit_code = django_filters.CharFilter(method='credit_code_filter')
    transport_company_id = django_filters.CharFilter(method='transport_company_id_filter')
    transport_company_name = django_filters.CharFilter(method='transport_company_name_filter')

    def transport_company_name_filter(self, queryset, name, value):
        transport_company_ids = TransportCompany.objects.filter(company__icontains=value, is_deleted=0). \
            values_list('transport_company_id', flat=True)
        car_ids = Car.objects.filter(is_deleted=0, transport_company_id__in=transport_company_ids). \
            values_list('car_id', flat=True)
        return queryset.filter(car_id__in=car_ids)

    def transport_company_id_filter(self, queryset, name, value):
        car_ids = Car.objects.filter(is_deleted=0, transport_company_id=value).values_list('car_id', flat=True)
        return queryset.filter(car_id__in=car_ids)

    def org_name_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, name__icontains=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    def clean_no_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, clean_no__icontains=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    def credit_code_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, credit_code__icontains=value).values_list('org_id',
                                                                                                      flat=True)
        return queryset.filter(org_id__in=org_ids)

    class Meta:
        model = CarRecordOrg
        fields = ['area_coding', 'transport_company_id', 'transport_company_name', 'street_coding', 'weight_type',
                  'type_id', 'org_id', 'car_num', 'rfid_num', 'quality', 'size', 'org_name', 'clean_no', 'credit_code']


class OtherCarRecordFilter(django_filters.FilterSet):
    area_coding = django_filters.CharFilter(field_name='area_coding')
    street_coding = django_filters.CharFilter(field_name='street_coding')
    org_id = django_filters.CharFilter(field_name='org_id')
    car_num = django_filters.CharFilter(field_name='car_num', lookup_expr='icontains')
    rfid_num = django_filters.CharFilter(field_name='rfid_num', lookup_expr='icontains')
    type_id = django_filters.CharFilter(field_name='type_id')
    quality = django_filters.CharFilter(field_name='quality')
    # size = django_filters.CharFilter(field_name='size', lookup_expr='icontains')
    capacity = django_filters.CharFilter(field_name='capacity')
    # weight_type = django_filters.CharFilter(field_name='weight_type', lookup_expr='icontains')
    # is_confirm = django_filters.CharFilter(field_name='is_confirm', lookup_expr='iexact')
    org_name = django_filters.CharFilter(method='org_name_filter')
    clean_no = django_filters.CharFilter(method='clean_no_filter')
    credit_code = django_filters.CharFilter(method='credit_code_filter')
    transport_company_id = django_filters.CharFilter(method='transport_company_id_filter')
    transport_company_name = django_filters.CharFilter(method='transport_company_name_filter')
    weight_type = django_filters.CharFilter(method='weight_type_filter')
    many_car_record_id = django_filters.CharFilter(method='many_car_record_id_filter')
    virtual_area = django_filters.CharFilter(method='virtual_area_filter')
    search = django_filters.CharFilter(method='search_filter')
    mam_type = django_filters.CharFilter(method='mam_type_filter')
    mam_subtype = django_filters.CharFilter(method='mam_subtype_filter')
    # is_confirm = django_filters.CharFilter(method='is_confirm_filter')

    def virtual_area_filter(self, queryset, name, value):
        if value:
            streets = VirtualAreaStreet.objects.filter(virtual_area_code=value).values_list(
                "street_code", flat=True)
            queryset = queryset.filter(street_coding__in=streets)
        return queryset

    def weight_type_filter(self, queryset, name, value):
        car_record_ids = [i.car_record_id for i in queryset]
        flow_query = CarRecordFlow.objects.using("tidb_ljfl_db").filter(is_deleted=0, car_record_id__in=car_record_ids)
        if value == 'CAR':
            car_record_org_ids = flow_query.filter(weight_type='CAR').values_list('car_record_id', flat=True)
            return queryset.filter(car_record_id__in=list(car_record_org_ids))
        elif value == 'CAPACITY':
            car_record_org_ids = flow_query.filter(weight_type='CAPACITY').values_list('car_record_id', flat=True)
            return queryset.filter(car_record_id__in=list(car_record_org_ids))
        else:
            return queryset

    def transport_company_name_filter(self, queryset, name, value):
        transport_company_ids = TransportCompany.objects.filter(company__icontains=value, is_deleted=0). \
            values_list('transport_company_id', flat=True)
        car_ids = Car.objects.filter(is_deleted=0, transport_company_id__in=transport_company_ids). \
            values_list('car_id', flat=True)
        return queryset.filter(car_id__in=car_ids)

    def transport_company_id_filter(self, queryset, name, value):
        car_ids = Car.objects.filter(is_deleted=0, transport_company_id=value).values_list('car_id', flat=True)
        return queryset.filter(car_id__in=car_ids)

    def org_name_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, name__icontains=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    def clean_no_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, clean_no__icontains=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    def credit_code_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, credit_code__icontains=value).values_list('org_id',
                                                                                                      flat=True)
        return queryset.filter(org_id__in=org_ids)

    def many_car_record_id_filter(self, queryset, name, value):
        if value:
            car_record_id_list = value.split(',')
            queryset = queryset.filter(car_record_id__in=set(car_record_id_list))
        return queryset

    def search_filter(self, queryset, name, value):
        if value:
            org_ids = Organization.objects.filter(is_deleted=0). \
                filter(Q(name__icontains=value) | Q(clean_no__icontains=value)).values_list('org_id', flat=True)
            queryset = queryset.filter(org_id__in=org_ids)
        return queryset

    def mam_type_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, mam_type__icontains=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    def mam_subtype_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, mam_subtype__icontains=value).values_list('org_id',
                                                                                                      flat=True)
        return queryset.filter(org_id__in=org_ids)

    class Meta:
        model = CarRecord
        fields = ['car_record_id', 'area_coding', 'transport_company_id', 'transport_company_name', 'street_coding',
                  'type_id', 'org_id', 'car_num', 'rfid_num', 'quality', 'org_name', 'clean_no', 'credit_code',
                  'search']


class OpinionDetailsFilter(django_filters.FilterSet):
    # 开始时间过滤
    date_start = django_filters.DateTimeFilter(method='date_start_filter')

    def get_time_to_timestamp(self, ds):
        """
        ds: 字符串时间格式
        :return: 时间戳
        """
        timeArray_ds = time.strptime(ds, "%Y-%m-%d")
        timestamp_ds = time.mktime(timeArray_ds)
        return timestamp_ds

    def date_start_filter(self, queryset, name, value):
        value = value.strftime("%Y-%m-%d")
        date_start = self.get_time_to_timestamp(value)
        queryset = queryset.filter(create_time__gte=date_start)
        return queryset

    # 结束时间
    date_end = django_filters.DateTimeFilter(method='date_end_filter')

    def date_end_filter(self, queryset, name, value):
        value = value.strftime("%Y-%m-%d")
        tomorrow = datetime.datetime.strptime(value, "%Y-%m-%d") + datetime.timedelta(days=1)
        str_value = tomorrow.strftime("%Y-%m-%d")
        date_end = self.get_time_to_timestamp(str_value)
        queryset = queryset.filter(
            create_time__lt=date_end)
        return queryset

    class Meta:
        model = OpinionDetails
        fields = '__all__'
        # fields = ['area_coding', 'transport_company_id', 'transport_company_name', 'street_coding',
        #           'type_id', 'org_id', 'car_num', 'rfid_num', 'quality', 'org_name', 'clean_no', 'credit_code']


class ApplyStationFilter(django_filters.FilterSet):
    area_coding = django_filters.CharFilter(method='area_coding_filter')

    def area_coding_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, area_coding=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    street_coding = django_filters.CharFilter(method='street_coding_filter')

    def street_coding_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, street_coding=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    org_name = django_filters.CharFilter(method='org_name_filter')

    def org_name_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, name__icontains=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    transport_company_id = django_filters.CharFilter(method='transport_company_id_filter')

    def transport_company_id_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, transport_company_id=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    class Meta:
        model = ApplyStationRecod
        fields = ['source', 'apply_user', 'reason', 'org_id', 'status', 'transport_company_id', 'org_name',
                  'street_coding', 'area_coding']


class ApplyOrgRfidFilter(django_filters.FilterSet):
    """过滤主体标签"""
    search_rfid_num = django_filters.CharFilter(method='search_rfid_num_filter')

    def search_rfid_num_filter(self, queryset, name, value):
        return queryset.filter(rfid_num__contains=value)

    class Meta:
        model = OrgRfid
        fields = '__all__'


class AppealRecodFilter(django_filters.FilterSet):
    area_coding = django_filters.CharFilter(method='area_coding_filter')

    def area_coding_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, area_coding=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    street_coding = django_filters.CharFilter(method='street_coding_filter')

    def street_coding_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, street_coding=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    org_name = django_filters.CharFilter(method='org_name_filter')

    def org_name_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, name__icontains=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    transport_company_id = django_filters.CharFilter(method='transport_company_id_filter')

    def transport_company_id_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, transport_company_id=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    second_appeal = django_filters.CharFilter(method='second_appeal_filter')

    def second_appeal_filter(self, queryset, name, value):
        if value == '1':
            return queryset.filter(status=3)
        else:
            return queryset.exclude(status=3)

    class Meta:
        model = AppealRecod
        fields = ['apply_user', 'org_id', 'transport_company_id', 'org_name',
                  'street_coding', 'area_coding', 'bill_factory_id']


class AppealDealRecordFilter(django_filters.FilterSet):
    appeal_id = django_filters.CharFilter(method='appeal_id_filter')

    def appeal_id_filter(self, queryset, name, value):
        record = AppealRecod.objects.filter(appeal_id=value).values('bill_factory_id', 'bill_org_id')
        bill_factory_id = record[0].get('bill_factory_id') if record and record[0] else ''
        bill_org_id = record[0].get('bill_org_id') if record and record[0] else ''
        queryset = queryset.filter(bill_factory_id=bill_factory_id, bill_org_id=bill_org_id)
        return queryset

    class Meta:
        model = CarRecordFlow
        fields = ['appeal_id', 'bill_factory_id', 'car_record_id']


class NonresidentOrderFilter(django_filters.FilterSet):
    org_name = django_filters.CharFilter(method=org_name_filter)
    order_status = django_filters.CharFilter(method=order_status_filter)
    type_id = django_filters.CharFilter(method="get_type_id_filter")
    quality = django_filters.CharFilter(method="get_quality_filter")

    def get_type_id_filter(self, queryset, name, value):
        """
        垃圾类型
        """
        queryset = queryset.filter(rubbish_type_id=value)
        return queryset

    def get_quality_filter(self, queryset, name, value):
        """
        评价筛选
        """
        quality_code = 0
        if value == "优":
            quality_code = 1
        elif value == "良":
            quality_code = 2
        elif value == "中":
            quality_code = 3
        elif value == "差":
            quality_code = 4
        queryset = queryset.filter(quality_code=quality_code)
        return queryset

    class Meta:
        model = NonresidentOrder
        fields = '__all__'


class NonresidentOrderWarningFilter(django_filters.FilterSet):
    class Meta:
        model = NonresidentOrderWarning
        fields = '__all__'

    apply_clean_time_start = django_filters.CharFilter(method='apply_clean_time_start_filter')

    def apply_clean_time_start_filter(self, queryset, name, value):

        if value:
            queryset = queryset.filter(apply_clean_time__gte=value)
        return queryset

    apply_clean_time_end = django_filters.CharFilter(method='apply_clean_time_end_filter')

    def apply_clean_time_end_filter(self, queryset, name, value):
        if value:
            queryset = queryset.filter(apply_clean_time__lte=value)
        return queryset

    order_create_time_start = django_filters.CharFilter(method='order_create_time_start_filter')

    def order_create_time_start_filter(self, queryset, name, value):
        if value:
            queryset = queryset.filter(order_create_time__gte=value)
        return queryset

    order_create_time_end = django_filters.CharFilter(method='order_create_time_start_filter')

    def order_create_time_end_filter(self, queryset, name, value):
        if value:
            queryset = queryset.filter(order_create_time__lte=value)
        return queryset


class RepairWorkingOrderFilter(django_filters.FilterSet):
    class Meta:
        model = RepairWorkingOrder
        fields = '__all__'


class OrganizationFilter(django_filters.FilterSet):
    class Meta:
        model = Organization
        fields = '__all__'


class AppointmentCollectionFilter(django_filters.FilterSet):
    org_id = django_filters.CharFilter(method='org_id_filter')

    def org_id_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, org_id=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    org_name = django_filters.CharFilter(method='org_name_filter')

    def org_name_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, name__icontains=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    area_coding = django_filters.CharFilter(method='area_coding_filter')
    street_coding = django_filters.CharFilter(method='street_coding_filter')
    comm_coding = django_filters.CharFilter(method='comm_coding_filter')

    def area_coding_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, area_coding=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    def street_coding_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, street_coding=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    def comm_coding_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, comm_coding=value).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=org_ids)

    start_date = django_filters.DateTimeFilter(method='start_date_filter')
    end_date = django_filters.DateTimeFilter(method='end_date_filter')

    def start_date_filter(self, queryset, name, start_date):
        queryset = queryset.filter(appointment_time_start__gte=start_date)
        return queryset

    def end_date_filter(self, queryset, name, end_date):
        end_date = end_date + datetime.timedelta(days=1)
        queryset = queryset.filter(appointment_time_end__lt=end_date)
        return queryset

    class Meta:
        model = MyNonresidentAppointmentCollection
        fields = "__all__"


class OrgSecondFilter(django_filters.FilterSet):
    area_coding = django_filters.CharFilter(method='area_coding_filter')
    street_coding = django_filters.CharFilter(method='street_coding_filter')
    comm_coding = django_filters.CharFilter(method='comm_coding_filter')

    def area_coding_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, is_declare=1, area_coding=value, logout_status=0). \
            values_list('org_id', flat=True)
        return queryset.filter(org_group_id__in=list(org_ids))

    def street_coding_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, is_declare=1, street_coding=value, logout_status=0). \
            values_list('org_id', flat=True)
        return queryset.filter(org_group_id__in=list(org_ids))

    def comm_coding_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, is_declare=1, comm_coding=value, logout_status=0). \
            values_list('org_id', flat=True)
        return queryset.filter(org_group_id__in=list(org_ids))

    class Meta:
        model = OrgGroup
        fields = "__all__"


class NonresidentPayOrderFilter(django_filters.FilterSet):
    status = django_filters.CharFilter(field_name='status', lookup_expr='icontains')
    org_name = django_filters.CharFilter(method='org_name_filter')
    clean_no = django_filters.CharFilter(method='clean_no_filter')
    pay_start_date = django_filters.DateTimeFilter(method='pay_start_date_filter')
    pay_end_date = django_filters.DateTimeFilter(method='pay_end_date_filter')
    confirm_start_date = django_filters.DateTimeFilter(method='confirm_start_date_filter')
    confirm_end_date = django_filters.DateTimeFilter(method='confirm_end_date_filter')

    def pay_start_date_filter(self, queryset, name, pay_start_date):
        queryset = queryset.filter(pay_time__gte=pay_start_date)
        return queryset

    def pay_end_date_filter(self, queryset, name, pay_end_date):
        pay_end_date = pay_end_date + datetime.timedelta(days=1)
        queryset = queryset.filter(pay_time__lt=pay_end_date)
        return queryset

    def confirm_start_date_filter(self, queryset, name, confirm_start_date):
        queryset = queryset.filter(confirm_time__gte=confirm_start_date)
        return queryset

    def confirm_end_date_filter(self, queryset, name, confirm_end_date):
        confirm_end_date = confirm_end_date + datetime.timedelta(days=1)
        queryset = queryset.filter(confirm_time__lt=confirm_end_date)
        return queryset

    def org_name_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, is_declare=1, logout_status=0,
                                              name__icontains=value). \
            values_list('org_id', flat=True)
        return queryset.filter(org_id__in=list(org_ids))

    def clean_no_filter(self, queryset, name, value):
        org_ids = Organization.objects.filter(is_deleted=0, is_declare=1, logout_status=0,
                                              clean_no__icontains=value). \
            values_list('org_id', flat=True)
        return queryset.filter(org_id__in=list(org_ids))

    class Meta:
        model = NonresidentPayOrder
        fields = ['status', 'org_name', 'clean_no', 'pay_start_date', 'pay_end_date', 'org_id',
                  'transport_company_id', 'bill_factory_id', 'bill_org_id', 'order_num', 'confirm_start_date',
                  'confirm_end_date']


class NonresidentPayOrderFilterForStatistic(django_filters.FilterSet):
    status = django_filters.CharFilter(field_name='status', lookup_expr='icontains')
    start_date = django_filters.DateFilter(method='start_date_filter')
    end_date = django_filters.DateFilter(method='end_date_filter')

    def start_date_filter(self, queryset, name, start_date):
        queryset = queryset.filter(pay_time__date__gte=start_date)
        return queryset

    def end_date_filter(self, queryset, name, end_date):
        queryset = queryset.filter(pay_time__date__lte=end_date)
        return queryset

    class Meta:
        model = NonresidentPayOrder
        fields = ['status', 'start_date', 'end_date']


class OtherOrgNoResidentFilter(django_filters.FilterSet):
    trans_company = django_filters.CharFilter(method='trans_company_filter')
    org_type_id = django_filters.CharFilter(field_name='org_type_id', lookup_expr='icontains')
    credit_code = django_filters.CharFilter(field_name='credit_code', lookup_expr='icontains')
    name = django_filters.CharFilter(field_name='name', lookup_expr='icontains')
    official_org_name = django_filters.CharFilter(field_name='official_org_name', lookup_expr='icontains')
    source_type = django_filters.CharFilter(field_name='source_type', lookup_expr='icontains')
    mam_type = django_filters.CharFilter(field_name='mam_type', lookup_expr='icontains')
    clean_code = django_filters.CharFilter(field_name='clean_code', lookup_expr='icontains')
    clean_no = django_filters.CharFilter(field_name='clean_no', lookup_expr='icontains')
    declare_type = django_filters.CharFilter(field_name='declare_type', lookup_expr='icontains')
    restaurant_trash = django_filters.CharFilter(field_name='restaurant_trash', lookup_expr='iexact')
    complete_base = django_filters.CharFilter(method='complete_base_filter')
    qu_logout_status = django_filters.CharFilter(method='qu_logout_status_filter')
    street_logout_status = django_filters.CharFilter(method='street_logout_status_filter')
    street_audit_logout_status = django_filters.CharFilter(method='street_audit_logout_status_filter')
    area_audit = django_filters.CharFilter(method='area_audit_filter')
    is_org_group = django_filters.CharFilter(method='is_org_group_filter')
    register_time = django_filters.CharFilter(method='register_time_filter')

    def register_time_filter(self, queryset, name, value):
        if value:
            times = value.split(',')
            start = int(time.mktime(time.strptime(times[0] + " 00:00:00", "%Y-%m-%d %H:%M:%S")))
            end = int(time.mktime(time.strptime(times[1] + " 23:59:59", "%Y-%m-%d %H:%M:%S")))
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(status=2,
                                                               audit_time__gte=start,
                                                               audit_time__lte=end).values_list('org_id', flat=True)
            queryset = queryset.filter(org_id__in=list(org_id_list))
        return queryset

    def trans_company_filter(self, queryset, name, value):
        transport_company_ids = TransportCompany.objects.filter(company__icontains=value, is_deleted=0). \
            values_list('transport_company_id', flat=True)
        return queryset.filter(transport_company_id__in=transport_company_ids)

    # 是否补全基本信息
    def complete_base_filter(self, queryset, name, value):
        org_id_list = NonResidentBase.objects.values_list("org_id", flat=True)
        if value == '是':
            return queryset.filter(org_id__in=org_id_list)
        else:
            return queryset.exclude(org_id__in=org_id_list)

    def qu_logout_status_filter(self, queryset, name, value):
        if value and int(value) == 1:  # 待审核
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=3, status=1).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 2:  # 非居民注销
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=2, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 3:  # 区审核注销通过
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=3, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 4:  # 街道审核注销通过
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=4, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 5:  # 二级用户注销通过
            org_id_list = OrgGroup.objects.filter(status=1, logout_status=1).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        else:  # 正常
            org_id_list = OrgNonresidentDeclare.objects.filter(check_type=3, status=1).values_list('org_id', flat=True)
            return queryset.filter(logout_status=0).exclude(org_id__in=list(org_id_list))

    def street_audit_logout_status_filter(self, queryset, name, value):
        if value and int(value) == 1:  # 待审核
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=4, status=1).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 2:  # 非居民注销
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=2, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 3:  # 区审核注销通过
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=3, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 4:  # 街道审核注销通过
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=4, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 5:  # 二级用户注销通过
            org_id_list = OrgGroup.objects.filter(status=1, logout_status=1).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        else:  # 正常
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=4, status=1).values_list('org_id', flat=True)
            return queryset.filter(logout_status=0).exclude(org_id__in=list(org_id_list))

    def street_logout_status_filter(self, queryset, name, value):
        if value and int(value) == 1:  # 非居民注销
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=2, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 2:  # 区审核注销通过
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=3, status=2).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 4:  # 街道审核注销通过
            org_id_list = OtherOrgNonresidentDeclare.objects.filter(check_type=4, status=2).values_list('org_id',
                                                                                                        flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        elif value and int(value) == 5:  # 二级用户注销通过
            org_id_list = OrgGroup.objects.filter(status=1, logout_status=1).values_list('org_id', flat=True)
            return queryset.filter(org_id__in=list(org_id_list))
        else:  # 正常
            return queryset.filter(logout_status=0)

    def area_audit_filter(self, queryset, name, value):
        declare_queryset = OtherOrgNonresidentDeclare.objects.filter(check_type=4)
        if value and int(value) == 1:  # 待审核
            org_id_list = declare_queryset.filter(status=1).values_list('org_id', flat=True)
        elif value and int(value) == 2:  # 区审核注销通过
            org_id_list = declare_queryset.filter(status=2).values_list('org_id', flat=True)
        else:
            org_id_list = declare_queryset.filter(status=3).values_list('org_id', flat=True)
        return queryset.filter(org_id__in=list(org_id_list))

    virtual_area = django_filters.CharFilter(method='virtual_area_filter')

    def virtual_area_filter(self, queryset, name, value):
        if value:
            streets = VirtualAreaStreet.objects.filter(
                virtual_area_code=value).values_list("street_code", flat=True)
            queryset = queryset.filter(street_coding__in=streets)
        return queryset

    def is_org_group_filter(self, queryset, name, value):
        if value and int(value) == 1:
            return queryset.filter(is_group_second=1)
        else:
            return queryset.filter(is_group_second=0)

    # is_sign_contract = django_filters.CharFilter(method='is_sign_contract_filter')
    #
    # def is_sign_contract_filter(self, queryset, name, value):
    #     org_ids = ContractNew.objects.filter(is_delete=0, status=3).values_list("org_id",flat=True)
    #     if value and int(value) == 1:
    #         queryset = queryset.filter(org_id__in=list(org_ids))
    #     else:
    #         queryset = queryset.exclude(org_id__in=list(org_ids))
    #     return queryset

    class Meta:
        model = OrganizationOther
        fields = '__all__'
        exclude = 'have_contract'
        
        
class OrgNoResidentQuotaFilter(django_filters.FilterSet):
    trans_company = django_filters.CharFilter(method='trans_company_filter')
    org_name = django_filters.CharFilter(field_name='name', lookup_expr='icontains')
    org_type_id = django_filters.CharFilter(field_name='org_type_id', lookup_expr='icontains')
    credit_code = django_filters.CharFilter(field_name='credit_code', lookup_expr='icontains')
    name = django_filters.CharFilter(field_name='name', lookup_expr='icontains')
    official_org_name = django_filters.CharFilter(field_name='official_org_name', lookup_expr='icontains')
    mam_type = django_filters.CharFilter(field_name='mam_type', lookup_expr='icontains')
    clean_code = django_filters.CharFilter(field_name='clean_code', lookup_expr='icontains')
    clean_no = django_filters.CharFilter(field_name='clean_no', lookup_expr='icontains')
    status = django_filters.CharFilter(method='status_filter')
    rubbishes = django_filters.CharFilter(field_name="rubbishes", lookup_expr='contains')

    def trans_company_filter(self, queryset, name, value):
        transport_company_ids = TransportCompany.objects.filter(company__icontains=value, is_deleted=0). \
            values_list('transport_company_id', flat=True)
        return queryset.filter(transport_company_id__in=transport_company_ids)

    virtual_area = django_filters.CharFilter(method='virtual_area_filter')

    def virtual_area_filter(self, queryset, name, value):
        if value:
            streets = VirtualAreaStreet.objects.filter(
                virtual_area_code=value).values_list("street_code", flat=True)
            queryset = queryset.filter(street_coding__in=streets)
        return queryset
    
    def status_filter(self, queryset, name, value):
        year_current = get_current_year()
        if value and int(value):
            org_ids = OrgNonresidentQuota.objects.filter(status=int(value), is_deleted=0,
                                                         quota_date=year_current).values_list('org_id', flat=True)
            queryset = queryset.filter(org_id__in=list(org_ids))
        elif value == '0':
            org_ids = OrgNonresidentQuota.objects.filter(is_deleted=0,
                                                         quota_date=year_current).values_list('org_id', flat=True)
            queryset = queryset.exclude(org_id__in=list(org_ids))
        return queryset
    
    class Meta:
        model = Organization
        fields = '__all__'
        exclude = 'have_contract'


class AppointmentRecyclingFilter(django_filters.FilterSet):
    # start_create_time = django_filters.DateTimeFilter(field_name='assigned_date', lookup_expr='gte')
    # end_create_time = django_filters.DateTimeFilter(field_name='assigned_date', lookup_expr='lte')
    car_num = django_filters.CharFilter(field_name='car_num', lookup_expr='icontains')
    recycling_num = django_filters.CharFilter(field_name='recycling_num', lookup_expr='icontains')
    clean_no = django_filters.CharFilter(field_name='clean_no', lookup_expr='icontains')
    org_name = django_filters.CharFilter(field_name='org_name', lookup_expr='icontains')
    start_collector_date = django_filters.DateTimeFilter(field_name='collector_date', lookup_expr='gte')
    end_collector_date = django_filters.DateTimeFilter(field_name='collector_date', lookup_expr='lte')
    start_date = django_filters.DateTimeFilter(method='start_create_time_filter')
    end_date = django_filters.DateTimeFilter(method='end_create_time_filter')


    def start_create_time_filter(self, queryset, name, start_date):
        queryset = queryset.filter(create_time__gte=start_date)
        return queryset


    def end_create_time_filter(self, queryset, name, end_date):
        create_end_date = end_date + datetime.timedelta(days=1)
        queryset = queryset.filter(create_time__lt=create_end_date)
        return queryset
    
    
    class Meta:
        model = AppointmentRecycling
        fields = ['start_date', 'end_date', 'car_num', 'org_id', 'transport_company_id',
                  'recycling_num', 'clean_no', 'org_name', 'start_collector_date', 'end_collector_date']


class BagBreakingResidentRecordFilter(django_filters.FilterSet):
    start_date = django_filters.DateTimeFilter(method='start_date_filter')
    end_date = django_filters.DateTimeFilter(method='end_date_filter')
    area_coding = django_filters.CharFilter(field_name='area_coding', lookup_expr='exact')
    street_coding = django_filters.CharFilter(field_name='street_coding', lookup_expr='exact')
    comm_coding = django_filters.CharFilter(field_name='comm_coding', lookup_expr='exact')
    org_name = django_filters.CharFilter(field_name='org_name', lookup_expr='icontains')
    resident_id = django_filters.CharFilter(field_name='resident_id', lookup_expr='exact')
    type_id = django_filters.CharFilter(method='type_filter')
    card_num = django_filters.CharFilter(method='card_num_filter')
    coin_type = django_filters.CharFilter(method='coin_type_filter')

    def start_date_filter(self, queryset, name, value):
        timestamp_start = datetime.datetime.combine(value, datetime.datetime.min.time()).timestamp()
        return queryset.filter(create_time__gte=timestamp_start)

    def end_date_filter(self, queryset, name, value):
        timestamp_end = datetime.datetime.combine(value, datetime.datetime.max.time()).timestamp()
        return queryset.filter(create_time__lte=timestamp_end)

    def type_filter(self, queryset, name, value):
        queryset = queryset.filter(type_id=value)
        return queryset

    def card_num_filter(self, queryset, name, value):
        resident = Resident.objects.using("tidb_ljfl_db").filter(card_num=value).first()
        queryset = queryset.filter(resident_id=resident.resident_id if resident else "default")
        return queryset

    def coin_type_filter(self, queryset, name, value):
        if value == '差':
            queryset = queryset.filter(
                Q(coin_type=value) | Q(coin_type__isnull=True) | Q(coin_type="") | Q(coin_type="无桶") | Q(coin_type="空桶")
            )
        else:
            queryset = queryset.filter(coin_type=value)
        return queryset


class TrashCanAlarmRecordFilter(django_filters.FilterSet):
    start_date = django_filters.DateTimeFilter(field_name='alarm_time__date', lookup_expr='gte')
    end_date = django_filters.DateTimeFilter(field_name='alarm_time__date', lookup_expr='lte')
    street_coding = django_filters.CharFilter(field_name='street_coding', lookup_expr='exact')
    comm_coding = django_filters.CharFilter(field_name='comm_coding', lookup_expr='exact')
    org_name = django_filters.CharFilter(field_name='org_name', lookup_expr='icontains')
    trash_can = django_filters.CharFilter(field_name='trash_can', lookup_expr='icontains')
    alarm_type = django_filters.CharFilter(field_name='alarm_type', lookup_expr='exact')

    class Meta:
        model = TrashCanAlarmRecord
        fields = ['start_date', 'end_date', 'street_coding', 'comm_coding', 'org_name', 'trash_can', 'alarm_type']


class OilWaterSeparationDeviceRecordFilter(django_filters.FilterSet):
    start_date = django_filters.DateTimeFilter(field_name='inbound_time__date', lookup_expr='gte')
    end_date = django_filters.DateTimeFilter(field_name='inbound_time__date', lookup_expr='lte')
    equipment_name = django_filters.CharFilter(field_name='equipment_name', lookup_expr='icontains')

    class Meta:
        model = OilWaterSeparationDeviceRecord
        fields = ['start_date', 'end_date', 'equipment_name']


class PendingCompanyRegistrationFilter(django_filters.FilterSet):
    company_name = django_filters.CharFilter(lookup_expr='icontains')
    org_name = django_filters.CharFilter(lookup_expr='icontains')
    submit_time_start = django_filters.DateTimeFilter(field_name='submit_time', lookup_expr='gte')
    submit_time_end = django_filters.DateTimeFilter(field_name='submit_time', lookup_expr='lte')
    
    class Meta:
        model = PendingCompanyRegistration
        fields = ['area_coding', 'street_coding', 'status', 'company_name', 'org_name', 'submit_time_start', 'submit_time_end']


class NonresidentCleaningPointRecordFilter(django_filters.FilterSet):
    cleaning_point_name = django_filters.CharFilter(method='cleaning_point_name_filter')

    def cleaning_point_name_filter(self, queryset, name, value):
        if value:
            objs = CleaningPoint.objects.filter(is_deleted__in=(0, 1), name__icontains=value)
            cleaning_point_ids = objs.values_list('cleaning_point_id', flat=True)
            queryset = queryset.filter(cleaning_point_id__in=cleaning_point_ids)
        return queryset

    org_name = django_filters.CharFilter(method='get_org_name')

    def get_org_name(self, queryset,name, value):
        """
        主体名查询
        """

        org = Organization.objects.filter(name__icontains=value)
        org_ids = set([i.org_id for i in org if i])
        if org_ids:
            queryset = queryset.filter(org_id__in=org_ids)
        else:
            queryset = queryset.none()
        return queryset

    weight_type = django_filters.CharFilter(method='weight_type_filter')

    def weight_type_filter(self, queryset, name, value):
        cleaning_point_record_ids = [i.cleaning_point_record_id for i in queryset]
        flow_query = CleaningPointRecordFlow.objects.using("tidb_ljfl_db").filter(is_deleted=0,
                                                                                  cleaning_point_record_id__in=cleaning_point_record_ids)
        if value == 'CAR':
            ids = flow_query.filter(weight_type='CAPACITY').values_list('cleaning_point_record_id', flat=True)
            print(ids)
            return queryset.exclude(cleaning_point_record_id__in=list(ids))
        elif value == 'CAPACITY':
            cleaning_point_record_ids = flow_query.filter(weight_type='CAPACITY').values_list(
                'cleaning_point_record_id', flat=True)
            return queryset.filter(cleaning_point_record_id__in=list(cleaning_point_record_ids))
        else:
            return queryset

    class Meta:
        model = CleaningPointRecord
        fields = '__all__'


class NonresidentCleaningPointTransRecordFilter(django_filters.FilterSet):
    cleaning_point_name = django_filters.CharFilter(method='factory_location_name_filter')

    def factory_location_name_filter(self, queryset, name, value):
        if value:
            objs = CleaningPointTransRecord.objects.filter(is_deleted__in=(0, 1), factory_location_name__icontains=value)
            _ids = objs.values_list('factory_location_id', flat=True)
            queryset = queryset.filter(cleaning_point_id__in=_ids)
        return queryset

    class Meta:
        model = CleaningPointTransRecord
        fields = '__all__'


class NonresidentCarRecordFilter(OtherCarRecordFilter):
    weight_type = django_filters.CharFilter(method='weight_type_filter')


    def weight_type_filter(self, queryset, name, value):
        car_record_ids = [i.car_record_id for i in queryset]
        flow_query = CarRecordFlow.objects.using("tidb_ljfl_db").filter(is_deleted=0, car_record_id__in=car_record_ids)
        if value == 'CAR':
            car_record_org_ids = flow_query.filter(weight_type='CAR').values_list('car_record_id', flat=True)
            return queryset.filter(car_record_id__in=list(car_record_org_ids))
        elif value == 'CAPACITY':
            car_record_org_ids = flow_query.filter(weight_type='CAPACITY').values_list('car_record_id', flat=True)
            return queryset.filter(car_record_id__in=list(car_record_org_ids))
        else:
            return queryset

    transport_company_name = django_filters.CharFilter(method='transport_company_name_filter')

    def transport_company_name_filter(self, queryset, name, value):
        transport_company_ids = TransportCompany.objects.filter(company__icontains=value, is_deleted=0). \
            values_list('transport_company_id', flat=True)
        car_ids = Car.objects.filter(is_deleted=0, transport_company_id__in=transport_company_ids). \
            values_list('car_id', flat=True)
        return queryset.filter(car_id__in=car_ids)

    transport_company_id = django_filters.CharFilter(method='transport_company_id_filter')

    def transport_company_id_filter(self, queryset, name, value):
        car_nums = Car.objects.filter(transport_company_id=value).values_list('car_num', flat=True)
        return queryset.filter(car_num__in=list(car_nums))



# 行政区编码表过滤器
class CodingFilter(django_filters.FilterSet):
    name = CharFilter(field_name="name", lookup_expr="icontains")
    # 行政区下级获取
    coding_like = NumberFilter(method="coding_filter")
    # 行政区上级获取
    coding_for_parent = NumberFilter(method="coding_for_parent_filter")

    def coding_filter(self, queryset, name, value):
        try:
            # if not cache.get('coding'):
            #     cache.set('coding', CityRegion.objects.all().order_by('id'), 60 * 60)
            cod = CityRegion.objects.filter(coding=value).order_by("id").first()
            if cod:
                if cod.grade == 1:  # 市级
                    queryset = queryset.filter(coding__gte=int(cod.coding))
                elif cod.grade == 2:  # 区级
                    queryset = queryset.filter(coding__gte=int(cod.coding), coding__lt=int(cod.coding) + 1000000)
                elif cod.grade == 3:  # 街道、乡镇级
                    queryset = queryset.filter(coding__gte=int(cod.coding), coding__lt=int(cod.coding) + 1000)
                elif cod.grade == 4:  # 社区、村级
                    queryset = queryset.filter(coding=cod.coding)
            return queryset
        except Exception as e:
            print(e)
            return queryset.none()

    def coding_for_parent_filter(self, queryset, name, value):
        region = CityRegion.objects.filter(coding=value).order_by("id").first()
        print(region, region.parent_id)
        if region:
            queryset = queryset.filter(region_id=region.parent_id)
            return queryset
        else:
            return queryset.none()


    virtual_area = CharFilter(method="virtual_area_filter")

    def virtual_area_filter(self, queryset, name, value):
        grade = self.request.GET.get("grade", 0)
        if value and int(grade) == 3 and value:
            streets = VirtualAreaStreet.objects.filter(virtual_area_code=value).values_list("street_code", flat=True)
            queryset = queryset.filter(coding__in=streets)
        return queryset

    class Meta:
        model = CityRegion
        fields = ["id", "coding", "coding_like", "name", "grade"]

