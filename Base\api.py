#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import json
import logging
from django.db import connections
from django.db.models.query import QuerySet
from django.shortcuts import HttpResponse
from django.utils.deprecation import MiddlewareMixin
from rest_framework import status
from rest_framework.request import QueryDict
from rest_framework.utils.serializer_helpers import ReturnDict
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination


class ConstCode:
    """
    返回码
    """
    Success = (status.HTTP_200_OK, 200, '处理成功.')

    Created = (status.HTTP_201_CREATED, 201, '创建成功.')

    Deleted = (status.HTTP_204_NO_CONTENT, 204, '删除成功.')

    BadRequest = (status.HTTP_400_BAD_REQUEST, 400, '提交的数据有误.')
    Existed = (status.HTTP_400_BAD_REQUEST, 4001, '对象已存在.')
    NotExisted = (status.HTTP_400_BAD_REQUEST, 4002, '对象不存在.')

    UnAuthorized = (status.HTTP_401_UNAUTHORIZED, 401, '认证失败.')

    Forbidden = (status.HTTP_403_FORBIDDEN, 403, '无法访问.')

    NotFound = (status.HTTP_404_NOT_FOUND, 404, '资源不存在.')

    MethodNotAllowed = (status.HTTP_405_METHOD_NOT_ALLOWED, 405, '不支持该操作.')

    TooManyRequests = (status.HTTP_429_TOO_MANY_REQUESTS, 429, '请求过于频繁, 请稍候重试.')

    ServerError = (status.HTTP_500_INTERNAL_SERVER_ERROR, 500, '服务错误.')

    __map__ = {
        status.HTTP_200_OK: '处理成功.',
        status.HTTP_201_CREATED: '创建成功.',
        status.HTTP_204_NO_CONTENT: '删除成功.',
        status.HTTP_400_BAD_REQUEST: '提交的数据有误.',
        status.HTTP_401_UNAUTHORIZED: '认证失败.',
        status.HTTP_403_FORBIDDEN: '无法访问.',
        status.HTTP_404_NOT_FOUND: '资源不存在.',
        status.HTTP_405_METHOD_NOT_ALLOWED: '不支持该操作.',
        status.HTTP_429_TOO_MANY_REQUESTS: '请求过于频繁，请稍候重试.',
        status.HTTP_500_INTERNAL_SERVER_ERROR: '服务错误.',
    }

    def get_default_msg(self, http_status_code):
        return self.__map__.get(http_status_code, '处理成功.')


ConstCode = ConstCode()


class ApiPageNumberPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100


def get_logger(logger_name='django'):
    """
    获取日志句柄
    :param logger_name:
    :return:
    """
    return logging.getLogger(logger_name)


# 项目默认日志
logger = get_logger()


def close_old_connections():
    """
    关闭不可用mysql连接
    :return:
    """
    for conn in connections.all():
        conn.close_if_unusable_or_obsolete()


def handle_db_connections(func):
    """
    自动管理Mysql连接
    :param func:
    :return:
    """

    def func_wrapper(*args, **kwargs):
        close_old_connections()
        result = func(*args, **kwargs)
        close_old_connections()
        return result

    return func_wrapper


def ApiResponse(code=ConstCode.Success, msg=None, data=None, errors=None):
    """
    Api返回内容
    :param code:
    :type code: ConstCode
    :param msg:
    :param data:
    :param errors:
    :return:
    """
    http_status_code, response_code, response_msg = code
    if msg:
        response_msg = msg
    response = dict(code=response_code, msg=response_msg)
    key = 'data'
    if http_status_code >= 300:
        key = 'errors'
    if data or data == []:
        response[key] = data
    if errors:
        response['errors'] = errors
    return Response(data=response, status=http_status_code)
