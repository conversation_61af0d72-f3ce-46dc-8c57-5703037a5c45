import datetime
import time

from dateutil.relativedelta import relativedelta
from django.db.models import Count, Sum
from rest_framework import viewsets
from rest_framework.response import Response
from django.db import connections
from django.db.models import Case, Q, When

from Base.api import get_logger
from Base.utils.tools import StandardResultsSetPagination, TimeFilterTools
from CollectManageApp.filters import TrashCanAlarmRecordFilter, OilWaterSeparationDeviceRecordFilter, \
    BagBreakingResidentRecordFilter, NonresidentPayOrderFilterForStatistic
from CollectManageApp.models import ResidentRecord, TrashCanAlarmRecord, OilWaterSeparationDeviceRecord, \
    NonresidentPayOrder, ResidentAccount, Resident
from CollectManageApp.models_base import Organization, RubbishType, OrgType, CityRegion, RtuDevice
from CollectManageApp.serializers import TrashCanAlarmRecordSer, OilWaterSeparationDeviceRecordSer, \
    BagBreakingResidentRecordSer
from CollectManageApp.kewei_db_models import (
    TrashStationModel
)

logger = get_logger('django')


class BagBreakingViewSet(viewsets.ModelViewSet):
    queryset = ResidentRecord.objects.using("tidb_ljfl_db").filter(is_deleted=0, creator="硕泰汇丰三方").order_by(
        "-create_time")
    serializer_class = BagBreakingResidentRecordSer
    pagination_class = StandardResultsSetPagination
    filter_class = BagBreakingResidentRecordFilter

    def get_queryset(self):
        rtu_device_name = self.request.GET.get('rtu_device_name')
        if rtu_device_name:
            device = TrashStationModel.objects.using("bagbreak_db").filter(device_name=rtu_device_name,
                                                                           status=0).first()
            if device:
                self.queryset = self.queryset.filter(device_id=device.rtu_device_id)
            else:
                self.queryset = self.queryset.none()

        return self.queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        # logger.info("bag_break_list_query=" + str(queryset.query))
        # logger.info("bag_break_list_query=")
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def cal_quality(self, request):
        queryset = self.filter_queryset(self.get_queryset())
        # null、空字符串、"无桶"、"空桶"等均当作"差"
        result = [dict(coin_type="优", count=0),
                  dict(coin_type="良", count=0),
                  dict(coin_type="中", count=0),
                  dict(coin_type="差", count=0)]
        for dat in queryset:
            if dat.quality not in ["优", "良", "中"]:
                result[3]["count"] += 1
            elif dat.quality == "优":
                result[0]["count"] += 1
            elif dat.quality == "良":
                result[1]["count"] += 1
            elif dat.quality == "中":
                result[2]["count"] += 1
        return Response(result)

    def cal_quality_trend(self, request):
        queryset = self.filter_queryset(self.get_queryset())

        quality_trend = dict()

        if request.GET.get("duration") == "daily":
            start_date = request.GET.get("start_date")
            end_date = request.GET.get("end_date")
            start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
            days = list()
            start_date_temp = start_date
            while start_date_temp <= end_date:
                days.append(start_date_temp)
                start_date_temp += datetime.timedelta(days=1)

            for day in days:
                timestamp_start = datetime.datetime.combine(day, datetime.datetime.min.time()).timestamp()
                timestamp_end = datetime.datetime.combine(day, datetime.datetime.max.time()).timestamp()
                new_queryset = queryset.filter(create_time__gte=timestamp_start, create_time__lte=timestamp_end)
                quality = 0
                for dat in new_queryset:
                    if dat.coin_type == "优":
                        quality += 4
                    elif dat.coin_type == "良":
                        quality += 3
                    elif dat.coin_type == "中":
                        quality += 2
                    else:
                        quality += 1
                quality_trend[day.strftime('%Y-%m-%d')] = quality

        if request.GET.get("duration") == "monthly":
            start_month = request.GET.get("start_month")
            end_month = request.GET.get("end_month")
            start_date = datetime.datetime.strptime(start_month, "%Y-%m").date()
            end_date = datetime.datetime.strptime(end_month, "%Y-%m").date()
            months = list()
            start_date_temp = start_date
            while start_date_temp <= end_date:
                months.append(f"{start_date_temp.year}-{start_date_temp.month}")
                start_date_temp += relativedelta(months=1)

            for month in months:
                start_date = datetime.datetime.strptime(month, "%Y-%m").date()
                end_date = start_date + relativedelta(months=1)
                timestamp_start = datetime.datetime.combine(start_date, datetime.datetime.min.time()).timestamp()
                timestamp_end = datetime.datetime.combine(end_date, datetime.datetime.max.time()).timestamp()
                new_queryset = queryset.filter(create_time__gte=timestamp_start, create_time__lte=timestamp_end)
                quality = 0
                for dat in new_queryset:
                    if dat.quality == "优":
                        quality += 4
                    elif dat.quality == "良":
                        quality += 3
                    elif dat.quality == "中":
                        quality += 2
                    else:
                        quality += 1
                quality_trend[month] = quality

        return Response(quality_trend)

    def cal_resident_quality(self, request):
        area_coding = request.GET.get("area_coding") or "110101000000"
        street_coding = request.GET.get("street_coding") or "110101008000"
        comm_coding = request.GET.get("comm_coding")
        car_num = request.GET.get("car_num")
        coin_type = request.GET.get("coin_type")
        type_id = request.GET.get("type_id")
        org_name = request.GET.get("org_name")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        page = request.GET.get("page") or 1
        page_size = request.GET.get("page_size") or 20
        page_size = int(page_size)
        offset = (int(page) - 1) * page_size

        where = " WHERE is_deleted=0 AND creator='硕泰汇丰三方'"
        if area_coding:
            where += f" AND area_coding='{area_coding}'"
        if street_coding:
            where += f" AND street_coding='{street_coding}'"
        if comm_coding:
            where += f" AND comm_coding='{comm_coding}'"
        if coin_type:
            where += f" AND quality='{coin_type}'"
        if type_id:
            where += f" AND type_id='{type_id}'"
        if car_num:
            resident = Resident.objects.using("tidb_ljfl_db").filter(card_num=car_num).first()
            where += f" AND resident_id='{resident.resident_id}'"
        if org_name:
            where += f" AND org_name like '%{org_name}%'"

        if start_date:
            start_timestamp, end_timestamp = TimeFilterTools.get_timestamp_duration(request)
            where += f" AND create_time>={start_timestamp}"
            where += f" AND create_time<={end_timestamp}"

        total_sql = f"""
         SELECT
            COUNT(distinct(resident_id)) as total
         FROM resident_record
         {where}
         AND  (coin_type='差' OR coin_type='' OR coin_type IS NULL OR coin_type='无桶' OR coin_type='空桶')
         """
        cursor = connections["tidb_ljfl_db"].cursor()
        cursor.execute(total_sql)
        total = cursor.fetchone()

        # null、空字符串、"无桶"、"空桶"等均当作"差"
        sql = f"""
         SELECT
            resident_id,
            COUNT(IF(coin_type='差' OR coin_type='' OR coin_type IS NULL OR coin_type='无桶' OR coin_type='空桶', TRUE, NULL)) AS total
         FROM resident_record
         {where}
         GROUP BY resident_id
         HAVING total > 0
         ORDER BY total desc
         LIMIT {offset}, {page_size}
         """

        cursor = connections["tidb_ljfl_db"].cursor()
        cursor.execute(sql)
        summary = cursor.fetchall()

        resident_ids = [s[0] for s in summary]
        resident_list = Resident.objects.using("tidb_ljfl_db").filter(resident_id__in=resident_ids).all()
        resident_dict = {s.resident_id: s for s in resident_list}
        comm_coding_list = [s.comm_coding for s in resident_list]
        street_coding_list = [s.street_coding for s in resident_list]
        org_id_list = [s.org_id for s in resident_list]
        street_coding_list.extend(comm_coding_list)
        city_region = CityRegion.objects.filter(coding__in=street_coding_list).values("coding", "name")
        city_region_dict = {c["coding"]: c["name"] for c in city_region}
        org_list = Organization.objects.filter(org_id__in=org_id_list).values("org_id", "name")
        org_dict = {c["org_id"]: c["name"] for c in org_list}

        result = []
        for row in summary:
            resident_id, count = row
            resident = resident_dict.get(resident_id)
            if resident:
                street_name = city_region_dict.get(resident.street_coding)
                comm_name = city_region_dict.get(resident.comm_coding)
                org_name = org_dict.get(resident.org_id) or ""
                resident_name = resident.name
                street_coding = resident.street_coding
                comm_coding = resident.comm_coding or ""
            else:
                street_name = ""
                comm_name = ""
                org_name = ""
                resident_name = ""
                street_coding = ""
                comm_coding = ""
            tmp = dict(
                resident_id=resident_id,
                street_name=street_name,
                comm_name=comm_name,
                org_name=org_name,
                count=count,
                resident_name=resident_name,
                comm_coding=comm_coding,
                street_coding=street_coding,
            )
            result.append(tmp)

        return Response(dict(count=total[0], data=result))

    def get_days(self, request):
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")
        start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
        end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
        start_date_temp = start_date
        days = list()
        while start_date_temp <= end_date:
            days.append(start_date_temp)
            start_date_temp += datetime.timedelta(days=1)
        return days

    def get_months(self, request):
        start_month = request.GET.get("start_month")
        end_month = request.GET.get("end_month")
        start_date = datetime.datetime.strptime(start_month, "%Y-%m").date()
        end_date = datetime.datetime.strptime(end_month, "%Y-%m").date()
        start_date_temp = start_date
        months = list()
        while start_date_temp <= end_date:
            months.append(start_date_temp)
            start_date_temp += relativedelta(months=1)
        return months

    def get_year(self, request):
        start_year = request.GET.get("start_year")
        end_year = request.GET.get("end_year")
        start_year = datetime.datetime.strptime(start_year, "%Y").date()
        end_year = datetime.datetime.strptime(end_year, "%Y").date()
        start_date_temp = start_year
        years = list()
        while start_date_temp <= end_year:
            years.append(start_date_temp)
            start_date_temp += relativedelta(years=1)
        return years

    def bag_breaking_partin_rate(self, request):
        area_coding = request.GET.get("area_coding") or "110101000000"
        street_coding = request.GET.get("street_coding") or "110101008000"
        comm_coding = request.GET.get("comm_coding") or ""
        org_name = request.GET.get("org_name") or ""
        rtotal = Resident.objects.filter(
            area_coding=area_coding, street_coding=street_coding, is_deleted=0
        ).aggregate(count=Count("*"))

        queryset = self.get_queryset().filter(area_coding=area_coding).order_by()
        if comm_coding:
            queryset = queryset.filter(comm_coding=comm_coding)
        if org_name:
            org_obj = Organization.objects.filter(name__contains=org_name).first()
            queryset = queryset.filter(org_name=org_obj.name if org_obj else "default")

        result = dict()
        rtotal = rtotal.get("count") or 0
        if request.GET.get("duration") == "daily":
            days = self.get_days(request)
            for day in days:
                end_date = day + relativedelta(days=1)
                ds = datetime.datetime.combine(day, datetime.datetime.min.time()).timestamp()
                de = datetime.datetime.combine(end_date, datetime.datetime.min.time()).timestamp()
                new_queryset = queryset.filter(create_time__gte=ds, create_time__lt=de)
                partin_count = new_queryset.aggregate(count=Count('resident_id', distinct=True))
                partin_count = partin_count.get("count") or 0
                result[day.strftime('%Y-%m-%d')] = "{:.2f}".format((partin_count / rtotal) * 100 if rtotal else 0.0)

        if request.GET.get("duration") == "monthly":
            months = self.get_months(request)
            for month in months:
                # start_date = datetime.datetime.strptime(month, "%Y-%m").date()
                end_month = month + relativedelta(months=1)
                ds = datetime.datetime.combine(month, datetime.datetime.min.time()).timestamp()
                de = datetime.datetime.combine(end_month, datetime.datetime.min.time()).timestamp()
                new_queryset = queryset.filter(create_time__gte=ds, create_time__lt=de)
                partin_count = new_queryset.aggregate(count=Count('resident_id', distinct=True))
                partin_count = partin_count.get("count") or 0
                result[month.strftime('%Y-%m')] = "{:.2f}".format((partin_count / rtotal) * 100 if rtotal else 0.0)

        if request.GET.get("duration") == "yearly":
            years = self.get_year(request)
            for year in years:
                end_year = year + relativedelta(years=1)
                ds = datetime.datetime.combine(year, datetime.datetime.min.time()).timestamp()
                de = datetime.datetime.combine(end_year, datetime.datetime.min.time()).timestamp()
                new_queryset = queryset.filter(create_time__gte=ds, create_time__lt=de)
                partin_count = new_queryset.aggregate(count=Count('resident_id', distinct=True))
                partin_count = partin_count.get("count") or 0
                result[year.strftime('%Y')] = "{:.2f}".format((partin_count / rtotal) * 100 if rtotal else 0.0)

        return Response(result)

    def get_last_year_month_weight(self, queryset, curr_date):
        """当前日期 上一年同月份日均重量"""
        start_month = curr_date - relativedelta(years=1) - relativedelta(days=curr_date.day - 1)
        end_month = start_month + relativedelta(months=1)
        ds = datetime.datetime.combine(start_month, datetime.datetime.min.time()).timestamp()
        de = datetime.datetime.combine(end_month, datetime.datetime.min.time()).timestamp()
        weight = queryset.filter(create_time__gte=ds, create_time__lt=de).aggregate(weight=Sum("weight"))
        days = (end_month - start_month).days
        # print(days)
        # print(queryset.filter(create_time__gte=ds, create_time__lt=de).query)
        # print(ds, de, end_month)
        return (weight.get("weight") or 0 / days) if days else 0

    def get_month_weight(self, queryset, curr_date):
        """当前月份第一天 到 当前日期 日均重量"""
        start_date = curr_date - relativedelta(days=curr_date.day - 1)
        end_date = curr_date
        if end_date == curr_date:
            end_date = curr_date + relativedelta(days=1)

        ds = datetime.datetime.combine(start_date, datetime.datetime.min.time()).timestamp()
        de = datetime.datetime.combine(end_date, datetime.datetime.min.time()).timestamp()
        weight = queryset.filter(create_time__gte=ds, create_time__lt=de).aggregate(weight=Sum("weight"))
        days = (end_date - start_date).days
        # print(days)
        # print(ds, de, start_date, end_date)
        return (weight.get("weight") or 0 / days) if days else 0

    def bag_breaking_year_rate(self, request):
        """同比"""
        area_coding = request.GET.get("area_coding") or "110101000000"
        comm_coding = request.GET.get("comm_coding") or ""
        org_name = request.GET.get("org_name") or ""

        queryset = self.get_queryset().filter(area_coding=area_coding).order_by()
        if comm_coding:
            queryset = queryset.filter(comm_coding=comm_coding)
        if org_name:
            org_obj = Organization.objects.filter(name__contains=org_name).first()
            queryset = queryset.filter(org_name=org_obj.name if org_obj else "default")

        result = dict()
        month_weight_dict = dict()
        if request.GET.get("duration") == "daily":
            days = self.get_days(request)
            for day in days:
                key = "{}-{}".format(day.year, day.month)
                if key in month_weight_dict:
                    last_year_weight = month_weight_dict.get(key)
                else:
                    last_year_weight = self.get_last_year_month_weight(queryset, day)
                    month_weight_dict[key] = last_year_weight
                curr_weight = self.get_month_weight(queryset, day)
                rate = ((last_year_weight - curr_weight) / last_year_weight) * 100 if last_year_weight else 0
                result[day.strftime('%Y-%m-%d')] = "{:.2f}".format(rate)

        if request.GET.get("duration") == "monthly":
            months = self.get_months(request)
            for month in months:
                last_year_weight = self.get_last_year_month_weight(queryset, month)
                curr_weight = self.get_month_weight(queryset, month)
                rate = ((last_year_weight - curr_weight) / last_year_weight) * 100 if last_year_weight else 0
                result[month.strftime('%Y-%m')] = "{:.2f}".format(rate)

        if request.GET.get("duration") == "yearly":
            result = dict()

        return Response(result)

    def get_last_three_month_weight(self, queryset, curr_date):
        """当前日期、月份，  前三个月生活垃圾日均投放量平均值"""
        start_month = curr_date - relativedelta(months=3) - relativedelta(days=curr_date.day - 1)
        end_month = curr_date - relativedelta(days=curr_date.day - 1)
        ds = datetime.datetime.combine(start_month, datetime.datetime.min.time()).timestamp()
        de = datetime.datetime.combine(end_month, datetime.datetime.min.time()).timestamp()
        weight = queryset.filter(create_time__gte=ds, create_time__lt=de).aggregate(weight=Sum("weight"))
        days = (end_month - start_month).days
        # print(days)
        # print(ds, de, end_month, start_month, "-----")
        return (weight.get("weight") or 0 / days) if days else 0

    def bag_breaking_cycle_rate(self, request):
        """环比"""
        area_coding = request.GET.get("area_coding") or "110101000000"
        comm_coding = request.GET.get("comm_coding") or ""
        org_name = request.GET.get("org_name") or ""

        queryset = self.get_queryset().filter(area_coding=area_coding).order_by()
        if comm_coding:
            queryset = queryset.filter(comm_coding=comm_coding)
        if org_name:
            org_obj = Organization.objects.filter(name__contains=org_name).first()
            queryset = queryset.filter(org_name=org_obj.name if org_obj else "default")

        result = dict()
        month_weight_dict = dict()
        if request.GET.get("duration") == "daily":
            days = self.get_days(request)
            for day in days:
                key = "{}-{}".format(day.year, day.month)
                if key in month_weight_dict:
                    last_year_weight = month_weight_dict.get(key)
                else:
                    last_year_weight = self.get_last_three_month_weight(queryset, day)
                    month_weight_dict[key] = last_year_weight
                curr_weight = self.get_month_weight(queryset, day)
                rate = ((last_year_weight - curr_weight) / last_year_weight) * 100 if last_year_weight else 0
                result[day.strftime('%Y-%m-%d')] = "{:.2f}".format(rate)

        if request.GET.get("duration") == "monthly":
            months = self.get_months(request)
            for month in months:
                last_year_weight = self.get_last_three_month_weight(queryset, month)
                curr_weight = self.get_month_weight(queryset, month)
                rate = ((last_year_weight - curr_weight) / last_year_weight) * 100 if last_year_weight else 0
                result[month.strftime('%Y-%m')] = "{:.2f}".format(rate)

        if request.GET.get("duration") == "yearly":
            result = dict()

        return Response(result)

    def bag_breaking_images_export(self, request):
        queryset = self.filter_queryset(self.get_queryset())
        queryset = queryset.filter(cover__gt="").filter(~Q(cover__isnull=True)).all()
        queryset = queryset.values("cover", "create_time")
        result = []
        for item in queryset:
            cover = item["cover"]
            if not cover:
                continue
            images = cover.split(",")
            name = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(item["create_time"]))
            tmp = dict(
                image_urls=images,
                name=name
            )
            result.append(tmp)
        return Response(result)


class BagBreakingStatisticViewSet(viewsets.ModelViewSet):
    queryset = ResidentRecord.objects.using("tidb_ljfl_db").filter(is_deleted=0, creator="硕泰汇丰三方")
    # filter_fields = ['area_coding', "street_coding"]  # 过滤器参数
    filter_class = BagBreakingResidentRecordFilter

    def cal_bag_breaking_record_statistic(self, request):
        queryset = self.filter_queryset(self.get_queryset())
        rtu_device_name = request.GET.get('rtu_device_name')
        if rtu_device_name:
            device = TrashStationModel.objects.using("bagbreak_db").filter(device_name=rtu_device_name,
                                                                           status=0).first()
            if device:
                queryset = queryset.filter(device_id=device.rtu_device_id)
            else:
                queryset = queryset.none()
        resident_count = queryset.aggregate(resident_count=Count('resident_id', distinct=True))
        record_count = queryset.aggregate(record_count=Count('id'))
        total_weight = queryset.aggregate(total_weight=Sum('weight'))
        data = {
            "resident_count": resident_count["resident_count"] if resident_count["resident_count"] else 0,
            "record_count": record_count["record_count"] if record_count["record_count"] else 0,
            "total_weight": round(total_weight["total_weight"] / 1000, 2) if total_weight["total_weight"] else 0
        }

        return Response(data)


class TrashCanAlarmViewSet(viewsets.ModelViewSet):
    queryset = TrashCanAlarmRecord.objects.all().order_by("-alarm_time")
    serializer_class = TrashCanAlarmRecordSer
    pagination_class = StandardResultsSetPagination
    filter_class = TrashCanAlarmRecordFilter

    def get_queryset(self):
        street_coding = self.request.GET.get('street_coding')
        if street_coding:
            self.queryset = self.queryset.filter(street_coding=street_coding)
        return self.queryset

    def cal_trash_can_alarm_record_statistic(self, request):
        queryset = self.filter_queryset(self.get_queryset())

        queryset = TrashCanAlarmRecord.objects.all()
        queryset = self.filter_queryset(queryset)
        data = queryset.values("alarm_type").annotate(alarm_count=Count("id"))
        statistic = {a["alarm_type"]: a["alarm_count"] for a in data}

        alarm_type_dict = TrashCanAlarmRecord.TrashCanAlarmType.AlarmTypeDict
        result = []
        for k, v in alarm_type_dict.items():
            result.append(dict(
                alarm_type=v,
                alarm_count=statistic.get(k) or 0,
            ))
        return Response(result)


class OilWaterSeparationDeviceViewSet(viewsets.ModelViewSet):
    queryset = OilWaterSeparationDeviceRecord.objects.all().order_by("-inbound_time")
    serializer_class = OilWaterSeparationDeviceRecordSer
    pagination_class = StandardResultsSetPagination
    filter_class = OilWaterSeparationDeviceRecordFilter

    def cal_separation_device_record_statistic(self, request):
        queryset = self.filter_queryset(self.get_queryset())
        data = queryset.aggregate(inbound_weight=Sum("inbound_weight"), outbound_weight=Sum("outbound_weight"))
        rate = round(data["outbound_weight"] / data["inbound_weight"], 2) if data["inbound_weight"] else 0
        inbound_weight = round(data["inbound_weight"], 2) if data["inbound_weight"] else 0
        outbound_weight = round(data["outbound_weight"], 2) if data["outbound_weight"] else 0
        new_data = dict(inbound_weight=inbound_weight,
                        outbound_weight=outbound_weight,
                        total_weight=inbound_weight,
                        rate=rate)
        return Response(new_data)


class NonOrgPayViewSet(viewsets.ModelViewSet):
    queryset = NonresidentPayOrder.objects.filter(is_deleted=0)
    filter_class = NonresidentPayOrderFilterForStatistic

    def cal_non_org_pay_statistic(self, resquest):
        queryset = self.filter_queryset(self.get_queryset())
        queryset = self.get_coding_queryset(queryset)
        total_pay_price = queryset.aggregate(total_pay_price=Sum("pay_price"))
        total_pay_price = round(total_pay_price["total_pay_price"] / 100, 2) if total_pay_price[
            "total_pay_price"] else 0

        queryset = NonresidentPayOrder.objects.filter(status=1)
        queryset = self.filter_queryset(queryset)
        queryset = self.get_coding_queryset(queryset)

        data = queryset.values("pay_type").annotate(count=Count("id"))
        new_data = [dict(pay_type="预交费", count=0),
                    dict(pay_type="即时缴费", count=0),
                    dict(pay_type="公对公", count=0)]
        for dat in data:
            if dat["pay_type"] == "预付订单" or dat["pay_type"] == "预付金额":
                new_data[0]["count"] += dat["count"]
            if dat["pay_type"] == "即时支付":
                new_data[1]["count"] += dat["count"]
            if dat["pay_type"] == "公对公支付订单" or dat["pay_type"] == "公对公支付":
                new_data[2]["count"] += dat["count"]

        result = dict(total_pay_price=total_pay_price, data=new_data)
        return Response(result)

    def get_coding_queryset(self, queryset):
        area_coding = self.request.GET.get('area_coding', "")
        street_coding = self.request.GET.get('street_coding', "")
        org_obj = Organization.objects.filter(is_deleted=0)
        if area_coding:
            org_obj = org_obj.filter(area_coding=area_coding)
        if street_coding:
            org_obj = org_obj.filter(street_coding=street_coding)

        org_ids = org_obj.values_list('org_id', flat=True).distinct()
        queryset = queryset.filter(org_id__in=list(org_ids))
        return queryset
