# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each Foreign<PERSON>ey has `on_delete` set to the desired behavior.
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from __future__ import unicode_literals

import uuid

from django.db import models
from django.utils import timezone


def uuid4():
    return uuid.uuid4().hex


class TrashStationModel(models.Model):
    """破袋机桶站智能垃圾桶信息"""

    station_id = models.Char<PERSON>ield(verbose_name="桶站ID", max_length=32, unique=True)
    rtu_device_id = models.CharField(verbose_name="桶ID", max_length=32, unique=True)
    terminal_id = models.CharField(verbose_name="终端ID", max_length=32, unique=True)
    name = models.Char<PERSON><PERSON>(verbose_name="桶站名称", max_length=32)
    device_name = models.Char<PERSON><PERSON>(verbose_name="桶名称", max_length=32)
    latitude = models.DecimalField(
        max_digits=20,
        decimal_places=10,
        blank=False,
        null=False,
        default=0.0,
        verbose_name="纬度",
    )
    longitude = models.DecimalField(
        max_digits=20,
        decimal_places=10,
        blank=False,
        null=False,
        default=0.0,
        verbose_name="经度",
    )
    area_coding = models.CharField(max_length=45, blank=True, null=True, default="")
    street_coding = models.CharField(max_length=45, blank=True, null=True, default="")
    comm_coding = models.CharField(max_length=45, blank=True, null=True, default="")
    address = models.CharField(
        verbose_name="所在位置", max_length=32, blank=False, null=False, default=""
    )
    org_id = models.CharField(
        verbose_name="主体ID", max_length=32, blank=False, null=False, default=""
    )
    status = models.PositiveSmallIntegerField("状态", null=False, blank=True, default="0")

    class Meta:
        managed = False
        db_table = "trash_station"
        app_label = "bagbreak_db"


class BagbreakGpsModel(models.Model):
    """gps"""

    station_name = models.CharField(max_length=32, verbose_name="桶站名")
    terminal_id = models.CharField(max_length=32, verbose_name="")
    sensor_id = models.CharField(max_length=255, verbose_name="传感器编号")
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=False, null=False, default=0.0,
                                   verbose_name="纬度")
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=False, null=False, default=0.0,
                                    verbose_name="经度")
    is_deleted = models.PositiveSmallIntegerField(default=0)
    gather_time = models.DateTimeField(verbose_name="数据收集时间")
    created_at = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(verbose_name="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "gps"
        app_label = "bagbreak_db"


class FamilyModel(models.Model):
    """家庭组"""

    name = models.CharField(max_length=45, blank=True, null=True)
    farmily_id = models.CharField(max_length=45, blank=True, null=True)
    card_num = models.CharField(max_length=45, blank=True, null=True)
    card_id_num = models.CharField(max_length=45, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    reward_coin = models.FloatField(blank=True, null=True)
    civilization_coin = models.FloatField(blank=True, null=True)
    credit_coin = models.FloatField(blank=True, null=True)
    reward_coin_amount = models.FloatField(blank=True, null=True)
    civilization_coin_amount = models.FloatField(blank=True, null=True)
    credit_coin_amount = models.FloatField(blank=True, null=True)
    # 家庭地址
    address = models.CharField(max_length=250, blank=True, null=True)
    building_num = models.CharField(max_length=250, blank=True, null=True)
    unit_num = models.CharField(max_length=45, blank=True, null=True)
    room_num = models.CharField(max_length=45, blank=True, null=True)
    phone = models.CharField(max_length=45, blank=True, null=True)
    throw_num = models.IntegerField(blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    house_header_name = models.CharField(max_length=45, blank=True, null=True)
    house_header_id = models.CharField(max_length=20, blank=True, null=True)
    wechat_id = models.CharField(max_length=45, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "family"
        app_label = "bagbreak_db"


class ResidentModel(models.Model):
    """居民信息"""

    # 居民uuid
    resident_id = models.CharField(max_length=45, default="")
    # 居民名字
    name = models.CharField(max_length=45, blank=True, null=True)
    # 家庭uuid
    farmily_id = models.CharField(max_length=45, blank=True, null=True)
    # 主体uuid
    org_id = models.CharField(max_length=45, blank=True, null=True)
    # 区级编码
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    # 街道编码
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    # 社区编码
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    # 居民卡号
    card_num = models.CharField(max_length=45, blank=True, null=True)
    # 居民
    card_id_num = models.CharField(max_length=45, blank=True, null=True)
    # 奖励积分
    reward_coin = models.FloatField(blank=True, null=True)
    # 文明积分
    civilization_coin = models.FloatField(blank=True, null=True)
    # 信用积分
    credit_coin = models.FloatField(blank=True, null=True)
    # 奖励积分总额
    reward_coin_amount = models.FloatField(blank=True, null=True)
    # 文明积分总额
    civilization_coin_amount = models.FloatField(blank=True, null=True)
    # 信用积分总额
    credit_coin_amount = models.FloatField(blank=True, null=True)
    # 居民地址
    address = models.CharField(max_length=250, blank=True, null=True)
    # 建筑号
    building_num = models.CharField(max_length=45, blank=True, null=True)
    # 具体建筑名
    unit_num = models.CharField(max_length=45, blank=True, null=True)
    # 门牌号
    room_num = models.CharField(max_length=45, blank=True, null=True)
    # 手机号
    phone = models.CharField(max_length=250, blank=True, null=True)
    throw_num = models.IntegerField(blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    # 备注
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    wechat_id = models.CharField(max_length=45, blank=True, null=True)
    alipay_id = models.CharField(max_length=20, blank=True, null=True)
    wechat_picture = models.CharField(max_length=200, blank=True, null=True)
    alipay_picture = models.CharField(max_length=200, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "resident"
        app_label = "bagbreak_db"


class ResidentRecordModel(models.Model):
    """居民投放记录"""

    resident_record_id = models.CharField(max_length=45)
    resident_id = models.CharField(max_length=45)
    type_id = models.CharField(max_length=45, blank=True, null=True)
    sub_type_id = models.CharField(max_length=45, blank=True, null=True)
    weight = models.FloatField(verbose_name="重量单位是(克)", blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    org_name = models.CharField(max_length=45, blank=True, null=True)
    device_id = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    reward_coin = models.FloatField(blank=True, null=True)
    civilization_coin = models.FloatField(blank=True, null=True)
    credit_coin = models.FloatField(blank=True, null=True)
    coin_type = models.CharField(max_length=45, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    latitude = models.DecimalField(
        max_digits=20,
        decimal_places=10,
        blank=False,
        null=False,
        default=0.0,
        verbose_name="纬度",
    )
    longitude = models.DecimalField(
        max_digits=20,
        decimal_places=10,
        blank=False,
        null=False,
        default=0.0,
        verbose_name="经度",
    )
    stream_number = models.CharField(max_length=45, blank=True, null=True)
    quality = models.CharField(
        max_length=45, blank=True, null=True, verbose_name="质量判定"
    )
    cover = models.CharField(
        max_length=1000, blank=True, null=True, verbose_name="破袋照片"
    )

    class Meta:
        managed = False
        db_table = "resident_record"
        app_label = "bagbreak_db"


class ResidentCoinRecordModel(models.Model):
    """积分记录详情"""

    resident_coin_record_id = models.CharField(max_length=45)
    resident_record_id = models.CharField(max_length=45)
    resident_id = models.CharField(max_length=45)
    farmily_id = models.CharField(max_length=45, default='')
    type_id = models.CharField(max_length=45, blank=True, null=True)
    sub_type_id = models.CharField(max_length=45, blank=True, null=True)
    weight = models.FloatField(blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    org_name = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    reward_coin = models.FloatField(blank=True, null=True)
    civilization_coin = models.FloatField(blank=True, null=True)
    credit_coin = models.FloatField(blank=True, null=True)
    fore_reward_coin = models.FloatField(default=0, blank=True, null=True)
    fore_civilization_coin = models.FloatField(default=0, blank=True, null=True)
    fore_credit_coin = models.FloatField(default=0, blank=True, null=True)
    custom_data = models.CharField(max_length=1000)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "resident_coin_record"
        app_label = "bagbreak_db"


class ResidentBindCardRecordModel(models.Model):
    """居民绑卡/解绑记录"""

    resident_id = models.CharField(max_length=45)
    name = models.CharField(max_length=45)
    family_id = models.CharField(max_length=45, blank=True, null=True)
    card_num = models.CharField(max_length=45, blank=True, null=True)
    card_id_num = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    reward_coin = models.FloatField(blank=True, null=True)
    phone = models.FloatField(blank=True, null=True)
    bind_type = models.FloatField(blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "resident_bind_card_record"
        app_label = "bagbreak_db"


class ResidentSignRecordModel(models.Model):
    """
    居民活动签到记录
    """
    activity_uuid = models.CharField(max_length=32, verbose_name='活动uuid')
    activity_integral_uuid = models.CharField(max_length=32, verbose_name='活动签到积分uuid')
    resident_uuid = models.CharField(max_length=32, verbose_name='签到时间段(开始)')
    sign_stamp = models.IntegerField(verbose_name='签到时间')
    history_integral = models.IntegerField(verbose_name='居民签到历史积分')

    class Meta:
        managed = False
        db_table = 'resident_sign_record'
        app_label = "bagbreak_db"
