#!/home/<USER>/venv/bin/python3
# coding: utf-8
from __future__ import absolute_import

import json
import requests
from django.conf import settings


class BaseServerError(Exception):

    def __init__(self, response=None, code=500, message=''):
        super(BaseServerError, self).__init__()
        self.response = response
        self.code = code
        if message:
            message = '基础服务异常[{}]!'.format(message)
        else:
            message = '基础服务异常!'
        self.message = message


class BaseServer:
    def __init__(self):
        self.host = settings.BASE_HOST

    @staticmethod
    def _get_response(url, params=None):
        try:
            response = requests.get(url, params=params)
        except Exception as e:
            raise BaseServerError(message='BaseServerError: {}'.format(str(e)))

        if response.status_code != 200 and not response.content:
            raise BaseServerError(message='http status:{}'.format(response.status_code))

        content = response.content.decode('utf-8')
        result = json.loads(content)
        if result.get('code') != 0:
            raise BaseServerError(response=content, message=result.get('msg'), code=result.get('code'))

        return result.get('data')

    @staticmethod
    def _post_response(url, data=None, **kwargs):
        try:
            response = requests.post(url, data=data, **kwargs)
        except Exception as e:
            raise BaseServerError(message='BaseServerError: {}'.format(str(e)))

        if response.status_code != 200 and not response.content:
            raise BaseServerError(message='http status:{}'.format(response.status_code))

        content = response.content.decode('utf-8')
        result = json.loads(content)
        if result.get('code') != 0:
            raise BaseServerError(response=content, message=result.get('msg'), code=result.get('code'))

        return result.get('data')

    def get_city_region(self, params):
        """获取行政划分"""
        url = 'http://{host}/v1/base/coding/'.format(host=self.host)
        return self._get_response(url, params=params)

    def get_org_type(self, params):
        """获取主体类型"""
        url = 'http://{host}/v1/base/orgType/'.format(host=self.host)
        return self._get_response(url, params=params)
