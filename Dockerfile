# FROM  harbor.ztbory.com/sys/python:3.6-slim-nonresident
# FROM harbor.ztbory.com/sys/python:3.11.10
FROM harbor.ztbory.com/sys/python:3.11.10-nonresident
ADD . /data/
WORKDIR /data/
RUN pip3 install --no-cache-dir -r /data/requirements.txt
# RUN yum install mysql-devel  gcc-c++ -y
# RUN apt update
# RUN pip3 install --no-cache-dir -r /data/requirements.txt && pip3 install grpcio==1.68.0 && pip3 install grpcio-tools==1.68.0

CMD ["/bin/sh","run.sh"]