#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import *

DEBUG = False

EMAIL_ENABLE = True  # 邮件通知是否可用
DATABASES = {
    'default': {},
    'ljfl_declare_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_declare_db',
        'USER': 'ljfldb',
        'HOST': '************',
        'PASSWORD': 'ljfl_db20201210',
        'PORT': 33407,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'ljfldb',
        'HOST': '************',
        'PASSWORD': 'ljfl_db20201210',
        'PORT': 33407,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'tidb_ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'ljfldb',
        'HOST': '************',
        'PASSWORD': 'ljfl_db20201210',
        'PORT': 33407,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'transport_db': {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': '************',  # 测试
        'PORT': 33407,
        'USER': 'ljfldb',
        'PASSWORD': 'ljfl_db20201210',
        'NAME': 'transport_db',
        'OPTIONS': {'charset': 'utf8mb4'},
    },
    'ljfl_db_replica': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'ljfldb',
        'HOST': '************',
        'PASSWORD': 'ljfl_db20201210',
        'PORT': 33407,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
}

# CACHES = {
#     'default': {
#         'BACKEND': 'django_redis_sentinel.cache.RedisSentinelCache',
#         'KEY_PREFIX': 'apiProduction:collectManageAPPV3',
#         'LOCATION': [
#             ('************', 16380),
#             ('************', 16380),
#             ('***********', 16380)
#         ],
#         'OPTIONS': {
#             'CLIENT_CLASS': 'django_redis_sentinel.client.SentinelClient',
#             'SENTINEL_SERVICE_NAME': 'ztbrmaster',
#             'REDIS_CLIENT_KWARGS': {
#                 'db': 4
#             }
#         }
#     }
# }

# 管理授权
AUTH_APPID = *********
AUTH_APPSECRET = 'jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7'
AUTH_HOST = 'dev-auth.ztbory.com'
# 基础服务
BASE_HOST = '***********:8290'
# OSS服务
OSS_HOST = 'filemanager.ztbory.com'
# 资质审核服务
QUALIFI_HOST = '***********:8520'

COMPANY_SYS_IP = 'http://***********:8520'

# 市级服务
CITY_IP = 'http://************:8094'

# 统一登陆服务
ACCOUNT_SERVER_URL = "http://***********:8001"

INTERNAL_NETWORKS = ["***********/24", "************"]
