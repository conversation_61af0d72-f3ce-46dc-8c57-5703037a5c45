import datetime
import threading
import time

from Base.utils.base import str_quarter_to_start_date, str_week_to_start_date, str_to_date, str_month_to_start_date, str_year_to_start_date
from Base.utils.const import ConstDuration, ConstRubbishType
import math
from rest_framework.pagination import PageNumberPagination, LimitOffsetPagination
from collections import OrderedDict
from rest_framework.response import Response
from CollectManageApp.models_base import RubbishType
import math
import uuid


class TimeFilterTools:
    @staticmethod
    def create_time_filter(request, qs, duration):
        """
        过滤create_time,正常时间格式转时间戳
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """

        if duration == ConstDuration.DAILY:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            # 将时间字符串转成时间撮
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(create_time__gte=timestamp_ds,
                           create_time__lt=timestamp_de)
            return qs

        # 周过滤
        elif duration == ConstDuration.WEEKLY:
            start_day = str_week_to_start_date(request.GET.get('start_week'))
            end_day = str_week_to_start_date(request.GET.get('end_week'), offset_weeks=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(create_time__gte=timestamp_ds,
                           create_time__lt=timestamp_de)
            return qs

        # 月份过滤
        elif duration == ConstDuration.MONTHLY:
            ds = request.query_params.get("start_month")
            de = request.query_params.get("end_month")
            year = int(de.split("-")[0])
            month = int(de.split("-")[1])
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
            new_de = str(year) + "-" + str(month)
            # 将时间字符串转成时间撮
            timeArray_ds = time.strptime(ds, "%Y-%m")
            timeArray_de = time.strptime(new_de, "%Y-%m")
            timestamp_ds = time.mktime(timeArray_ds)
            timestamp_de = time.mktime(timeArray_de)
            qs = qs.filter(create_time__gte=timestamp_ds,
                           create_time__lt=timestamp_de)
            return qs

        # 季度过滤
        elif duration == ConstDuration.QUARTERLY:
            start_day = str_quarter_to_start_date(request.GET.get('start_quarter'))
            end_day = str_quarter_to_start_date(request.GET.get('end_quarter'), offset_quarter=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(create_time__gte=timestamp_ds,
                           create_time__lt=timestamp_de)
            return qs

        # 年份过滤
        elif duration == ConstDuration.YEARLY:
            ds = request.query_params.get('start_year')
            de = request.query_params.get('end_year')
            new_de = str(int(de) + 1)
            # 将时间字符串转成时间撮
            timeArray_ds = time.strptime(ds, "%Y")
            timeArray_de = time.strptime(new_de, "%Y")
            timestamp_ds = time.mktime(timeArray_ds)
            timestamp_de = time.mktime(timeArray_de)
            qs = qs.filter(create_time__gte=timestamp_ds,
                           create_time__lt=timestamp_de)
            return qs
        else:
            return qs

    @staticmethod
    def weightptime_filter(request, qs, duration):
        """
        过滤weightptime,正常时间格式
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """

        if duration == ConstDuration.DAILY:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            qs = qs.filter(weightptime__gte=ds,
                           weightptime__lt=de)
            return qs

        # 周过滤
        elif duration == ConstDuration.WEEKLY:
            start_day = str_week_to_start_date(request.GET.get('start_week'))
            end_day = str_week_to_start_date(request.GET.get('end_week'), offset_weeks=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            qs = qs.filter(weightptime__gte=ds,
                           weightptime__lt=de)
            return qs

        # 月份过滤
        elif duration == ConstDuration.MONTHLY:
            ds = request.query_params.get("start_month")
            de = request.query_params.get("end_month")
            year = int(de.split("-")[0])
            month = int(de.split("-")[1])
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
            start_ds = ds + "-" + "1"
            new_de = str(year) + "-" + str(month) + "-" + "1"
            qs = qs.filter(weightptime__gte=start_ds,
                           weightptime__lt=new_de)
            return qs

        # 季度过滤
        elif duration == ConstDuration.QUARTERLY:
            start_day = str_quarter_to_start_date(request.GET.get('start_quarter'))
            end_day = str_quarter_to_start_date(request.GET.get('end_quarter'), offset_quarter=1)
            qs = qs.filter(weightptime__gte=start_day,
                           weightptime__lt=end_day)
            return qs

        # 年份过滤
        elif duration == ConstDuration.YEARLY:
            ds = request.query_params.get('start_year')
            de = request.query_params.get('end_year')
            new_de = str(int(de) + 1)
            start_day = ds + '-01-01'
            start_end = new_de + '-01-01'
            qs = qs.filter(weightptime__gte=start_day,
                           weightptime__lt=start_end)
            return qs
        else:
            return qs

    @staticmethod
    def new_create_time_filter(request, qs, duration):
        """
        过滤create_time,正常时间格式
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """

        if duration == ConstDuration.DAILY:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            qs = qs.filter(create_time__gte=ds,
                           create_time__lt=de)
            return qs

        # 周过滤
        elif duration == ConstDuration.WEEKLY:
            start_day = str_week_to_start_date(request.GET.get('start_week'))
            end_day = str_week_to_start_date(request.GET.get('end_week'), offset_weeks=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            qs = qs.filter(create_time__gte=ds,
                           create_time__lt=de)
            return qs

        # 月份过滤
        elif duration == ConstDuration.MONTHLY:
            ds = request.query_params.get("start_month")
            de = request.query_params.get("end_month")
            year = int(de.split("-")[0])
            month = int(de.split("-")[1])
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
            start_ds = ds + "-" + "1"
            new_de = str(year) + "-" + str(month) + "-" + "1"
            qs = qs.filter(create_time__gte=start_ds,
                           create_time__lt=new_de)
            return qs

        # 季度过滤
        elif duration == ConstDuration.QUARTERLY:
            start_day = str_quarter_to_start_date(request.GET.get('start_quarter'))
            end_day = str_quarter_to_start_date(request.GET.get('end_quarter'), offset_quarter=1)
            qs = qs.filter(create_time__gte=start_day,
                           create_time__lt=end_day)
            return qs

        # 年份过滤
        elif duration == ConstDuration.YEARLY:
            ds = request.query_params.get('start_year')
            de = request.query_params.get('end_year')
            new_de = str(int(de) + 1)
            start_day = ds + '-01-01'
            start_end = new_de + '-01-01'
            qs = qs.filter(create_time__gte=start_day,
                           create_time__lt=start_end)
            return qs
        else:
            return qs

    @staticmethod
    def date_time_filter(request, qs, duration):
        """
        过滤date,正常时间格式
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """

        if duration == ConstDuration.DAILY:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            qs = qs.filter(date__gte=ds,
                           date__lt=de)
            return qs

        # 周过滤
        elif duration == ConstDuration.WEEKLY:
            start_day = str_week_to_start_date(request.GET.get('start_week'))
            end_day = str_week_to_start_date(request.GET.get('end_week'), offset_weeks=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            qs = qs.filter(date__gte=ds,
                           date__lt=de)
            return qs

        # 月份过滤
        elif duration == ConstDuration.MONTHLY:
            ds = request.query_params.get("start_month")
            de = request.query_params.get("end_month")
            year = int(de.split("-")[0])
            month = int(de.split("-")[1])
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
            start_ds = ds + "-" + "1"
            new_de = str(year) + "-" + str(month) + "-" + "1"
            qs = qs.filter(date__gte=start_ds,
                           date__lt=new_de)
            return qs

        # 季度过滤
        elif duration == ConstDuration.QUARTERLY:
            start_day = str_quarter_to_start_date(request.GET.get('start_quarter'))
            end_day = str_quarter_to_start_date(request.GET.get('end_quarter'), offset_quarter=1)
            qs = qs.filter(date__gte=start_day,
                           date__lt=end_day)
            return qs

        # 年份过滤
        elif duration == ConstDuration.YEARLY:
            ds = request.query_params.get('start_year')
            de = request.query_params.get('end_year')
            new_de = str(int(de) + 1)
            start_day = ds + '-01-01'
            start_end = new_de + '-01-01'
            qs = qs.filter(date__gte=start_day,
                           date__lt=start_end)
            return qs
        else:
            return qs

    @staticmethod
    def chuan_create_time_filter(request, qs, duration, start_date, end_date):
        """
        过滤create_time,正常时间格式
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """

        if duration == ConstDuration.DAILY:
            ds = start_date
            de = end_date
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            qs = qs.filter(create_time__gte=ds,
                           create_time__lt=de)
            return qs
        return qs

    @staticmethod
    def start_date_end_date_create_time_filter(request, qs, duration, ds, de):
        """
        过滤create_time,正常时间格式转时间戳，传入日期参数
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """

        if duration == ConstDuration.DAILY:
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            # 将时间字符串转成时间撮
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(create_time__gte=timestamp_ds,
                           create_time__lt=timestamp_de)
            return qs

    @staticmethod
    def terminal_time_filter(qs, request):
        if request.method == 'POST':
            ds = request.data.get("start_date")
            de = request.data.get("end_date")
        else:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
        if not ds or not de:
            return qs
        end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
        de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
        # 将时间字符串转成时间
        timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
        qs = qs.filter(scan_time__gte=timestamp_ds,
                       scan_time__lt=timestamp_de)
        return qs
    
    @staticmethod
    def finish_time_filter(request, qs, duration):
        """完成时间过滤 car_bill_factory
        过滤start_time,正常时间格式转时间戳
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """
        if duration == ConstDuration.DAILY:
            ds = request.query_params.get("finish_start_date")
            de = request.query_params.get("finish_end_date")
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            # 将时间字符串转成时间撮
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(finish_time__gte=timestamp_ds,
                           finish_time__lt=timestamp_de)
            return qs
        return qs
    
    
    @staticmethod
    def start_time_filter(request, qs, duration):
        """
        过滤start_time,正常时间格式转时间戳
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """

        if duration == ConstDuration.DAILY:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            # 将时间字符串转成时间撮
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(start_time__gte=timestamp_ds,
                           start_time__lt=timestamp_de)
            return qs

        # 周过滤
        elif duration == ConstDuration.WEEKLY:
            start_day = str_week_to_start_date(request.GET.get('start_week'))
            end_day = str_week_to_start_date(request.GET.get('end_week'), offset_weeks=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(start_time__gte=timestamp_ds,
                           start_time__lt=timestamp_de)
            return qs

        # 月份过滤
        elif duration == ConstDuration.MONTHLY:
            ds = request.query_params.get("start_month")
            de = request.query_params.get("end_month")
            year = int(de.split("-")[0])
            month = int(de.split("-")[1])
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
            new_de = str(year) + "-" + str(month)
            # 将时间字符串转成时间撮
            timeArray_ds = time.strptime(ds, "%Y-%m")
            timeArray_de = time.strptime(new_de, "%Y-%m")
            timestamp_ds = time.mktime(timeArray_ds)
            timestamp_de = time.mktime(timeArray_de)
            qs = qs.filter(start_time__gte=timestamp_ds,
                           start_time__lt=timestamp_de)
            return qs

        # 季度过滤
        elif duration == ConstDuration.QUARTERLY:
            start_day = str_quarter_to_start_date(request.GET.get('start_quarter'))
            end_day = str_quarter_to_start_date(request.GET.get('end_quarter'), offset_quarter=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(start_time__gte=timestamp_ds,
                           start_time__lt=timestamp_de)
            return qs

        # 年份过滤
        elif duration == ConstDuration.YEARLY:
            ds = request.query_params.get('start_year')
            de = request.query_params.get('end_year')
            new_de = str(int(de) + 1)
            # 将时间字符串转成时间撮
            timeArray_ds = time.strptime(ds, "%Y")
            timeArray_de = time.strptime(new_de, "%Y")
            timestamp_ds = time.mktime(timeArray_ds)
            timestamp_de = time.mktime(timeArray_de)
            qs = qs.filter(start_time__gte=timestamp_ds,
                           start_time__lt=timestamp_de)
            return qs
        else:
            return qs

    @staticmethod
    def commit_time_filter(request, qs, duration):
        """
        过滤commit_time,正常时间格式转时间戳
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """

        if duration == ConstDuration.DAILY:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            # 将时间字符串转成时间撮
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(commit_time__gte=timestamp_ds,
                           commit_time__lt=timestamp_de)
            return qs

        # 周过滤
        elif duration == ConstDuration.WEEKLY:
            start_day = str_week_to_start_date(request.GET.get('start_week'))
            end_day = str_week_to_start_date(request.GET.get('end_week'), offset_weeks=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(commit_time__gte=timestamp_ds,
                           commit_time__lt=timestamp_de)
            return qs

        # 月份过滤
        elif duration == ConstDuration.MONTHLY:
            ds = request.query_params.get("start_month")
            de = request.query_params.get("end_month")
            year = int(de.split("-")[0])
            month = int(de.split("-")[1])
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
            new_de = str(year) + "-" + str(month)
            # 将时间字符串转成时间撮
            timeArray_ds = time.strptime(ds, "%Y-%m")
            timeArray_de = time.strptime(new_de, "%Y-%m")
            timestamp_ds = time.mktime(timeArray_ds)
            timestamp_de = time.mktime(timeArray_de)
            qs = qs.filter(commit_time__gte=timestamp_ds,
                           commit_time__lt=timestamp_de)
            return qs

        # 季度过滤
        elif duration == ConstDuration.QUARTERLY:
            start_day = str_quarter_to_start_date(request.GET.get('start_quarter'))
            end_day = str_quarter_to_start_date(request.GET.get('end_quarter'), offset_quarter=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(commit_time__gte=timestamp_ds,
                           commit_time__lt=timestamp_de)
            return qs

        # 年份过滤
        elif duration == ConstDuration.YEARLY:
            ds = request.query_params.get('start_year')
            de = request.query_params.get('end_year')
            new_de = str(int(de) + 1)
            # 将时间字符串转成时间撮
            timeArray_ds = time.strptime(ds, "%Y")
            timeArray_de = time.strptime(new_de, "%Y")
            timestamp_ds = time.mktime(timeArray_ds)
            timestamp_de = time.mktime(timeArray_de)
            qs = qs.filter(commit_time__gte=timestamp_ds,
                           commit_time__lt=timestamp_de)
            return qs
        else:
            return qs


    @staticmethod
    def audit_time_filter(request, qs, duration):
        """
        过滤audit_time,正常时间格式转时间戳
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """
    
        if duration == ConstDuration.DAILY:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            # 将时间字符串转成时间撮
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(audit_time__gte=timestamp_ds,
                           audit_time__lt=timestamp_de)
            return qs
    
        # 周过滤
        elif duration == ConstDuration.WEEKLY:
            start_day = str_week_to_start_date(request.GET.get('start_week'))
            end_day = str_week_to_start_date(request.GET.get('end_week'), offset_weeks=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(audit_time__gte=timestamp_ds,
                           audit_time__lt=timestamp_de)
            return qs
    
        # 月份过滤
        elif duration == ConstDuration.MONTHLY:
            ds = request.query_params.get("start_month")
            de = request.query_params.get("end_month")
            year = int(de.split("-")[0])
            month = int(de.split("-")[1])
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
            new_de = str(year) + "-" + str(month)
            # 将时间字符串转成时间撮
            timeArray_ds = time.strptime(ds, "%Y-%m")
            timeArray_de = time.strptime(new_de, "%Y-%m")
            timestamp_ds = time.mktime(timeArray_ds)
            timestamp_de = time.mktime(timeArray_de)
            qs = qs.filter(audit_time__gte=timestamp_ds,
                           audit_time__lt=timestamp_de)
            return qs
    
        # 季度过滤
        elif duration == ConstDuration.QUARTERLY:
            start_day = str_quarter_to_start_date(request.GET.get('start_quarter'))
            end_day = str_quarter_to_start_date(request.GET.get('end_quarter'), offset_quarter=1)
            # 将时间字符串转成时间撮
            ds = datetime.datetime.strftime(start_day, '%Y-%m-%d')
            de = datetime.datetime.strftime(end_day, '%Y-%m-%d')
            timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
            qs = qs.filter(audit_time__gte=timestamp_ds,
                           audit_time__lt=timestamp_de)
            return qs
    
        # 年份过滤
        elif duration == ConstDuration.YEARLY:
            ds = request.query_params.get('start_year')
            de = request.query_params.get('end_year')
            new_de = str(int(de) + 1)
            # 将时间字符串转成时间撮
            timeArray_ds = time.strptime(ds, "%Y")
            timeArray_de = time.strptime(new_de, "%Y")
            timestamp_ds = time.mktime(timeArray_ds)
            timestamp_de = time.mktime(timeArray_de)
            qs = qs.filter(audit_time__gte=timestamp_ds,
                           audit_time__lt=timestamp_de)
            return qs
        else:
            return qs

    @staticmethod
    def get_timestamp_duration(request):
        if request.method == 'POST':
            ds = request.data.get("start_date")
            de = request.data.get("end_date")
        else:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
        if not ds or not de:
            ds = datetime.date.today().strftime('%Y-%m-%d')
            de = datetime.date.today().strftime('%Y-%m-%d')
        end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
        de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
        # 将时间字符串转成时间
        timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
        return timestamp_ds, timestamp_de

    @staticmethod
    def new_create_time_filter_by_field_name(request, qs, duration, field_name='create_time'):
        """
        通过自定义的field_name过滤,正常时间格式
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :param qs: queryset对象
        :return: 过滤时间后的queryset对象
        """

        if duration == ConstDuration.DAILY:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
            end_date = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            de = datetime.datetime.strftime(end_date, '%Y-%m-%d')
            filter_dict = dict()
            filter_dict[field_name + '__gte'] = ds
            filter_dict[field_name + '__lt'] = de
            print(filter_dict)
            qs = qs.filter(**filter_dict)
            return qs

        # 周过滤
        elif duration == ConstDuration.WEEKLY:
            start_day = str_week_to_start_date(request.GET.get('start_week'))
            end_day = str_week_to_start_date(request.GET.get('end_week'), offset_weeks=1)

            filter_dict = dict()
            filter_dict[field_name + '__gte'] = start_day
            filter_dict[field_name + '__lt'] = end_day
            qs = qs.filter(**filter_dict)
            return qs

        # 月份过滤
        elif duration == ConstDuration.MONTHLY:
            ds = request.query_params.get("start_month")
            de = request.query_params.get("end_month")
            year = int(de.split("-")[0])
            month = int(de.split("-")[1])
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
            start_ds = ds + "-" + "1"
            new_de = str(year) + "-" + str(month) + "-" + "1"
            filter_dict = dict()
            filter_dict[field_name + '__gte'] = start_ds
            filter_dict[field_name + '__lt'] = new_de
            qs = qs.filter(**filter_dict)
            return qs

        # 季度过滤
        elif duration == ConstDuration.QUARTERLY:
            start_day = str_quarter_to_start_date(request.GET.get('start_quarter'))
            end_day = str_quarter_to_start_date(request.GET.get('end_quarter'), offset_quarter=1)
            filter_dict = dict()
            filter_dict[field_name + '__gte'] = start_day
            filter_dict[field_name + '__lt'] = end_day
            qs = qs.filter(**filter_dict)
            return qs

        # 年份过滤
        elif duration == ConstDuration.YEARLY:
            ds = request.query_params.get('start_year')
            de = request.query_params.get('end_year')
            new_de = str(int(de) + 1)
            start_day = ds + '-01-01'
            start_end = new_de + '-01-01'
            filter_dict = dict()
            filter_dict[field_name + '__gte'] = start_day
            filter_dict[field_name + '__lt'] = start_end
            qs = qs.filter(**filter_dict)
            return qs
        else:
            return qs
    @staticmethod
    def get_date_duration(request, duration):
        """
        根据参数获取起始时间戳
        :param request: request对象用来获取前端日期参数
        :param duration: 日期类型
        :return: 过滤时间后的queryset对象
        """
        if not duration:
            today = datetime.date.today().strftime("%Y-%m-%d")
            return (
                datetime.datetime.strptime(today, "%Y-%m-%d"),
                datetime.datetime.strptime(today, "%Y-%m-%d") + datetime.timedelta(days=1),
            )

        if duration == ConstDuration.DAILY:
            ds = request.query_params.get("start_date")
            de = request.query_params.get("end_date")
            ds = datetime.datetime.strptime(ds, "%Y-%m-%d")
            de = datetime.datetime.strptime(de, "%Y-%m-%d") + datetime.timedelta(days=1)
            return ds, de

        # 周过滤
        elif duration == ConstDuration.WEEKLY:
            start_day = str_week_to_start_date(request.GET.get("start_week"))
            end_day = str_week_to_start_date(request.GET.get("end_week"), offset_weeks=1)
            return date_to_datetime(start_day), date_to_datetime(end_day)

        # 月份过滤
        elif duration == ConstDuration.MONTHLY:
            start_day = str_month_to_start_date(request.GET.get("start_month"))
            end_day = str_month_to_start_date(request.GET.get("end_month"), offset_months=1)
            return date_to_datetime(start_day), date_to_datetime(end_day)

        # 季度过滤
        elif duration == ConstDuration.QUARTERLY:
            start_day = str_quarter_to_start_date(request.GET.get("start_quarter"))
            end_day = str_quarter_to_start_date(request.GET.get("end_quarter"), offset_quarter=1)
            # 将时间字符串转成日期
            ds = datetime.datetime.strptime(start_day, "%Y-%m-%d")
            de = datetime.datetime.strptime(end_day, "%Y-%m-%d")
            return ds, de

        # 年份过滤
        elif duration == ConstDuration.YEARLY:
            ds = request.query_params.get("start_year")
            de = request.query_params.get("end_year")
            new_de = str(int(de) + 1)
            # 将时间字符串转日期
            ds = datetime.datetime.strptime(ds + "-01-01", "%Y-%m-%d")
            de = datetime.datetime.strptime(new_de + "-01-01", "%Y-%m-%d")
            return ds, de
        else:
            return 0, 0

class FilterTools:
    @staticmethod
    def coding_filter(request, qs):
        area_coding = request.GET.get('area_coding', '')
        street_coding = request.GET.get('street_coding', '')
        comm_coding = request.GET.get('comm_coding', '')
        org_sub_type_id = request.GET.get('org_sub_type_id', '')
        type_id = request.GET.get('type_id', '')
        quality = request.GET.get('quality', '')
        car_num = request.GET.get('car_num', '')
        if area_coding:
            qs = qs.filter(area_coding=area_coding)
        if street_coding:
            qs = qs.filter(street_coding=street_coding)
        if comm_coding:
            qs = qs.filter(comm_coding=comm_coding)
        if org_sub_type_id:
            qs = qs.filter(org_sub_type_id=org_sub_type_id)
        if type_id:
            qs = qs.filter(type_id=type_id)
        if quality:
            qs = qs.filter(quality=quality)
        if car_num:
            qs = qs.filter(car_num=car_num)
        return qs

    @staticmethod
    def fuzzy_filter(request, qs):
        official_org_name = request.GET.get('official_org_name', '')
        org_name = request.GET.get('org_name', '')
        if official_org_name:
            qs = qs.filter(official_org_name__contains=official_org_name)
        if org_name:
            qs = qs.filter(name__contains=org_name)
        return qs

    @staticmethod
    def time_filter(request, qs):
        scan_time = request.GET.get('scan_time', '')
        commit_time = request.GET.get('commit_time', '')
        ds = datetime.datetime.strftime(scan_time, '%Y-%m-%d %H:%M:%S')
        de = datetime.datetime.strftime(commit_time, '%Y-%m-%d %H:%M:%S')
        # 将时间字符串转成时间撮
        timestamp_ds, timestamp_de = time_to_timestamp(ds, de)
        qs = qs.filter(create_time__gte=timestamp_ds,
                       create_time__lt=timestamp_de)
        return qs


def date_to_datetime(dt):
    return datetime.datetime(dt.year, dt.month, dt.day)


def get_time_to_timestamp(ds):
    """
    ds: 字符串时间格式
    return: 时间戳
    """
    timeArray_ds = time.strptime(ds, "%Y-%m-%d")
    timestamp_ds = time.mktime(timeArray_ds)
    return timestamp_ds


def time_to_timestamp(ds, de):
    """
    时间格式转时间戳
    ds: 起始时间，str
    de: 截至时间，str
    """
    timeArray_ds = time.strptime(ds, "%Y-%m-%d")
    timeArray_de = time.strptime(de, "%Y-%m-%d")
    timestamp_ds = time.mktime(timeArray_ds)
    timestamp_de = time.mktime(timeArray_de)
    return timestamp_ds, timestamp_de


def datetime_to_timestamp(ds, de):
    """
    时间格式转时间戳
    ds: 起始时间，str
    de: 截至时间，str
    """
    timeArray_ds = time.strptime(ds, "%Y-%m-%d %H:%M:%S")
    timeArray_de = time.strptime(de, "%Y-%m-%d %H:%M:%S")
    timestamp_ds = time.mktime(timeArray_ds)
    timestamp_de = time.mktime(timeArray_de)
    return timestamp_ds, timestamp_de


def timestamp_to_time(ds, de):
    """
    时间戳格式转时间
    ds: 起始时间戳
    de: 截至时间戳
    """
    time_ds = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(ds))
    time_de = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(de))
    return time_ds, time_de


def timestamp_to_datetime(ds):
    """
    时间戳格式转时间格式
    """
    time_ds = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(ds))
    return time_ds

def bdToGaoDe2(lon, lat):
    """
    百度坐标转高德坐标
    :param lon:
    :param lat:
    :return:
    """
    PI = 3.14159265358979324 * 3000.0 / 180.0
    x = lon - 0.0065
    y = lat - 0.006
    z = math.sqrt(x * x + y * y) - 0.00002 * math.sin(y * PI)
    theta = math.atan2(y, x) - 0.000003 * math.cos(x * PI)
    lon = z * math.cos(theta)
    lat = z * math.sin(theta)
    return lon, lat


# -------------分页---------
class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 500


# -------------自定义分页--------
ONE_PAGE_OF_DATA = 10


def set_pagination(request, qs):
    try:
        curPage = int(request.GET.get('Page', 1))
        allPage = int(request.GET.get('allPage', 1))
        pageType = str(request.GET.get('pageType', 1))
    except ValueError:
        curPage = 1
        allPage = 1
        pageType = ''

    if pageType == 'pageDown':
        curPage += 1
    elif pageType == 'pageUp':
        curPage -= 1

    startPage = (curPage - 1) * ONE_PAGE_OF_DATA
    endPage = startPage + ONE_PAGE_OF_DATA
    pages = qs.filter(is_deleted=0)[startPage:endPage]
    print(pages.values())
    all_count = qs.count()
    if curPage == 1 and allPage == 1:
        all_count = qs.count()
        print(all_count)
        all_page = all_count / ONE_PAGE_OF_DATA
        remain = all_count % ONE_PAGE_OF_DATA
        if remain > 0:
            allPage += 1
    # return {'user_list': pages.values(), 'all_count': all_count.values(), 'curPage': curPage}
    return {'user_list': pages.values(), 'all_count': all_count}


class ApiLimitOffsetPagination(LimitOffsetPagination):
    """偏移分页装饰器"""
    default_limit = 10
    limit_query_param = 'count'
    offset_query_param = 'nextid'
    max_limit = 10000

    def paginate_queryset(self, queryset, request, view=None):
        # print(queryset.query.__str__())
        self.count = self.get_count(queryset)
        self.limit = self.get_limit(request)
        if self.limit is None:
            return None

        self.offset = self.get_offset(request)
        self.request = request
        if self.count > self.limit and self.template is not None:
            self.display_page_controls = True
        if self.count == 0:
            return []
        # print(queryset.filter(id__gt=self.offset)[:self.limit].query.__str__())
        return list(queryset.filter(id__gt=self.offset)[:self.limit])

    def get_paginated_response(self, data):
        nextid = 0
        if data:
            nextid = data[-1]['id']
        return Response(OrderedDict([
            ('count', self.count),
            ('nextid', nextid if len(data) > 0 and self.count >= self.limit else None),
            ('results', data)
        ]))


x_pi = 3.14159265358979324 * 3000.0 / 180.0
pi = 3.1415926535897932384626  # π
a = 6378245.0  # 长半轴
ee = 0.00669342162296594323  # 扁率


def gcj02towgs84(lng, lat):
    """
    GCJ02(火星坐标系)转GPS84
    :param lng:火星坐标系的经度
    :param lat:火星坐标系纬度
    :return:
    """
    dlat = transformlat(lng - 105.0, lat - 35.0)
    dlng = transformlng(lng - 105.0, lat - 35.0)
    radlat = lat / 180.0 * pi
    magic = math.sin(radlat)
    magic = 1 - ee * magic * magic
    sqrtmagic = math.sqrt(magic)
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * pi)
    dlng = (dlng * 180.0) / (a / sqrtmagic * math.cos(radlat) * pi)
    mglat = lat + dlat
    mglng = lng + dlng
    return [lng * 2 - mglng, lat * 2 - mglat]


def transformlat(lng, lat):
    ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + \
          0.1 * lng * lat + 0.2 * math.sqrt(math.fabs(lng))
    ret += (20.0 * math.sin(6.0 * lng * pi) + 20.0 *
            math.sin(2.0 * lng * pi)) * 2.0 / 3.0
    ret += (20.0 * math.sin(lat * pi) + 40.0 *
            math.sin(lat / 3.0 * pi)) * 2.0 / 3.0
    ret += (160.0 * math.sin(lat / 12.0 * pi) + 320 *
            math.sin(lat * pi / 30.0)) * 2.0 / 3.0
    return ret


def transformlng(lng, lat):
    ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + \
          0.1 * lng * lat + 0.1 * math.sqrt(math.fabs(lng))
    ret += (20.0 * math.sin(6.0 * lng * pi) + 20.0 *
            math.sin(2.0 * lng * pi)) * 2.0 / 3.0
    ret += (20.0 * math.sin(lng * pi) + 40.0 *
            math.sin(lng / 3.0 * pi)) * 2.0 / 3.0
    ret += (150.0 * math.sin(lng / 12.0 * pi) + 300.0 *
            math.sin(lng / 30.0 * pi)) * 2.0 / 3.0
    return ret


#  高德Gcj转百度bd09
def gcj02_to_bd09(gcj02_lng, gcj02_lat):
    x_pi = 3.14159265358979324 * 3000.0 / 180.0
    x = gcj02_lng
    y = gcj02_lat
    z = math.sqrt(x * x + y * y) + 0.00002 * math.sin(y * x_pi)
    theta = math.atan2(y, x) + 0.000003 * math.cos(x * x_pi)
    bd09_Lng = z * math.cos(theta) + 0.0065
    bd09_Lat = z * math.sin(theta) + 0.006
    return bd09_Lng, bd09_Lat


def bdToGaoDe(lon, lat):
    """
    百度坐标转高德坐标
    :param lon:
    :param lat:
    :return:
    """
    PI = 3.14159265358979324 * 3000.0 / 180.0
    x = lon - 0.0065
    y = lat - 0.006
    z = math.sqrt(x * x + y * y) - 0.00002 * math.sin(y * PI)
    theta = math.atan2(y, x) - 0.000003 * math.cos(x * PI)
    lon = z * math.cos(theta)
    lat = z * math.sin(theta)
    return lon, lat


class SyncThread(threading.Thread):
    def __init__(self, func, args):
        threading.Thread.__init__(self)
        self.func = func
        self.args = args

    def run(self):
        self.res = self.func(*self.args)

    def getResult(self):
        return self.res


class MyCustomPagination(PageNumberPagination):
    page_size = 10  # 每页显示多少个

    page_size_query_param = "page_size"  # 改变默认每页显示的个数

    max_page_size = 100  # 最大页数不超过100

    page_query_param = "page"  # 获取页码数的

    def get_paginated_response(self, data, ):
        """输出格式"""
        return Response(OrderedDict([
            ('count', self.page.paginator.count),  # 整个数据的个数
            ('success', True),  # 验证消息
            ('next', self.get_next_link()),  # 下一页url
            ('previous', self.get_previous_link()),  # 上一页url
            ('results', data)  # 当前页的数据
        ]))


def create_time_uuid():
    order_id = time.strftime("%Y%m%d%H%M%S", time.localtime(time.time())) + uuid.uuid4().hex
    return order_id


# <editor-fold desc="获取时间段内统计指标集合">
def _get_duration_daily(request, start_key='start_date', end_key='end_date'):
    """
    获取日统计参数中duration集合
    :param request:
    :return:
    """
    today = datetime.datetime.now().date()
    start_day = str_to_date(request.GET.get(start_key), default=today)
    end_day = str_to_date(request.GET.get(end_key), default=today, offset_days=1)
    
    start_day, end_day = (start_day, end_day) if start_day < end_day else (end_day, start_day)
    
    durations = []
    while start_day < end_day:
        key = start_day.strftime('%Y-%m-%d')
        durations.append(key)
        start_day = start_day + datetime.timedelta(days=1)
    return durations


def _get_duration_weekly(request, start_key='start_week', end_key='end_week'):
    """
    获取周统计参数中duration集合
    :param request:
    :return:
    """
    start_day = str_week_to_start_date(request.GET.get(start_key))
    end_day = str_week_to_start_date(request.GET.get(end_key), offset_weeks=1)
    
    start_day, end_day = (start_day, end_day) if start_day < end_day else (end_day, start_day)
    
    durations = []
    while start_day < end_day:
        year, week, _ = start_day.isocalendar()
        key = '{}-{:02d}'.format(year, week)
        durations.append(key)
        start_day = start_day + datetime.timedelta(days=7)
    return durations


def _get_duration_monthly(request, start_key='start_month', end_key='end_month'):
    """
    获取月统计参数中duration集合
    :param request:
    :return:
    """
    start_day = str_month_to_start_date(request.GET.get(start_key))
    end_day = str_month_to_start_date(request.GET.get(end_key), offset_months=1)
    
    start_day, end_day = (start_day, end_day) if start_day < end_day else (end_day, start_day)
    
    durations = []
    while start_day < end_day:
        key = start_day.strftime('%Y-%m')
        durations.append(key)
        start_day = (start_day + datetime.timedelta(days=31)).replace(day=1)
    return durations


def _get_duration_quarterly(request, start_key='start_quarter', end_key='end_quarter'):
    """
    获取季统计参数中duration集合
    :param request:
    :return:
    """
    start_day = str_quarter_to_start_date(request.GET.get(start_key))
    end_day = str_quarter_to_start_date(request.GET.get(end_key), offset_quarter=1)
    
    start_day, end_day = (start_day, end_day) if start_day < end_day else (end_day, start_day)
    
    durations = []
    while start_day < end_day:
        year, quarter = start_day.year, int((start_day.month - 1) / 3) + 1
        key = '{}-{:02d}'.format(year, quarter)
        durations.append(key)
        year, quarter = (year, quarter + 1) if quarter < 4 else (year + 1, 1)
        start_day = datetime.date(year, (quarter - 1) * 3 + 1, 1)
    return durations


def _get_duration_yearly(request, start_key='start_year', end_key='end_year'):
    """
    获取年统计参数中duration集合
    :param request:
    :return:
    """
    start_day = str_year_to_start_date(request.GET.get(start_key))
    end_day = str_year_to_start_date(request.GET.get(end_key), offset_year=1)
    
    start_day, end_day = (start_day, end_day) if start_day < end_day else (end_day, start_day)
    
    durations = []
    while start_day < end_day:
        key = start_day.strftime('%Y')
        durations.append(key)
        start_day = datetime.date(start_day.year + 1, 1, 1)
    return durations


def add_date_range(stats_data, request, duration, start_key=None, end_key=None, zero_data=None):
    """补全日期数据"""
    duration_list_dict = {
        ConstDuration.DAILY: _get_duration_daily,
        ConstDuration.WEEKLY: _get_duration_weekly,
        ConstDuration.MONTHLY: _get_duration_monthly,
        ConstDuration.QUARTERLY: _get_duration_quarterly,
        ConstDuration.YEARLY: _get_duration_yearly,
    }
    if zero_data is None:
        zero_data = []
    func = duration_list_dict.get(duration)
    if func:
        if start_key and end_key:
            duration_list = func(request, start_key=start_key, end_key=end_key)
        else:
            duration_list = func(request)
        if len(stats_data.keys()) != len(duration_list):
            for _duration in duration_list:
                if _duration not in stats_data:
                    zero_data = zero_data.copy()
                    zero_data['day'] = _duration
                    stats_data[_duration] = zero_data
    return stats_data
