#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import
from django.db import models
from django.utils import timezone

from django_mysql.models import Model as ExtendModel
from CollectManageApp.services import make_time_stamp


class CityRegion(models.Model):
    region_id = models.CharField(unique=True, max_length=45)
    name = models.CharField(max_length=45, blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    coding = models.Char<PERSON>ield(max_length=45, blank=True, null=True)
    clean_type = models.Char<PERSON>ield(max_length=45, blank=True, null=True)
    clean_code = models.CharField(max_length=45, blank=True, null=True)
    address = models.CharField(max_length=250, blank=True, null=True)
    creator = models.Char<PERSON><PERSON>(max_length=45, blank=True, null=True)
    parent_id = models.CharField(max_length=45, blank=True, null=True)
    grade = models.IntegerField(blank=True, null=True)
    remark = models.Char<PERSON>ield(max_length=250, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'city_region'
        app_label = 'ljfl_db'

class Car(models.Model):
    car_id = models.CharField(max_length=45)
    clean_code = models.CharField(max_length=45, blank=True)
    clean_no = models.CharField(max_length=45, blank=True)
    device_id = models.CharField(max_length=45, blank=True, null=True)
    name = models.CharField(max_length=45, blank=True, null=True)
    meter_no = models.CharField(max_length=45, blank=True, null=True)
    car_color = models.CharField(max_length=45, blank=True, null=True)
    facility_num = models.CharField(max_length=45, blank=True, null=True)
    car_num = models.CharField(max_length=45, blank=True, null=True)
    brand = models.CharField(max_length=45, blank=True, null=True)
    power_type = models.CharField(max_length=45, blank=True, null=True)
    oil_type = models.CharField(max_length=45, blank=True, null=True)
    car_emission_grade = models.CharField(max_length=45, blank=True, null=True)
    carry_person_num = models.IntegerField(blank=True, null=True)
    total_weight = models.CharField(max_length=45, blank=True, null=True)
    car_weight = models.CharField(max_length=45, blank=True, null=True)
    carry_weight = models.CharField(max_length=45, blank=True, null=True)
    car_size = models.CharField(max_length=45, blank=True, null=True)
    facility_status = models.CharField(max_length=45, blank=True, null=True)
    reg_date = models.CharField(max_length=45, blank=True, null=True)
    receive_date = models.CharField(max_length=45, blank=True, null=True)
    cost = models.CharField(max_length=45, blank=True, null=True)
    run_year = models.CharField(max_length=45, blank=True, null=True)
    is_runing = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=1000, blank=True, null=True)
    collector_uint_id = models.CharField(max_length=45, blank=True, null=True)
    type_id = models.CharField(max_length=45, blank=True, null=True)
    car_type_id = models.CharField(max_length=45, blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    address = models.CharField(max_length=250, blank=True, null=True)
    contacts = models.CharField(max_length=45, blank=True, null=True)
    phone = models.CharField(max_length=45, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    is_online = models.IntegerField(blank=True, null=True, default=0)
    online_time = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    car_status = models.IntegerField(blank=True, null=True, default=0)
    update_date = models.DateTimeField(blank=True, null=True)
    manage_unit = models.CharField(max_length=255, blank=True, null=True, default='')
    operate_unit = models.CharField(max_length=255, blank=True, null=True, default='')
    update_gps_time = models.IntegerField(blank=True, null=True)
    is_spray = models.IntegerField(blank=True, null=True, default=0)
    is_weight = models.IntegerField(blank=True, null=True, default=1)
    is_gps = models.IntegerField(blank=True, null=True, default=1)
    is_quality = models.IntegerField(blank=True, null=True, default=0)
    transport_type = models.IntegerField(blank=True, null=True, default=0)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True, default='')
    company = models.CharField(max_length=45, blank=True, null=True, default='')
    company_type = models.CharField(max_length=45, blank=True, null=True)
    receiving_unit = models.CharField(max_length=500, blank=True, null=True, default='')
    uhf_device = models.IntegerField(blank=True, null=True)
    city_system = models.IntegerField(blank=True, null=True, default=0)
    area_system = models.IntegerField(blank=True, null=True, default=0)
    source_transfer_station = models.CharField(max_length=100, blank=True, null=True, default='')
    standing_book = models.IntegerField(blank=True, null=True, default=0)
    is_self = models.PositiveIntegerField(blank=True, null=True)
    factory_location_id = models.CharField(max_length=500, blank=True, null=True, default='')
    transfer_station_id = models.CharField(max_length=45, blank=True, null=True, default='')
    remark2 = models.CharField(max_length=255, blank=True, null=True, default='')
    district = models.CharField(max_length=45, blank=True, null=True, default='')
    source_area = models.CharField(max_length=45, blank=True, null=True, default='')
    source_street = models.CharField(max_length=1000, blank=True, null=True, default='')
    source_cleaning_point = models.CharField(max_length=500, blank=True, null=True, default='')
    rubbish_name = models.CharField(max_length=255, blank=True, null=True, default='')
    data_upload_device = models.IntegerField(blank=True, null=True, default=0)
    rfid_label = models.IntegerField(blank=True, null=True, default=0)
    access_date = models.DateField(blank=True, null=True)
    area_report = models.IntegerField(blank=True, null=True)
    area_mark = models.CharField(max_length=45, blank=True, null=True, default='')
    upload_type = models.CharField(max_length=100, blank=True, null=True)
    dock_company = models.CharField(max_length=255, blank=True, null=True)
    is_transfer_to_gufei = models.IntegerField(default=0)
    is_declare = models.IntegerField(default=0)
    register_num = models.CharField(max_length=5, null=True, blank=True, default='', verbose_name=('类型编码'))
    dest_cleaning_point_id = models.TextField(blank=True, null=True, verbose_name='垃圾去向单位名称(垃圾楼id)')

    class Meta:
        managed = False
        db_table = 'car'
        app_label = 'ljfl_db'


class CarRecord(ExtendModel):
    car_record_id = models.CharField(max_length=45)
    car_id = models.CharField(max_length=45)
    car_num = models.CharField(max_length=45)
    type_id = models.CharField(max_length=45, blank=True, null=True)
    weight = models.FloatField(blank=True, null=True)
    rfid_num = models.CharField(max_length=45, blank=True, null=True)
    cover = models.CharField(max_length=2000, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    org_name = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    quality = models.CharField(max_length=45, blank=True, null=True)
    # size = models.CharField(max_length=45, blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    address = models.CharField(max_length=200, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    is_handled = models.IntegerField(blank=True, null=True, default=0)
    handle_time = models.IntegerField(blank=True, null=True, default=0)
    creator = models.CharField(max_length=45, blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    stream_number = models.CharField(max_length=45)

    class Meta:
        managed = False
        db_table = 'car_record'
        app_label = 'ljfl_db'


class CarRecordOrg(models.Model):
    car_record_id = models.CharField(max_length=45)
    car_flow_id = models.CharField(max_length=45, blank=True, null=True)
    terminal_record_id = models.CharField(max_length=45, blank=True, null=True)
    car_id = models.CharField(max_length=45)
    car_num = models.CharField(max_length=45)
    type_id = models.CharField(max_length=45, blank=True, null=True)
    weight_type = models.CharField(default='CAR', max_length=45, blank=True)
    weight = models.FloatField(blank=True, null=True)
    modify_weight = models.FloatField(blank=True, null=True, default=0.00)
    capacity = models.CharField(max_length=45, blank=True, default='')
    capacity_weight = models.FloatField(blank=True, null=True, default=0.00)
    rfid_num = models.CharField(max_length=45, blank=True, null=True)
    cover = models.CharField(max_length=1000, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    org_name = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    quality = models.CharField(max_length=45, blank=True, null=True)
    size = models.CharField(max_length=45, blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    address = models.CharField(max_length=250, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    is_confirm = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    confirm_time = models.IntegerField(blank=True, null=True)
    expire_time = models.IntegerField(blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    stream_number = models.CharField(max_length=45, blank=True, null=True)
    from_car = models.IntegerField(blank=True, null=True)
    from_driver = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'car_record_org'
        app_label = 'ljfl_db'


class FactoryLocation(models.Model):
    clean_code = models.CharField(max_length=45, blank=True)
    clean_no = models.CharField(max_length=45, blank=True)
    area = models.CharField(max_length=100, blank=True, null=True)
    quancheng = models.IntegerField(blank=True, null=True)
    name = models.CharField(max_length=100, blank=True, null=True)
    address = models.CharField(max_length=100, blank=True, null=True)
    mark = models.CharField(max_length=100, blank=True, null=True)
    type = models.CharField(max_length=100, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    factory = models.CharField(max_length=100, blank=True, null=True)
    process_type = models.CharField(max_length=200, blank=True, null=True)
    model_name = models.CharField(max_length=64, blank=True, null=True)
    operation_condition = models.CharField(max_length=12, blank=True, null=True)
    print_type = models.CharField(max_length=200, blank=True, null=True)
    manage_unit = models.CharField(max_length=255, blank=True, null=True)
    operate_unit = models.CharField(max_length=255, blank=True, null=True)
    contacts = models.CharField(max_length=45, blank=True, null=True)
    phone = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    factory_location_id = models.CharField(max_length=45)
    factory_type = models.CharField(max_length=45, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    is_camera = models.IntegerField(blank=True, null=True)
    pic = models.CharField(max_length=1000, blank=True, null=True)
    enter_monitor = models.IntegerField(blank=True, null=True)
    weighbridge_monitor = models.IntegerField(blank=True, null=True)
    discharge_monitor = models.IntegerField(blank=True, null=True)
    delivery_monitor = models.IntegerField(blank=True, null=True)
    is_weight = models.IntegerField(blank=True, null=True)
    city_system = models.IntegerField(blank=True, null=True)
    door_camera_num = models.IntegerField(blank=True, null=True)
    plate_camera_num = models.IntegerField(blank=True, null=True)
    weighbridge_camera_num = models.IntegerField(blank=True, null=True)
    discharging_camera_num = models.IntegerField(blank=True, null=True)
    trans_camera_num = models.IntegerField(blank=True, null=True)
    output_type = models.CharField(max_length=200, blank=True, null=True)
    raw_material_type = models.CharField(max_length=200, blank=True, null=True)
    alias_name = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'factory_location'
        app_label = 'ljfl_db'


class FactoryType(models.Model):
    factory_id = models.CharField(max_length=45)
    name = models.CharField(max_length=45, blank=True, null=True)
    clean_code = models.CharField(max_length=45, blank=True)
    description = models.CharField(max_length=500, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'factory_type'
        app_label = 'ljfl_db'


class OrgDetail(models.Model):
    org_id = models.CharField(max_length=45)
    licence = models.CharField(max_length=255)
    picture = models.CharField(max_length=250, blank=True, null=True)
    scope = models.CharField(max_length=250, blank=True, null=True)
    floor_area = models.CharField(max_length=64, blank=True, null=True)
    loating = models.CharField(max_length=64, blank=True, null=True)
    mam_organ = models.CharField(max_length=250, blank=True, null=True)
    mam_person = models.CharField(max_length=250, blank=True, null=True)
    mam_person_phone = models.CharField(max_length=250, blank=True, null=True)
    tp_name = models.CharField(max_length=250, blank=True, null=True)
    tp_phone = models.CharField(max_length=250, blank=True, null=True)
    third_name = models.CharField(max_length=250, blank=True, null=True)
    third_phone = models.CharField(max_length=250, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    is_deleted = models.IntegerField(default=0)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    heavy_rubbish_point = models.CharField(max_length=255, blank=True, null=True)
    fix_rubbish_point = models.CharField(max_length=255, blank=True, null=True)
    have_dining_room = models.IntegerField(blank=True, null=True)
    throw_point = models.IntegerField(blank=True, null=True)
    kitchen_rubbish_count = models.IntegerField(blank=True, null=True)
    ought_buckets = models.IntegerField(blank=True, null=True)
    ought_small_cars = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'org_detail'
        app_label = 'ljfl_db'


class OrgType(models.Model):
    org_type_id = models.CharField(max_length=45)
    parent_id = models.CharField(max_length=45, blank=True, null=True)
    name = models.CharField(max_length=45, blank=True, null=True)
    alias_name = models.CharField(max_length=45, blank=True, null=True)
    clean_code = models.CharField(max_length=45, blank=True, null=True)
    description = models.CharField(max_length=500, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    sort = models.IntegerField(default=99)
    is_deleted = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'org_type'
        app_label = 'ljfl_db'


class Organization(models.Model):
    org_id = models.CharField(max_length=45)
    declare_type = models.CharField(max_length=45, default='Normal')
    source_type = models.CharField(max_length=45, default='MEAPP')
    clean_code = models.CharField(max_length=45, blank=True)
    clean_no = models.CharField(max_length=45, blank=True)
    org_type_id = models.CharField(max_length=45)
    org_sub_type_id = models.CharField(max_length=45)
    name = models.CharField(max_length=100, blank=True, null=True)
    cover = models.CharField(max_length=1000, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    farmily_num = models.IntegerField(blank=True, null=True)
    resident_num = models.IntegerField(blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    address = models.CharField(max_length=250, blank=True, null=True)
    contacts = models.CharField(max_length=256, blank=True, null=True)
    phone = models.CharField(max_length=256, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    update_date = models.DateTimeField(blank=True, null=True)
    longitude_b = models.DecimalField(max_digits=20, decimal_places=8, blank=True, null=True)
    latitude_b = models.DecimalField(max_digits=20, decimal_places=8, blank=True, null=True)
    longitude_g = models.DecimalField(max_digits=20, decimal_places=8, blank=True, null=True)
    latitude_g = models.DecimalField(max_digits=20, decimal_places=8, blank=True, null=True)
    liabler = models.CharField(max_length=255)
    is_declare = models.IntegerField()
    is_group = models.IntegerField(default=0)
    is_group_second = models.IntegerField(default=0)
    is_group_other = models.IntegerField(default=0)
    is_group_second_other = models.IntegerField(default=0)
    sort = models.IntegerField(blank=True, null=True)
    scale = models.CharField(max_length=45, blank=True, null=True)
    examine_org = models.IntegerField(blank=True, null=True)
    credit_code = models.CharField(max_length=45, blank=True, null=True)
    permission_code = models.CharField(max_length=255, blank=True, null=True)
    is_reduce_device = models.IntegerField(default=0, blank=True, null=True)
    reduce_water_device = models.CharField(max_length=56, default='', blank=True)
    reduce_oil_device = models.CharField(max_length=56, default='', blank=True)
    is_local_device = models.IntegerField(default=0, blank=True, null=True)
    local_factory = models.CharField(max_length=255, default='', blank=True)
    local_capacity = models.CharField(max_length=255, default='', blank=True)
    local_process = models.CharField(max_length=255, default='', blank=True)
    local_out_solid_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_solid_direction = models.CharField(max_length=255, default='', blank=True)
    local_out_water_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_water_direction = models.CharField(max_length=255, default='', blank=True)
    local_out_dregs_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_dregs_direction = models.CharField(max_length=255, default='', blank=True)
    have_contract = models.IntegerField(blank=True, null=True, default=0)
    non_resident_mark = models.IntegerField(blank=True, null=True)
    official_org_name = models.CharField(max_length=45, blank=True, null=True)
    official_address = models.CharField(max_length=255, blank=True)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True)
    licence_cover = models.CharField(max_length=255, blank=True, null=True)
    mam_type = models.CharField(max_length=255)
    mam_subtype = models.CharField(max_length=255)
    restaurant_predict_weight = models.IntegerField(default=0)
    restaurant_trash_120 = models.IntegerField(default=0)
    restaurant_trash_240 = models.IntegerField(default=0)
    restaurant_trash = models.IntegerField(default=0)
    restaurant_trash_rfid = models.IntegerField(default=0)
    other_trash_120 = models.IntegerField(default=0)
    other_trash_240 = models.IntegerField(default=0)
    other_trash = models.IntegerField(default=0)
    other_trash_rfid = models.IntegerField(default=0)
    other_transport_company_id = models.CharField(max_length=45, blank=True, null=True)
    other_predict_weight = models.IntegerField(default=0)
    other_have_contract = models.IntegerField(blank=True, null=True, default=0,
                                              verbose_name='其他垃圾是否有合同 0-无，1-有，2-合同待确认')
    logout_status = models.IntegerField(blank=True, null=True, default=0)
    logout_time = models.DateTimeField(blank=True, null=True)
    rubbishes = models.CharField(max_length=64, null=True, blank=True, verbose_name="垃圾类型")
    three_guarantees_agreement = models.CharField(max_length=1000, blank=True, verbose_name='门前三包责任书')
    three_guarantees_board = models.CharField(max_length=1000, blank=True, verbose_name='门前三包责任图')

    class Meta:
        managed = False
        db_table = 'organization'
        app_label = 'ljfl_db'


class TransportCompany(models.Model):
    clean_code = models.CharField(max_length=45, blank=True)
    clean_no = models.CharField(max_length=45, blank=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    district = models.CharField(max_length=100, blank=True, null=True)
    source_area = models.CharField(max_length=100, blank=True, null=True)
    company = models.CharField(max_length=45, blank=True, null=True)
    company_type = models.CharField(max_length=45, blank=True, null=True)
    receiving_unit = models.CharField(max_length=500, blank=True, null=True)
    source_street = models.CharField(max_length=2000, blank=True, null=True)
    source_cleaning_point = models.CharField(max_length=2000, blank=True, null=True)
    is_declare = models.IntegerField(default=1)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    creator = models.CharField(max_length=45, blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    cover = models.CharField(max_length=1000, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    manage_unit = models.CharField(max_length=100, blank=True, null=True)
    admin = models.CharField(blank=True, null=True, max_length=32, verbose_name=('负责人/法人姓名'))
    id_card = models.CharField(blank=True, null=True, max_length=32, verbose_name=('负责人身份证'))
    phone = models.CharField(blank=True, null=True, max_length=32, verbose_name=('负责人联系电话'))
    credit_code = models.CharField(blank=True, null=True, max_length=32, verbose_name=('社会统一信用代码'))
    c_area_coding = models.CharField(max_length=45, blank=True, null=True, verbose_name='区域ID')
    c_street_coding = models.CharField(max_length=45, blank=True, null=True, verbose_name='街道coding')
    c_street_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='街道名称')
    c_comm_coding = models.CharField(max_length=45, blank=True, null=True, verbose_name='街道coding')
    c_comm_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='街道名称')
    rubbish_type = models.CharField(max_length=255, null=True, blank=True, default='', verbose_name=('垃圾类型id'))
    rubbish_type_name = models.CharField(max_length=255, null=True, blank=True, default='', verbose_name=('垃圾类型名称'))
    viald_time = models.CharField(max_length=45, blank=True, null=True, default='', verbose_name=(u'证书有效期'))
    approval_author = models.CharField(blank=True, null=True, max_length=50, default='', verbose_name=('行政许可审批机关'))
    license_number = models.CharField(blank=True, null=True, max_length=25, default='', verbose_name=('行政许可证号'))
    office_address = models.CharField(blank=True, null=True, max_length=120, default='', verbose_name=('办公地址'))
    email = models.CharField(blank=True, null=True, max_length=120, default='', verbose_name=('邮箱'))
    company_picture = models.TextField(blank=True, null=True, default='', verbose_name=('营业执照'))
    quali_file = models.TextField(blank=True, null=True, default='', verbose_name=('资质附件照片'))
    sub_mchid = models.CharField(blank=True, null=True, default='', verbose_name=('子商户号'))

    # 企业类型（单位性质）
    def get_company_type_str(self):
        COMPANY_TYPE_DICT = {'401': '事业单位', '402': '国有企业', '403': '集体企业', '404': '民营企业'}
        return COMPANY_TYPE_DICT[self.company_type]

    class Meta:
        managed = False
        db_table = 'transport_company'
        app_label = 'ljfl_db'


class TransportCompanyArea(models.Model):
    transport_company_area_id = models.CharField(max_length=45, blank=True, null=True, default='')
    area_coding = models.CharField(max_length=45, blank=True, null=False, default='')
    street_coding = models.TextField(blank=True, null=True)
    street_name = models.TextField(blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    status = models.IntegerField(blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    transport_company_id = models.CharField(max_length=45, blank=True, null=False, default='')
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    clean_code = models.CharField(max_length=32, blank=True, null=True, default='', verbose_name='公司代码')
    clean_no = models.CharField(max_length=12, blank=True, null=True, default='', verbose_name='公司代码简写')
    rubbishes = models.CharField(default='RESTAURANTS', max_length=64, null=True, blank=True, verbose_name="垃圾类型")


    class Meta:
        managed = False
        db_table = 'transport_company_area'
        app_label = 'ljfl_db'



# 扫码表
class TerminalRecord(models.Model):
    # 门店名称
    org_name = models.CharField(max_length=45, default='', blank=True, null=True)
    org_id = models.CharField(max_length=45, default='', blank=True, null=True)
    # 记录id
    terminal_record_id = models.CharField(max_length=45, default='', blank=True, null=True)
    # 单位名称
    # 单位名称
    official_org_name = models.CharField(max_length=45, default='', blank=True, null=True)
    org_type_id = models.CharField(max_length=45, default='', blank=True, null=True)
    org_sub_type_id = models.CharField(max_length=45, default='', blank=True, null=True)
    # 车牌号
    car_num = models.CharField(max_length=45, default='', blank=True, null=True)
    # 扫码时间
    scan_time = models.IntegerField(default=0, blank=True, null=True)
    # 提交时间
    commit_time = models.IntegerField(default=0, blank=True, null=True)
    # 提交状态 0 只扫码暂时没有提交  1已经提交
    commit_status = models.IntegerField(null=True, blank=True, default=0)
    # 车辆收运记录id
    car_record_list = models.TextField(default='', blank=True, null=True)
    quality = models.CharField(max_length=200, blank=True, null=True, default='')
    # 留言
    message = models.CharField(max_length=200, blank=True, null=True, default='')
    # 发布人
    publisher = models.CharField(max_length=45, blank=True, null=True, default='')
    # 垃圾类型
    type_id = models.CharField(max_length=45, blank=True, null=True, default='')
    # 照片
    cover = models.CharField(max_length=1000, blank=True, null=True, default='')
    # 收集方式 称重or计桶 'weight' 'count'
    calcu_type = models.CharField(max_length=45, blank=True, null=True, default='')
    # 估算重量
    guess_weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, default=0)
    can_size240 = models.IntegerField(null=True, blank=True, default=0)
    can_size120 = models.IntegerField(null=True, blank=True, default=0)
    # 不合格桶数
    unconform_can_size240 = models.IntegerField(null=True, blank=True, default=0)
    unconform_can_size120 = models.IntegerField(null=True, blank=True, default=0)
    # 确认状态  0未确认，1已经确认
    confirm_status = models.IntegerField(default=0, null=True, blank=True)
    # 确认时间
    confirm_time = models.IntegerField(default=0, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True, default='')
    street_coding = models.CharField(max_length=45, blank=True, null=True, default='')
    # 非居民端的评分
    grade_score = models.IntegerField(null=True, blank=True, default=0)
    # 非居民的反馈信息
    feedback = models.CharField(max_length=200, blank=True, null=True, default='')
    stream_number = models.CharField(max_length=45, blank=True, null=True, default='')
    car_flow_id = models.CharField(max_length=45, blank=True, null=True, default='')
    expiration_time = models.IntegerField(default=0, blank=True, null=True)
    remark = models.CharField(max_length=255, blank=True, null=True, default='')

    class Meta:
        managed = False
        db_table = 'terminal_record'
        app_label = 'ljfl_db'


class WechatSessionKey(models.Model):
    """微信 session_key
    """
    uuid = models.CharField(max_length=32, verbose_name='uuid')
    state = models.IntegerField(default=100, verbose_name='状态')
    open_id = models.CharField(max_length=50, verbose_name='当前程序用户唯一标识')
    session_key = models.CharField(max_length=50, verbose_name='解密encrypted_data')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = "微信 session_key"
        verbose_name_plural = verbose_name
        db_table = 'MiniApp_wechatsessionkey'
        app_label = 'ljfl_db'


class WeChatAppID(models.Model):
    """微信 配置 app_id app_secret
    """
    app_id = models.CharField(max_length=50, verbose_name='app_id')
    app_secret = models.CharField(max_length=50, verbose_name='app_secret')
    state = models.IntegerField(default=100, verbose_name='app_secret')
    name = models.CharField(max_length=50, verbose_name='项目名称')
    note = models.CharField(max_length=30, verbose_name='项目备注')
    login_type = models.CharField(max_length=50, verbose_name='登陆类型')
    expire_in = models.IntegerField(verbose_name='Token有效时长(秒)', default=86400)
    area_coding = models.CharField(max_length=32, verbose_name='区coding')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = "微信配置"
        verbose_name_plural = verbose_name
        db_table = 'MiniApp_wechatappid'
        app_label = 'ljfl_db'


class CarFlowRecord(models.Model):
    car_flow_id = models.CharField(max_length=45)
    car_id = models.CharField(max_length=45)
    car_num = models.CharField(max_length=45)
    type_id = models.CharField(max_length=45, blank=True, null=True)
    weight = models.FloatField(blank=True, null=True)
    factory_location_id = models.CharField(max_length=45, blank=True, null=True)
    factory_location_name = models.CharField(max_length=45, blank=True, null=True)
    cover = models.CharField(max_length=1000, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    quality = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    status = models.IntegerField(blank=True, null=True)
    is_confirm = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    confirm_time = models.IntegerField(blank=True, null=True)
    expire_time = models.IntegerField(blank=True, null=True)
    start_time = models.IntegerField(blank=True, null=True)
    finished_time = models.IntegerField(blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    terminal_factory_record_id = models.CharField(max_length=45, blank=True, null=True, default='')

    class Meta:
        managed = False
        db_table = 'car_flow_record'
        app_label = 'ljfl_db'


class RubbishType(models.Model):
    type_id = models.CharField(max_length=45)
    name = models.CharField(max_length=45, blank=True, null=True)
    description = models.CharField(max_length=500, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'rubbish_type'
        app_label = 'ljfl_db'


class TerminalFactoryRecord(models.Model):
    dev_type = models.IntegerField(null=True, blank=True, verbose_name=('设备类型type_code(必填)'))
    streamnumber = models.CharField(max_length=32, null=True, blank=True, verbose_name=('流水号'))  # 流水号0desc 1 asc
    platenumber = models.CharField(max_length=32, null=True, blank=True, verbose_name=('车牌号码'))  # 车牌号码
    drivername = models.CharField(max_length=32, null=True, blank=True, verbose_name=('司机姓名'))  # 司机姓名
    netweight = models.CharField(max_length=32, null=True, blank=True, verbose_name=('净重'))  # 净重
    weightptime = models.DateTimeField(null=True, blank=True, verbose_name=('皮重时间'))  # 皮重时间
    carrierunit = models.CharField(max_length=32, null=True, blank=True, verbose_name=('承运单位'))  # 承运单位
    harvestunits = models.CharField(max_length=32, null=True, blank=True, verbose_name=(' 收货单位'))  # 收货单位
    weightmtime = models.DateTimeField(null=True, blank=True, verbose_name=('毛重时间'))  # 毛重时间
    pweight = models.CharField(max_length=32, null=True, blank=True, verbose_name=('皮重'))  # 皮重
    mweight = models.CharField(max_length=32, null=True, blank=True, verbose_name=('毛重'))  # 皮重
    deliveryunit = models.CharField(max_length=32, null=True, blank=True, verbose_name=(' 发货单位'))  # 发货单位
    breedname = models.CharField(max_length=32, null=True, blank=True, verbose_name=('品种名称'))  # 品种名称
    specificationsmodels = models.CharField(max_length=32, null=True, blank=True, verbose_name=('规格型号'))  # 规格型号
    theinspector = models.CharField(max_length=32, null=True, blank=True, verbose_name=('检查员'))  # 检查员
    descr = models.CharField(max_length=32, null=True, blank=True, verbose_name=('备注说明'))  # 备注说明
    warehousing = models.CharField(max_length=32, null=True, blank=True, verbose_name=(' 是否入库'))  # 是否入库
    area = models.CharField(max_length=32, null=True, blank=True, verbose_name=('所属地区'))  # 所属地区
    weighingName = models.CharField(max_length=32, null=True, blank=True, verbose_name=('地磅的名称'))  # 地磅的名称

    mweightOriginal = models.TextField(null=True, blank=True, verbose_name=('进厂称重原始读取串口重量信号'))
    mweightParsing = models.TextField(null=True, blank=True, verbose_name=('进厂称重原始读取串口重量信号解析后的数据'))
    pweightOriginal = models.TextField(null=True, blank=True, verbose_name=('出厂称重原始读取串口重量信号'))
    pweightParsing = models.TextField(null=True, blank=True, verbose_name=('出厂称重原始读取串口重量信号解析后的数据'))

    inphoto = models.CharField(max_length=64, null=True, blank=True, verbose_name=('进厂图片'))  # 进厂图片
    outphoto = models.CharField(max_length=64, null=True, blank=True, verbose_name=('出厂图片'))  # 出厂图片
    createBy = models.CharField(max_length=32, null=True, blank=True, verbose_name=('创建者'))  # 创建者
    createDate = models.DateTimeField(default=timezone.now, verbose_name=('创建时间'))
    updateBy = models.CharField(max_length=32, null=True, blank=True, verbose_name=('更新者'))  # 更新者
    updateDate = models.DateTimeField(auto_now=True)
    # date时间
    ser_date = models.DateField(default=None, verbose_name=('date时间'))
    # 增加字段街道、社区、垃圾楼、主体
    street_name = models.CharField(max_length=255, default='', verbose_name=(u'街道名称'))
    comm_name = models.CharField(max_length=255, default='', verbose_name=(u'社区名称'))
    cleaning_point_name = models.CharField(max_length=255, default='', verbose_name=(u'垃圾楼名称'))
    organization_name = models.CharField(max_length=255, default='', verbose_name=(u'主体名称'))
    kitchen_photo = models.CharField(max_length=2000, default='', verbose_name=('厨余、餐厨照片'))
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    type_id = models.CharField(max_length=45, blank=True, null=True)
    source_type = models.IntegerField(blank=True, null=True)
    terminal_factory_record_id = models.CharField(max_length=45, default='')
    clean_point_id = models.CharField(max_length=45, blank=True, null=True)
    factory_location_id = models.CharField(max_length=45, blank=True, null=True)
    factory_type = models.CharField(max_length=45, default='', blank=True, null=True)
    is_direct = models.IntegerField(default=0, null=True, blank=True, verbose_name=('是否直运/转运（0直运，1转运）'))
    is_abnormal = models.IntegerField(default=0, null=True, blank=True, verbose_name=('是否异常（0正常，1代表异常）'))
    is_area = models.IntegerField(default=0, null=True, blank=True, verbose_name=('是否跨区（0不跨区，1跨区）'))

    class Meta:
        managed = False
        db_table = 'terminal_factory_record'
        app_label = 'ljfl_db'


class CarType(models.Model):
    car_type_id = models.CharField(max_length=45)
    name = models.CharField(max_length=45, blank=True, null=True)
    description = models.CharField(max_length=500, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'car_type'
        app_label = 'ljfl_db'


class OrgRfid(models.Model):
    rfid = models.CharField(max_length=45)
    rfid_num = models.CharField(max_length=45)
    type_id = models.CharField(max_length=45)
    name = models.CharField(max_length=45, blank=True, null=True, default='')
    org_id = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    station_id = models.CharField(max_length=45, blank=True, null=True)
    bind_name = models.CharField(max_length=45, blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    address = models.CharField(max_length=250, blank=True, null=True)
    tare = models.FloatField(blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(default=0)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(default=make_time_stamp)
    update_time = models.IntegerField(default=make_time_stamp)
    contacts = models.CharField(max_length=45, blank=True, null=True)
    phone = models.CharField(max_length=45, blank=True, null=True)
    rfid_type_id = models.CharField(max_length=45)
    latitude_b = models.DecimalField(max_digits=20, decimal_places=11, default=0)
    longitude_b = models.DecimalField(max_digits=20, decimal_places=11, default=0)
    latitude_g = models.DecimalField(max_digits=20, decimal_places=11, default=0)
    longitude_g = models.DecimalField(max_digits=20, decimal_places=11, default=0)
    is_declare = models.IntegerField(default=0)
    size = models.CharField(max_length=100, blank=True, null=True)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True)
    gufei_rfid_code = models.CharField(max_length=45, blank=True, null=True, default='')
    push_status = models.IntegerField(default=0)
    logout_status = models.IntegerField(blank=True, null=True, default=0)

    class Meta:
        managed = False
        db_table = 'org_rfid'
        app_label = 'ljfl_db'


class RfidType(models.Model):
    rfid_type_id = models.CharField(max_length=45)
    name = models.CharField(max_length=45, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(default=0)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(default=make_time_stamp)
    update_time = models.IntegerField(default=make_time_stamp)

    class Meta:
        managed = False
        db_table = 'rfid_type'
        app_label = 'ljfl_db'


class TransportContract(models.Model):
    """合同信息"""
    contract_id = models.CharField(max_length=45, verbose_name="唯一id")
    transport_company_id = models.CharField(max_length=45, null=True, blank=True, verbose_name=('公司id'))
    transport_company_name = models.CharField(blank=True, null=False, max_length=50, verbose_name=('公司名称'))
    area_coding = models.CharField(blank=True, null=True, max_length=120, verbose_name=('服务区域'))
    street_coding = models.CharField(max_length=255, blank=True, null=True, verbose_name='街道coding')
    street_name = models.CharField(max_length=500, blank=True, null=True, verbose_name='街道名称')
    org_id = models.CharField(max_length=64, blank=True, null=True, verbose_name='非居民单位id')
    organization = models.CharField(max_length=64, blank=True, null=True, verbose_name='非居民单位')
    org_type = models.CharField(max_length=32, blank=True, null=True, verbose_name='单位类型')
    detaile_address = models.CharField(blank=True, null=True, max_length=255, verbose_name=('详细地址'))
    food_license = models.CharField(blank=True, null=True, max_length=255, verbose_name=('食品经营许可证'))
    credit_code = models.CharField(blank=True, null=True, max_length=32, verbose_name=('社会统一信用代码'))
    contract_num = models.CharField(blank=True, null=True, max_length=64, verbose_name=('合同编号'))
    rubbish_type = models.CharField(max_length=255, null=True, blank=True, default='', verbose_name=('垃圾类型id'))
    rubbish_type_name = models.CharField(max_length=255, null=True, blank=True, default='', verbose_name=('垃圾类型名称'))
    viald_time = models.CharField(max_length=45, blank=True, null=True, verbose_name=(u'服务期限'))
    sign_date = models.CharField(blank=True, null=True, max_length=32, verbose_name=('合同签订日期'))
    collect_date = models.CharField(blank=True, null=True, max_length=32, verbose_name=('收集时间'))
    transport_contacts = models.CharField(max_length=45, blank=True, null=True, verbose_name=(u'清运公司联系人'))
    transport_phone = models.CharField(max_length=30, blank=True, null=True, verbose_name=(u'清运公司联系电话'))
    contract_picture = models.TextField(blank=True, null=True, verbose_name=('合同照片'))
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    is_deleted = models.IntegerField(default=0, verbose_name=('删除状态  0 未删除  1删除'))
    contract_status = models.IntegerField(default=1, verbose_name=('合同状态：1正常 2过期 3作废 '))
    belong_to = models.CharField(max_length=20, null=True, default='线下', verbose_name='合同归属')
    service_start_date = models.DateField(null=True, verbose_name='服务期限开始日期')
    service_end_date = models.DateField(null=True, verbose_name='服务期限结束日期')

    class Meta:
        db_table = 'transport_contract'
        verbose_name = u"合同信息"
        app_label = 'ljfl_db'


class OpinionDetails(models.Model):
    uuid = models.CharField(max_length=45)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    car_record_id = models.CharField(max_length=45, blank=True, null=True)
    org_name = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    car_num = models.CharField(max_length=45, blank=True, null=True)
    feed_back_type = models.CharField(max_length=45, blank=True, null=True)
    opinion_description = models.CharField(max_length=255, blank=True, null=True)
    cover = models.CharField(max_length=2000, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    phone = models.CharField(max_length=45, blank=True, null=True)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True)
    relation_id = models.CharField(max_length=45, blank=True, null=True)
    is_deal = models.IntegerField(blank=True, null=True, default=0)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    status = models.IntegerField(blank=True, null=True, default=0)

    class Meta:
        managed = False
        db_table = 'opinion_details'
        app_label = 'ljfl_db'


class ApplyStationRecod(models.Model):
    """非居民申请报修桶"""
    apply_id = models.CharField(max_length=45)
    apply_user = models.CharField(max_length=45, blank=True, null=True, verbose_name='申请人')
    org_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='org_id')
    rfid_num = models.CharField(max_length=45, blank=True, null=True, verbose_name='标签号')
    source = models.CharField(max_length=12, blank=True, null=True, default='no_org',
                              verbose_name='来源  非居民no_org  司机端car')
    reason = models.IntegerField(blank=True, null=True, verbose_name='原因: 1申请新桶, 2桶破损, 3签破损, 4退桶')
    type_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='垃圾类型')
    size = models.IntegerField(blank=True, null=True, verbose_name='桶规格 0 120L 1 240L')
    count = models.IntegerField(max_length=12, blank=True, null=True, verbose_name='桶个数')
    status = models.IntegerField(default=0, blank=True, null=True, verbose_name='0未处理 1已处理')
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'apply_station_record'
        app_label = 'ljfl_db'


class AppealRecod(models.Model):
    """非居民申诉"""
    appeal_id = models.CharField(max_length=45)
    apply_user = models.CharField(max_length=45, blank=True, null=True, verbose_name='申请人')
    org_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='org_id')
    bill_factory_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='末端联单uuid')
    bill_org_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='收运联单uuid')
    appeal_type = models.CharField(max_length=45, blank=True, null=True, verbose_name='反馈类别')
    content = models.TextField(blank=True, null=True, verbose_name='投诉内容')
    photos = models.TextField(blank=True, null=True, verbose_name='投诉照片')
    qu_content = models.TextField(blank=True, null=True, verbose_name='再次投诉内容')
    qu_photos = models.TextField(blank=True, null=True, verbose_name='再次投诉照片')
    company_count = models.IntegerField(default=0, blank=True, null=True, verbose_name='公司申诉桶数')
    qu_count = models.IntegerField(default=0, blank=True, null=True, verbose_name='区申诉桶数')
    status = models.IntegerField(default=0, blank=True, null=True, verbose_name='0公司未处理 1区未处理 2公司已处理 3区已处理')
    appeal_time = models.DateTimeField(null=True, blank=True, verbose_name=('申诉时间'))  # 申诉时间24小时不处理自动审核通过
    re_appeal_time = models.DateTimeField(null=True, blank=True, verbose_name=('二次申诉时间'))  # 申诉时间24小时不处理自动审核通过
    company_deal_time = models.DateTimeField(null=True, blank=True,
                                             verbose_name=('清运公司处理时间'))  # 清运公司拒绝根据处理时间判断24小时后不能再次申诉
    re_appeal = models.IntegerField(default=0, blank=True, null=True, verbose_name='是否可以再次申诉 0否 1是 2已过申诉时间')
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'appeal_record'
        app_label = 'ljfl_db'


class AppealDetailRecod(models.Model):
    """非居民申诉详情记录"""
    appeal_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='申诉uuid')
    appeal_detail_id = models.CharField(max_length=45)
    bill_factory_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='末端联单uuid')
    bill_org_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='收运联单uuid')
    car_record_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='车辆称重记录uuid')
    a_type = models.IntegerField(blank=True, null=True, default=0, verbose_name='反馈类别  0桶重不符，1主体不符')
    size = models.CharField(max_length=45, blank=True, null=True, verbose_name='桶规格')
    capacity = models.IntegerField(blank=True, null=True, default=0, verbose_name='0 整桶/  1半桶')
    deal_weight = models.FloatField(blank=True, null=True, default=0, verbose_name='修正重量kg')
    record_weight = models.FloatField(blank=True, null=True, default=0, verbose_name='原始重量kg')
    weight_type = models.CharField(max_length=255, blank=True, null=True, verbose_name='重量类型 CAR-车辆称重 CAPACITY-容重')
    company_deal_status = models.IntegerField(default=0, blank=True, null=True,
                                              verbose_name='0清运公司未处理 1清运公司已处理 2清运公司已拒绝')
    area_deal_status = models.IntegerField(default=0, blank=True, null=True, verbose_name='1区未处理 2区已处理 3区已拒绝')
    company_confirm_time = models.DateTimeField(null=True, blank=True, verbose_name=('清运公司处理时间'))
    area_confirm_time = models.DateTimeField(null=True, blank=True, verbose_name=('区处理时间'))
    company_confirmer = models.CharField(max_length=45, blank=True, null=True, verbose_name='清运公司处理人')
    area_confirmer = models.CharField(max_length=45, blank=True, null=True, verbose_name='区处理人')

    class Meta:
        managed = False
        db_table = 'appeal_detail_record'
        app_label = 'ljfl_db'


class CarRecordFlow(models.Model):
    car_record_id = models.CharField(max_length=255, unique=True)
    bill_factory_id = models.CharField(max_length=45, blank=True, null=True)
    bill_org_id = models.CharField(max_length=45, blank=True, null=True)
    terminal_record_id = models.CharField(max_length=45, blank=True, null=True)
    factory_location_id = models.CharField(max_length=45, blank=True, null=True)
    weight_type = models.CharField(max_length=255, blank=True, null=True)
    bill_weight = models.FloatField(blank=True, null=True)
    car_weight = models.FloatField(blank=True, null=True)
    capacity = models.CharField(max_length=45)
    capacity_weight = models.FloatField(blank=True, null=True)
    size = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    car_num = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    create_time = models.IntegerField( blank=True, null=True)
    

    class Meta:
        managed = False
        db_table = 'car_record_flow'
        app_label = 'ljfl_db'


class CarBillFactory(models.Model):
    bill_factory_id = models.CharField(max_length=45)
    area_coding = models.CharField(max_length=16)
    transport_company_id = models.CharField(max_length=45)
    car_num = models.CharField(max_length=16)
    type_id = models.CharField(max_length=45)
    org_count = models.IntegerField()
    org_nonresident_count = models.IntegerField()
    org_resident_count = models.IntegerField()
    org_unknown_count = models.IntegerField()
    bill_count = models.IntegerField()
    bill_weight = models.FloatField()
    terminal_factory_record_id = models.CharField(max_length=45)
    terminal_factory_record_weight = models.FloatField(blank=True, null=True)
    terminal_factory_record_type_id = models.CharField(max_length=45, blank=True)
    terminal_factory_record_type_name = models.CharField(max_length=45, blank=True)
    factory_location_id = models.CharField(max_length=45)
    start_time = models.IntegerField()
    is_finished = models.IntegerField()
    finish_time = models.IntegerField()
    work_duration = models.IntegerField()
    is_confirm = models.IntegerField()
    confirm_time = models.IntegerField()
    operator_relation_id = models.CharField(max_length=45)
    operator_username = models.CharField(max_length=45)
    is_deleted = models.IntegerField()
    create_time = models.IntegerField()
    update_time = models.IntegerField()
    remark = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'car_bill_factory'
        app_label = 'ljfl_db'


class CarBillOrg(models.Model):
    bill_factory_id = models.CharField(max_length=45)
    bill_org_id = models.CharField(max_length=45)
    bill_area_coding = models.CharField(max_length=45)
    car_num = models.CharField(max_length=45)
    org_id = models.CharField(max_length=45)
    type_id = models.CharField(max_length=45)
    bill_count = models.IntegerField()
    bill_weight = models.FloatField()
    car_120_count = models.IntegerField()
    car_240_count = models.IntegerField()
    car_120_weight = models.FloatField()
    car_240_weight = models.FloatField()
    capacity_120_count = models.IntegerField()
    capacity_240_count = models.IntegerField()
    capacity_120_weight = models.FloatField()
    capacity_240_weight = models.FloatField()
    message = models.CharField(max_length=200, blank=True, null=True)
    publisher = models.CharField(max_length=45, blank=True, null=True)
    feedback = models.CharField(max_length=200, blank=True, null=True)
    start_time = models.IntegerField()
    finish_time = models.IntegerField()
    is_confirm = models.IntegerField()
    confirm_time = models.IntegerField()
    operator_relation_id = models.CharField(max_length=45)
    operator_username = models.CharField(max_length=45)
    is_deleted = models.IntegerField()
    create_time = models.IntegerField()
    update_time = models.IntegerField()
    remark = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'car_bill_org'
        app_label = 'ljfl_db'


class VersionModel(models.Model):
    # 版本号
    newVersionCode = models.CharField(max_length=200, null=True, blank=True, verbose_name=('版本号'))
    # 版本信息
    updateMessage = models.TextField(blank=True, null=True, verbose_name=('版本信息'))
    # 版本名
    newVersionName = models.TextField(blank=True, null=True, verbose_name=('版本名'))
    # app名称
    appName = models.TextField(blank=True, null=True, verbose_name=('app名称'))
    # url
    url = models.CharField(max_length=200, null=True, blank=True, verbose_name=('URL'))
    # 增加更新的app名称
    userName = models.TextField(blank=True, null=True, verbose_name=('增加更新的app名称'))
    # 备注
    remark = models.CharField(max_length=200, null=True, blank=True, verbose_name=('备注'))

    def __str__(self):
        return self.appName

    class Meta:
        db_table = 'app_version'
        app_label = 'ljfl_db'


# 甲方(非居民)
class NonResidentBase(models.Model):
    company_id = models.CharField(max_length=50, db_index=True, verbose_name='公司id')
    org_id = models.CharField(max_length=50, db_index=True, verbose_name='非居民id')
    bank_name = models.CharField(max_length=30, verbose_name='账户开户行')
    account_name = models.CharField(max_length=30, verbose_name='账户名称')
    account = models.CharField(max_length=30, verbose_name='账号')
    business_license_code = models.CharField(max_length=30, null=True, verbose_name='经营许可证号')
    business_license_date = models.DateField(null=True, verbose_name='经营许可有效期')
    unit_nature = models.CharField(max_length=30, null=True, verbose_name='单位性质')
    right_obligations = models.TextField(null=True, verbose_name='乙方权利和义务')
    break_duty = models.TextField(null=True, verbose_name='乙方违约责任')
    convention = models.TextField(null=True, verbose_name='甲方约定要求')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.IntegerField(default=1, verbose_name='0 非正常 1正常')
    rubbishes = models.CharField(default='RESTAURANTS', max_length=64, null=True, blank=True, verbose_name="垃圾类型")

    def __str__(self):
        return f'{self.company_id}-{self.org_id}'

    class Meta:
        managed = False
        db_table = 'nonresident_base'
        verbose_name = '基础信息(甲方(非居民))'
        app_label = 'ljfl_db'
        unique_together = ('company_id', 'org_id', 'rubbishes')


class NonResidentLogout(models.Model):
    """非居民注销"""
    logout_id = models.CharField(max_length=45)
    org_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='org_id')
    reson = models.IntegerField(blank=True, null=True, verbose_name='注销原因')
    content = models.TextField(blank=True, null=True, verbose_name='原因描述')
    deal_type = models.IntegerField(default=0, blank=True, null=True, verbose_name='审核对象 :0清运公司 1街道')
    status = models.IntegerField(default=0, blank=True, null=True, verbose_name='0未处理 1通过  2拒绝')
    deal_content = models.IntegerField(blank=True, null=True, verbose_name='处理描述')
    submit_time = models.DateTimeField(null=True, blank=True, verbose_name=('提交时间'))
    deal_time = models.DateTimeField(null=True, blank=True, verbose_name=('处理时间'))
    deal_user = models.CharField(max_length=45, blank=True, null=True, verbose_name='处理人')
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'nonresident_logout'
        app_label = 'ljfl_db'


class CleaningPoint(models.Model):
    cleaning_point_id = models.CharField(max_length=45)
    name = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    stats_type = models.CharField(max_length=45, blank=True, null=True)
    facility_num = models.CharField(max_length=45, blank=True, null=True)
    facility_type = models.CharField(max_length=45, blank=True, null=True)
    facility_status = models.CharField(max_length=45, blank=True, null=True)
    use_date = models.CharField(max_length=45, blank=True, null=True)
    receive_date = models.CharField(max_length=45, blank=True, null=True)
    invest_money = models.CharField(max_length=45, blank=True, null=True)
    floor_area = models.CharField(max_length=45, blank=True, null=True)
    site_area = models.CharField(max_length=45, blank=True, null=True)
    property_unit = models.CharField(max_length=45, blank=True, null=True)
    work_date = models.CharField(max_length=45, blank=True, null=True)
    service_area = models.CharField(max_length=45, blank=True, null=True)
    hold_num = models.CharField(max_length=45, blank=True, null=True)
    bin_num = models.IntegerField(default=0, blank=True, null=True)
    s_trash_sort = models.CharField(max_length=45, blank=True, null=True)
    salt_pool_cubage = models.CharField(max_length=45, blank=True, null=True)
    car_stop_num = models.CharField(max_length=45, blank=True, null=True)
    clean_energy_car_num = models.CharField(max_length=45, blank=True, null=True)
    charging_pole_num = models.CharField(max_length=45, blank=True, null=True)
    rest_point_num = models.CharField(max_length=45, blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    address = models.CharField(max_length=250, blank=True, null=True)
    contacts = models.CharField(max_length=45, blank=True, null=True)
    phone = models.CharField(max_length=45, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    type_id = models.CharField(max_length=45, default="")
    update_date = models.DateTimeField(auto_now=True, blank=True, null=True)
    other_cabinet = models.IntegerField(blank=True, null=True)
    kitchen_cabinet = models.IntegerField(blank=True, null=True)
    manage_unit = models.CharField(max_length=255, default="")
    operate_unit = models.CharField(max_length=255, default="")
    garbage_whereabouts = models.CharField(max_length=255, default="")
    state = models.IntegerField(default=1)
    approved_load = models.CharField(max_length=45, default="")
    is_camera = models.IntegerField(default=0, blank=True, null=True)
    cover = models.CharField(max_length=1000, blank=True, null=True)
    metering_device = models.IntegerField(default=0, blank=True, null=True)
    uhf_delivery = models.IntegerField(default=0, blank=True, null=True)
    uhf_enter = models.IntegerField(default=0, blank=True, null=True)
    street_system = models.IntegerField(default=0, blank=True, null=True)
    city_system = models.IntegerField(default=0, blank=True, null=True)
    hidden = models.IntegerField(default=0, blank=True, null=True)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True, default='')

    class Meta:
        managed = False
        db_table = 'cleaning_point'
        app_label = 'ljfl_db'


class CleaningPointRecord(models.Model):
    cleaning_point_record_id = models.CharField(max_length=45, unique=True, verbose_name='清洁站记录唯一ID')
    cleaning_point_id = models.CharField(max_length=45, verbose_name='清洁站唯一ID')
    device_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='设备ID')
    type_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='垃圾类型ID')
    sub_type_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='子类型ID')
    weight = models.FloatField(blank=True, null=True, verbose_name='重量')
    cover = models.CharField(max_length=1000, blank=True, null=True, verbose_name='封面')
    rfid_num = models.CharField(max_length=45, blank=True, null=True, verbose_name='标签号')
    org_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='主体ID')
    org_name = models.CharField(max_length=100, blank=True, null=True, verbose_name='主体名称')
    area_coding = models.CharField(max_length=45, blank=True, null=True, verbose_name='区级行政区划代码')
    street_coding = models.CharField(max_length=45, blank=True, null=True, verbose_name='街道行政区划代码')
    comm_coding = models.CharField(max_length=45, blank=True, null=True, verbose_name='社区行政区划代码')
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True, verbose_name='纬度')
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True, verbose_name='经度')
    is_deleted = models.IntegerField(default=0, verbose_name='删除标记，0为有效数据，1为删除数据')
    creator = models.CharField(max_length=45, blank=True, null=True, verbose_name='创建者')
    remark = models.CharField(max_length=500, blank=True, null=True, verbose_name='备注')
    create_time = models.IntegerField(blank=True, null=True, verbose_name='创建时间')
    update_time = models.IntegerField(blank=True, null=True, verbose_name='更新时间')
    stream_number = models.CharField(max_length=45, default='', verbose_name='流水号，唯一，UUID')
    car_num = models.CharField(max_length=45, default='', blank=True, verbose_name='车牌号')

    class Meta:
        db_table = 'cleaning_point_record'
        app_label = 'ljfl_db'


class CleaningPointTransRecord(models.Model):
    id = models.AutoField(primary_key=True, verbose_name="主键")
    cleaning_point_trans_record_id = models.CharField(max_length=45, verbose_name="唯一id")
    cleaning_point_id = models.CharField(max_length=45, verbose_name="清洁站唯一id")
    car_id = models.CharField(max_length=45, verbose_name="唯一id")
    car_num = models.CharField(max_length=45, null=True, blank=True, verbose_name="车牌")
    type_id = models.CharField(max_length=45, null=True, blank=True, verbose_name="垃圾类型id")
    weight = models.FloatField(null=True, blank=True, verbose_name="重量")
    cover = models.CharField(max_length=1000, null=True, blank=True, verbose_name="封面")
    rfid_num = models.CharField(max_length=45, null=True, blank=True, verbose_name="标签号")
    org_id = models.CharField(max_length=45, null=True, blank=True, verbose_name="对应的主体id")
    org_name = models.CharField(max_length=45, null=True, blank=True, verbose_name="对应的主体名称")
    area_coding = models.CharField(max_length=45, null=True, blank=True, verbose_name="区级行政区划代码")
    street_coding = models.CharField(max_length=45, null=True, blank=True, verbose_name="街道行政区划代码")
    comm_coding = models.CharField(max_length=45, null=True, blank=True, verbose_name="社区行政区划代码")
    latitude = models.DecimalField(max_digits=20, decimal_places=11, null=True, blank=True, verbose_name="纬度")
    longitude = models.DecimalField(max_digits=20, decimal_places=11, null=True, blank=True, verbose_name="经度")
    is_deleted = models.IntegerField(default=0, verbose_name="删除标记,0是有效数据，1是删除数据")
    remark = models.CharField(max_length=500, null=True, blank=True, verbose_name="备注")
    create_time = models.IntegerField(null=True, blank=True, verbose_name="创建时间（识别标签）")
    update_time = models.IntegerField(null=True, blank=True, verbose_name="更新时间")
    factory_location_id = models.CharField(max_length=45, default='', verbose_name="末端厂id")
    factory_location_name = models.CharField(max_length=255, default='', verbose_name="末端厂名称")
    into_factory_time = models.IntegerField(default=0, verbose_name="进厂时间")
    uuid = models.CharField(max_length=45, default='', verbose_name="流水号")

    class Meta:
        db_table = 'cleaning_point_trans_record'
        app_label = 'ljfl_db'


class CleaningPointTransHandSelectedRedcord(models.Model):
    cleaning_point_trans_hand_selected_id = models.CharField(max_length=45)
    cleaning_point_id = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    car_num = models.CharField(max_length=45, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    status = models.IntegerField(blank=True, null=True,default=0)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    latitude_b = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude_b = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    latitude_g = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude_g = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    address = models.CharField(max_length=1000, blank=True, null=True)


    class Meta:
        managed = False
        db_table = 'cleaning_point_trans_hand_selected'
        app_label = 'ljfl_db'


class VirtualAreaStreet(models.Model):
    """虚拟区 - 街道 关联表"""

    street_code = models.CharField(max_length=16)
    street_name = models.CharField(max_length=128)
    virtual_area_code = models.CharField(max_length=61)

    class Meta:
        managed = False
        db_table = "virtual_area_street"
        app_label = "ljfl_db"
        

class OrgGroup(models.Model):
    uuid = models.CharField(max_length=40, unique=True, verbose_name='唯一ID')
    org_id = models.CharField(max_length=45, verbose_name='用户org_id')
    org_group_id = models.CharField(max_length=45, verbose_name='集团用户org_id')
    org_group_name = models.CharField(max_length=45, verbose_name='集团用户名称')
    org_group_clean_no = models.CharField(max_length=45, verbose_name='集团用户排放代码')
    org_group_credit_code = models.CharField(max_length=45, verbose_name='集团用户信用代码')
    status = models.IntegerField(verbose_name='状态 0审核中 1已通过 2拒绝', default=0)
    refuse_reason = models.CharField(max_length=255, null=True, verbose_name='拒绝原因')
    prove_file = models.TextField(verbose_name='证明文件', null=True)
    logout_status = models.IntegerField(blank=True, null=True, default=0)
    rubbishes = models.CharField(default='RESTAURANTS', max_length=64, null=True, blank=True,
                                 verbose_name="垃圾类型RESTAURANTS或OTHER")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        managed = False
        db_table = 'org_group'
        app_label = 'ljfl_declare_db'
        verbose_name = '集团用户'


class OrgGroupBase(models.Model):
    uuid = models.CharField(max_length=40, unique=True, verbose_name='唯一ID')
    org_id = models.CharField(max_length=45, verbose_name='用户org_id')
    org_group_id = models.CharField(max_length=45, verbose_name='集团用户org_id')
    org_group_name = models.CharField(max_length=45, verbose_name='集团用户名称')
    org_group_clean_no = models.CharField(max_length=45, verbose_name='集团用户排放代码')
    org_group_credit_code = models.CharField(max_length=45, verbose_name='集团用户信用代码')
    status = models.IntegerField(verbose_name='状态 0审核中 1已通过 2拒绝', default=0)
    refuse_reason = models.CharField(max_length=255, null=True, verbose_name='拒绝原因')
    prove_file = models.TextField(verbose_name='证明文件', null=True)
    logout_status = models.IntegerField(blank=True, null=True, default=0)
    rubbishes = models.CharField(default='RESTAURANTS', max_length=64, null=True, blank=True,
                                 verbose_name="垃圾类型RESTAURANTS或OTHER")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        managed = False
        db_table = 'org_group'
        app_label = 'ljfl_db'
        verbose_name = '集团用户'


class RepairCar(models.Model):
    car_num = models.CharField(max_length=45, verbose_name='车牌号')
    car_record_id = models.CharField(max_length=500, verbose_name='收运ids')
    abnormal_type = models.IntegerField(verbose_name='异常类型 0称重不准 1有轨迹无称重记录 2有称重记录无轨迹')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    status = models.IntegerField(default=0, blank=True, null=True,verbose_name='0未处理，1已处理')
    
    class Meta:
        managed = False
        db_table = 'repair_car'
        app_label = 'ljfl_db'
        verbose_name = '司机端异常报修车辆'


class CarState(models.Model):
    STATE_HAS_GPS = 1
    STATE_HAS_RECORD = 2

    car_num = models.CharField(max_length=20, blank=True, null=True, verbose_name=('车牌号'))
    state_type = models.IntegerField(blank=True, null=True, verbose_name=('车辆状态类型，1代表轨迹，2代表收运记录,'
                                                                          ' 4身份识别 5质量抓拍, 6去向 ,'
                                                                          '7直运清运和转运车有去向 8:直运清运和转运车有来源'
                                                                          ))
    create_time = models.DateTimeField(verbose_name=('创建时间'))

    class Meta:
        managed = False
        db_table = 'car_state'
        app_label = 'jfpt'


class OrganizationOther(models.Model):
    org_id = models.CharField(max_length=45)
    declare_type = models.CharField(max_length=45, default='Normal')
    source_type = models.CharField(max_length=45, default='MEAPP')
    clean_code = models.CharField(max_length=45, blank=True)
    clean_no = models.CharField(max_length=45, blank=True)
    org_type_id = models.CharField(max_length=45)
    org_sub_type_id = models.CharField(max_length=45)
    name = models.CharField(max_length=100, blank=True, null=True)
    cover = models.CharField(max_length=1000, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    farmily_num = models.IntegerField(blank=True, null=True)
    resident_num = models.IntegerField(blank=True, null=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    address = models.CharField(max_length=250, blank=True, null=True)
    contacts = models.CharField(max_length=256, blank=True, null=True)
    phone = models.CharField(max_length=256, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    update_date = models.DateTimeField(blank=True, null=True)
    longitude_b = models.DecimalField(max_digits=20, decimal_places=8, blank=True, null=True)
    latitude_b = models.DecimalField(max_digits=20, decimal_places=8, blank=True, null=True)
    longitude_g = models.DecimalField(max_digits=20, decimal_places=8, blank=True, null=True)
    latitude_g = models.DecimalField(max_digits=20, decimal_places=8, blank=True, null=True)
    liabler = models.CharField(max_length=255)
    is_declare = models.IntegerField()
    sort = models.IntegerField(blank=True, null=True)
    scale = models.CharField(max_length=45, blank=True, null=True)
    examine_org = models.IntegerField(blank=True, null=True)
    credit_code = models.CharField(max_length=45, blank=True, null=True)
    permission_code = models.CharField(max_length=255, blank=True, null=True)
    is_reduce_device = models.IntegerField()
    reduce_water_device = models.CharField(max_length=56, default='', blank=True)
    reduce_oil_device = models.CharField(max_length=56, default='', blank=True)
    is_local_device = models.IntegerField()
    local_factory = models.CharField(max_length=255, default='', blank=True)
    local_capacity = models.CharField(max_length=255, default='', blank=True)
    local_process = models.CharField(max_length=255, default='', blank=True)
    local_out_solid_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_solid_direction = models.CharField(max_length=255, default='', blank=True)
    local_out_water_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_water_direction = models.CharField(max_length=255, default='', blank=True)
    local_out_dregs_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_dregs_direction = models.CharField(max_length=255, default='', blank=True)
    have_contract = models.IntegerField(blank=True, null=True, default=0)
    non_resident_mark = models.IntegerField(blank=True, null=True)
    official_org_name = models.CharField(max_length=45, blank=True, null=True)
    official_address = models.CharField(max_length=255, blank=True)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True)
    licence_cover = models.CharField(max_length=255, blank=True, null=True)
    mam_type = models.CharField(max_length=255)
    mam_subtype = models.CharField(max_length=255)
    restaurant_predict_weight = models.IntegerField(default=0)
    restaurant_trash_120 = models.IntegerField(default=0)
    restaurant_trash_240 = models.IntegerField(default=0)
    restaurant_trash = models.IntegerField(default=0)
    restaurant_trash_rfid = models.IntegerField(default=0)
    other_predict_weight = models.IntegerField(default=0)
    other_trash_120 = models.IntegerField(default=0)
    other_trash_240 = models.IntegerField(default=0)
    other_trash = models.IntegerField(default=0)
    other_trash_rfid = models.IntegerField(default=0)
    logout_status = models.IntegerField(blank=True, null=True, default=0)
    transport_company_name = models.CharField(max_length=45, blank=True, null=True, default="", verbose_name="运输公司名称")
    floor_name = models.CharField(max_length=45, default="", blank=True, null=True, verbose_name='楼宇/商圈/建筑名称')
    floor_cover = models.TextField(default="", blank=True, null=True, verbose_name='楼宇/商圈/建筑名称照片')
    official_cover = models.TextField(default="", blank=True, null=True, verbose_name='营业执照图片')

    class Meta:
        managed = False
        db_table = 'organization_other'
        app_label = 'ljfl_db'


class RtuDevice(models.Model):
    rtu_device_id = models.CharField(max_length=45, blank=True, null=True)
    rtu_device_type_id = models.CharField(max_length=45, blank=True, null=True)
    rtu_device_sub_type_id = models.CharField(max_length=45, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    name = models.CharField(max_length=45, blank=True, null=True)
    mac = models.CharField(max_length=45, blank=True, null=True)
    cover = models.CharField(max_length=1000, blank=True, null=True)
    type_ids = models.CharField(max_length=250, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True, default='')
    latitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    longitude = models.DecimalField(max_digits=20, decimal_places=11, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    remark = models.CharField(max_length=500, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    contacts = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=255, blank=True, null=True)
    status = models.IntegerField(blank=True, null=True, default=0)
    throw_sort_station_id = models.CharField(max_length=255, blank=True, null=True, default='')

    class Meta:
        managed = False
        db_table = 'rtu_device'
        app_label = 'ljfl_db'


class PendingCompanyRegistration(models.Model):
    """待注册公司记录"""
    company_name = models.CharField(max_length=255, verbose_name="公司名称")
    company_contact_person = models.CharField(max_length=255, verbose_name="公司联系人")
    company_contact_phone = models.CharField(max_length=255, verbose_name="公司联系方式")
    
    area_coding = models.CharField(max_length=45, verbose_name='区coding')
    area_name = models.CharField(max_length=255, blank=True, null=True, verbose_name="服务区域名称")
    street_coding = models.CharField(max_length=45, verbose_name='街道coding')
    street_name = models.CharField(max_length=255, blank=True, null=True, verbose_name="服务街道名称")
    org_name = models.CharField(max_length=255, blank=True, null=True, verbose_name="非居民门头名称")
    org_manager_name = models.CharField(max_length=255, blank=True, null=True, verbose_name="非居民管理责任人")
    org_contact_person = models.CharField(max_length=255, blank=True, null=True, verbose_name="非居民联系人")
    org_contact_phone = models.CharField(max_length=255, blank=True, null=True, verbose_name="非居民联系方式")
    
    submit_time = models.DateTimeField(default=timezone.now, verbose_name="提交时间")
    status = models.IntegerField(default=0, verbose_name="状态 0-未处理 1-已处理")
    is_delete = models.IntegerField(default=0, verbose_name='是否删除')
    create_time = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    update_time = models.DateTimeField(auto_now=True, blank=True, null=True)
    
    
    class Meta:
        managed = False
        db_table = 'pending_company_registration'
        app_label = 'ljfl_db'
        verbose_name = "待注册公司记录"
        verbose_name_plural = "待注册公司记录"


class CleaningPointRecordFlow(models.Model):
    cleaning_point_record_id = models.CharField(max_length=255, unique=True)
    weight_type = models.CharField(max_length=255, blank=True, null=True,
                                   verbose_name="重量类型 WEIGHT-称重 CAPACITY-容重")
    source_weight = models.FloatField(blank=True, null=True, verbose_name="称重重量")
    capacity = models.CharField(max_length=45, verbose_name="容重 120L/240L 整桶/半桶", blank=True, null=True)
    capacity_weight = models.FloatField(blank=True, null=True, verbose_name="容重重量")
    size = models.CharField(max_length=45, blank=True, null=True, verbose_name="桶规格 240L/120L")
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    cleaning_point_id = models.CharField(max_length=45, blank=True, null=True, verbose_name="垃圾楼id")
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    confirm_time = models.IntegerField(blank=True, null=True)
    confirm_desc = models.CharField(max_length=45, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'cleaning_point_record_flow'
        app_label = 'ljfl_db'
