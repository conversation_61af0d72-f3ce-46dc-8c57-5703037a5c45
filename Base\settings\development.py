#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import *
EMAIL_ENABLE = False  # 邮件通知是否可用

DATABASES = {
    'default': {},
    'ljfl_declare_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_declare_db',
        'USER': 'root',
        'HOST': '**********',
        'PASSWORD': 'zhitongbr2019',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db_nonresident',
        'USER': 'root',
        'HOST': '**********',
        'PASSWORD': 'zhitongbr2019',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'transport_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'transport_db',
        'USER': 'root',
        'HOST': '**********0',
        'PASSWORD': '!Qaz2wsx',
        'PORT': 3306,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
}

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://**********:6379/4',
        'KEY_PREFIX': 'apiLocal:NonResident',
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    },
}

# 日志
# LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     # 样式
#     'formatters': {
#         'verbose': {
#             'format': '[%(asctime)s] [%(levelname)s] %(message)s'
#         },
#     },
#     # 日志记录的位置 控制台,文件,email
#     'handlers': {
#         'console': {
#             'level': 'INFO',
#             'class': 'logging.StreamHandler',
#             'formatter': 'verbose'
#         },
#         'email': {
#             'level': 'INFO',
#             'class': 'django.utils.log.AdminEmailHandler',
#             'include_html': True,
#         }
#     },
#     # 日志器
#     'loggers': {
#         'django': {
#             'handlers': ['console', 'email'],
#             'level': 'INFO',
#             'propagate': True,
#         },
#     },
# }
#
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {  # 格式化
        'simple': {
            'format': '%(asctime)s %(levelname)6s - %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'console': {
            'format': '%(asctime)s %(levelname)8s - %(message)s',
            # 'format': '[%(asctime)s][%(levelname)s] %(pathname)s %(lineno)d -> %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        }
    },
    'handlers': {  # 处理器
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'console'
        },
        'fileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'log.log'),
            'when': 'midnight',
            'encoding': 'utf8'
        },
        'issuingFileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'issuing.log'),
            'when': 'midnight',
            'encoding': 'utf8'
        },
        # 'statsFileHandler': {
        #     'level': 'INFO',
        #     'class': 'logging.handlers.TimedRotatingFileHandler',
        #     'formatter': 'simple',
        #     'filename': os.path.join(BASE_DIR, 'log', 'stats.log'),
        #     'when': 'midnight',
        #     'encoding': 'utf8'
        # }
    },
    'loggers': {  # 记录器
        'django': {
            'handlers': ['fileHandler', 'console'],
            'level': 'INFO',
            'propagate': False
        },
        'issuing': {
            'handlers': ['issuingFileHandler'],
            'level': 'INFO',
            'propagate': False
        },
        # 'stats': {
        #     'handlers': ['statsFileHandler'],
        #     'level': 'INFO',
        #     'propagate': False
        # },
    }
}
