# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import protoc_pb2 as protoc__pb2


class XiaoChengXuServiceStub(object):
    """定义服务接
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.login = channel.unary_unary(
                '/MiniApp.XiaoChengXuService/login',
                request_serializer=protoc__pb2.LoginRequest.SerializeToString,
                response_deserializer=protoc__pb2.LoginResponse.FromString,
                )
        self.get_user_info = channel.unary_unary(
                '/MiniApp.XiaoChengXuService/get_user_info',
                request_serializer=protoc__pb2.GetUserInfoRequest.SerializeToString,
                response_deserializer=protoc__pb2.GetUserInfoResponse.FromString,
                )
        self.save_formid = channel.unary_unary(
                '/MiniApp.XiaoChengXuService/save_formid',
                request_serializer=protoc__pb2.SaveFormidRequest.SerializeToString,
                response_deserializer=protoc__pb2.SaveFormidResponse.FromString,
                )
        self.recv_wx_event = channel.unary_unary(
                '/MiniApp.XiaoChengXuService/recv_wx_event',
                request_serializer=protoc__pb2.RecvWxEventRequest.SerializeToString,
                response_deserializer=protoc__pb2.RecvWxEventResponse.FromString,
                )
        self.push_wx_msg = channel.unary_unary(
                '/MiniApp.XiaoChengXuService/push_wx_msg',
                request_serializer=protoc__pb2.PushWxMsgRequest.SerializeToString,
                response_deserializer=protoc__pb2.PushWxMsgResponse.FromString,
                )
        self.get_template_id = channel.unary_unary(
                '/MiniApp.XiaoChengXuService/get_template_id',
                request_serializer=protoc__pb2.GetTemplateIdRequest.SerializeToString,
                response_deserializer=protoc__pb2.GetTemplateIdResponse.FromString,
                )
        self.send_template_notice = channel.unary_unary(
                '/MiniApp.XiaoChengXuService/send_template_notice',
                request_serializer=protoc__pb2.SendMsgRequest.SerializeToString,
                response_deserializer=protoc__pb2.SendMsgResponse.FromString,
                )


class XiaoChengXuServiceServicer(object):
    """定义服务接
    """

    def login(self, request, context):
        """
        定义rpc方法
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def get_user_info(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def save_formid(self, request, context):
        """rpc send_msg(SendMsgRequest) returns (SendMsgResponse) {}
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def recv_wx_event(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def push_wx_msg(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def get_template_id(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def send_template_notice(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_XiaoChengXuServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'login': grpc.unary_unary_rpc_method_handler(
                    servicer.login,
                    request_deserializer=protoc__pb2.LoginRequest.FromString,
                    response_serializer=protoc__pb2.LoginResponse.SerializeToString,
            ),
            'get_user_info': grpc.unary_unary_rpc_method_handler(
                    servicer.get_user_info,
                    request_deserializer=protoc__pb2.GetUserInfoRequest.FromString,
                    response_serializer=protoc__pb2.GetUserInfoResponse.SerializeToString,
            ),
            'save_formid': grpc.unary_unary_rpc_method_handler(
                    servicer.save_formid,
                    request_deserializer=protoc__pb2.SaveFormidRequest.FromString,
                    response_serializer=protoc__pb2.SaveFormidResponse.SerializeToString,
            ),
            'recv_wx_event': grpc.unary_unary_rpc_method_handler(
                    servicer.recv_wx_event,
                    request_deserializer=protoc__pb2.RecvWxEventRequest.FromString,
                    response_serializer=protoc__pb2.RecvWxEventResponse.SerializeToString,
            ),
            'push_wx_msg': grpc.unary_unary_rpc_method_handler(
                    servicer.push_wx_msg,
                    request_deserializer=protoc__pb2.PushWxMsgRequest.FromString,
                    response_serializer=protoc__pb2.PushWxMsgResponse.SerializeToString,
            ),
            'get_template_id': grpc.unary_unary_rpc_method_handler(
                    servicer.get_template_id,
                    request_deserializer=protoc__pb2.GetTemplateIdRequest.FromString,
                    response_serializer=protoc__pb2.GetTemplateIdResponse.SerializeToString,
            ),
            'send_template_notice': grpc.unary_unary_rpc_method_handler(
                    servicer.send_template_notice,
                    request_deserializer=protoc__pb2.SendMsgRequest.FromString,
                    response_serializer=protoc__pb2.SendMsgResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'MiniApp.XiaoChengXuService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class XiaoChengXuService(object):
    """定义服务接
    """

    @staticmethod
    def login(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MiniApp.XiaoChengXuService/login',
            protoc__pb2.LoginRequest.SerializeToString,
            protoc__pb2.LoginResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def get_user_info(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MiniApp.XiaoChengXuService/get_user_info',
            protoc__pb2.GetUserInfoRequest.SerializeToString,
            protoc__pb2.GetUserInfoResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def save_formid(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MiniApp.XiaoChengXuService/save_formid',
            protoc__pb2.SaveFormidRequest.SerializeToString,
            protoc__pb2.SaveFormidResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def recv_wx_event(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MiniApp.XiaoChengXuService/recv_wx_event',
            protoc__pb2.RecvWxEventRequest.SerializeToString,
            protoc__pb2.RecvWxEventResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def push_wx_msg(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MiniApp.XiaoChengXuService/push_wx_msg',
            protoc__pb2.PushWxMsgRequest.SerializeToString,
            protoc__pb2.PushWxMsgResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def get_template_id(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MiniApp.XiaoChengXuService/get_template_id',
            protoc__pb2.GetTemplateIdRequest.SerializeToString,
            protoc__pb2.GetTemplateIdResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def send_template_notice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MiniApp.XiaoChengXuService/send_template_notice',
            protoc__pb2.SendMsgRequest.SerializeToString,
            protoc__pb2.SendMsgResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
