#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

import os
import django
from django.forms import model_to_dict

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.development")
django.setup()

from CollectManageApp.models import OrgNonresidentDeclare, OrgNonresidentDeclareOperate
from CollectManageApp.auto_audit_organization_declare import ConstOperateType, declare_operate_log

declare = OrgNonresidentDeclare.objects.first()
data = model_to_dict(declare)
data['name'] = '土豆小馆分馆'
declare_operate_log('db6659fa061111ec9733fa163eac5912',
                    '1964a276019d11ecaa2cfa163eac5912',
                    data)
