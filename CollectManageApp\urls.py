#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import
from django.urls import path
from rest_framework import routers
from CollectManageApp import views, views2,nonresident_other_views

router = routers.DefaultRouter()
router.register(r'organization', views.OrganizationVS)
router.register(r'car', views.CarViewSet)
router.register(r'org_nonresident_declare', views.OrgNonresidentDeclareViewS)  # 添加责任主体,小程序单位管理中单位列表和单位详情
router.register(r'another_org_nonresident_declare', views.OrgNonresidentDeclareAnotherRubbishesViewS)  # 主体已存在，注册另一种垃圾类型的数据
router.register(r'clearing_order', views.TerminalRecordViews)  # 清运记录电子联单/收运记录电子联单(只能查看已确认的清运记录)
router.register(r'terminal_order', views.CarFlowRecordViews)  # 末端联单
router.register(r'clear_record', views.ClearRecordViews)  # 清运记录
router.register(r'transport_company', views.TransportCompanyViewSet)  # add company
router.register(r'transport_company_area', views.TransportCompanyAreaViewSet)  # add company_area
router.register(r'contract', views.ContractViewSet)  # add contract
router.register(r'city_transport_company', views.CompanyViewSet)  # 公司信息台账
router.register(r'city_car', views.QuCarViewSet)  # 车辆信息台账
router.register(r'org_noresident', views.OrgNonresidentViewSet)  # 注册责任主体台账
router.register(r'org_noresident_simple', views.OrgNonresidentSimpleViewSet)  # 注册责任主体台账
router.register(r'social_unit', views.SocialUnitViewSet)  # 社会单位非居民
router.register(r'export_org_noresident', views.ExportOrgNonresidentViewSet)  # 朝阳注册责任主体台账
router.register(r'city_contract', views.CityContractViewSet)  # 合同台账
router.register(r'city_contract_agg', views.CityContractAggViewSet)  # 合同台账统计
router.register(r'city_car_record', views.CityCarRecordViews)  # 车辆收运台账
router.register(r'other_car_record', views.OtherCarRecordViews)  # 车辆收运台账
router.register(r'export_org_history_stats', views.ExportOrgHistoryStats)  # 非居民导出按月统计
router.register(r'org_rfid', views.OrgRfidViewSet)  # 非居民打标签
router.register(r'rfid_type', views.RfidTypeViewSet)  # 非居民打标签垃圾桶标签类型
router.register(r'rfid_rubbish_type', views.RubbishTypeViewSet)  # 非居民打标签垃圾类型列表
router.register(r'org_rfid_mess', views.OrgRfidViewSetList)  # 获取打签标签信息列表 清运公司使用
router.register(r'no_rfid_org', views.OrganizationRfidRVS)  # 获取未打签主体信息列表
router.register(r'no_rfid_org_new', views.OrganizationRfidNewVS)  # 获取未打签主体信息列表
router.register(r'apply_station', views.ApplyStationViewSet)  # 非居民申请桶，报修
router.register(r'apply_rfid', views.ApplyOrgRfidViewSet)  # 申请报修标签列表
router.register(r'version', views.VersionsVS)  # App版本更新接口
router.register(r'car_record_flow', views.CarRecordFlowViewSet)  # 收运记录
router.register(r'appeal', views.AppealRecodViewSet)  # 非居民申诉
router.register(r'appeal_record', views.AppealDetailRecodViewSet)  # 申诉详情记录小程序反馈结果
router.register(r'appeal_deal_record', views.AppealDealRecordViewSet)  # 申诉详情记录清运公司和区后台
router.register(r'nonresident_order', views.NonresidentOrderSet)  # 非居调度订单
router.register(r'nonresident_order_stats', views.NonresidentOrderStatSet)  # 非居调度订单git
router.register(r'nonresident_order_warning', views.NonresidentOrderWarningSet)  # 非居调度订单预警详情
router.register(r'nonresident_order_warning_stats', views.NonresidentOrderWarningStatsSet)  # 非居调度预警统计
router.register(r'out_time_warning', views.OutTimeWarningSet)  # 非居民调度预警统计
router.register(r'opinion_details', views.OpinionDetailsViewSet)  # 意见反馈
router.register(r'solid_working_order', views.SolidWorkingOrder)  # 固废工单
router.register(r'nonresident_charge', views.NonresidentChargeSet)  # 非居民收费配置
router.register(r'appointment_collection', views.AppointmentCollectionViewSet)  # 密晓分非居民预约收运
router.register(r'org_second', views.OrgSecondViewSet)  # 非居民二级用户
router.register(r'capacity_car_record', views.CapacityCarRecordViews)  # 收运容重记录
router.register(r'non_order_info', views.NonresidentPayOrderViews)  # 非居民支付账单信息
router.register(r'non_org_quota', views.OrgNonresidentQuotaViewSet)  # 非居民定额量
router.register(r'appointment_recycling', views.AppointmentRecyclingViewSet)  # 预约回收
router.register(r'pending_company_registration', views.PendingCompanyRegistrationViewSet)  # 待注册公司记录

# 开发区其他垃圾非居民注册
router.register(r'other/org_nonresident_declare', views.OtherOrgNonresidentDeclareView)  # 其他垃圾添加责任主体,小程序单位管理中单位列表和单位详情
router.register(r'other/org_noresident', views.OtherOrgNonresidentViewSet)  # 其他垃圾非居民 注册责任主体台账

#大屏根据有无数据记录显示非居民点
router.register(r'is-online', views.NonresidentIsOnline, basename='is-online')
router.register(r'nonresident-other/cleaning-point-trans-record', nonresident_other_views.NonresidentOutboundRecord)
router.register(r'nonresident-other/cleaning-point-record', nonresident_other_views.NonresidentCleaningPointRecord)
router.register(r'nonresident-other/cleaning-point-record-weight', nonresident_other_views.NonresidentCleaningPointRecordWeight)
router.register(r'nonresident-other/car-record', nonresident_other_views.NonresidentCarRecordViews)
# router.register(r'nonresident-other/car-record', nonresident_other_views.NonresidentOtherCarRecordViews)
router.register(r'nonresident-other/car-record-weight', nonresident_other_views.NonresidentCarRecordWeightViews)
router.register(r'region-code', views.CityRegionFindViewSet)


# router.register(r'no_record_nonresident', views.NoRecordNonresidentVS)  # 无收运记录的非居民台账

urlpatterns = [
    # 经营类型分类
    path('mam_type/', views.get_mam_types),
    # 非居民审核
    path('street_scope_verify/', views.street_scope_verify),  # 街道范围检测（非居民审核通过前先调该接口）
    path('org_nonresident_audit/', views.audit_nonresident_declare),
    path('org_nonresident_credit_code_query/', views.org_nonresident_credit_code_query),
    path('org_nonresident_operate_log/', views.org_nonresident_operate_log),
    # # 非居民端注册
    # path('register/', views.register),
    # 获取行政编码
    path('region/', views.RegionView.as_view()),
    # 获取责任主体类型
    path('org_type/', views.get_org_type),
    ## 获取清运公司
    # path('transport_company/', views.get_transport_company),
    # 根据二维码获取责任主体信息
    path('org_info/', views.qr_code_get_org_info),
    # 获取指定垃圾类型
    path('rubbish_type/', views.get_rubbish_type),
    # 同步更新收运合同
    path('sync_update_car_contract/', views.sync_update_car_contract),
    # 过期合同修改主体是否有合同
    path('sync_update_org_contract/', views.sync_update_org_contract),
    # 获取终端记录
    path('get_terminal_record/', views.car_num_get_data),
    # 获取主体信息
    path('org_data/', views.get_org_data),
    path('org_contract_static/', views.org_contract_static),
    # 区级各街道合同签订数统计
    path('area_contract_count/', views.area_contract_count),
    # 非居民 排放垃圾类型 主体数量
    path('nonresident-other/org-count/', nonresident_other_views.get_other_org_count),

    # 扫码
    path('scan_qrcode/', views.scan_qrcode_record),
    # 扫码提交
    path('scan_commit/', views.scan_qrcode_commit),
    # 获取容重
    path('get_size_modify_weight/', views.get_size_modify_weight),
    # 扫码流程方案2 （扣码流程）
    path('offline_scan_commit/', views.offline_scan_commit_commit),
    # 数据统计兼容手持app、小程序、pc 、的收运统计
    path('statis_data/', views.get_statis_data),
    path('statis_data_new/', views.get_statis_data_new),  # 非居民和司机端小程序清运记录统计
    # 主体清运公司
    path('org_collect_company/', views.org_collect_company),
    # 清运记录确认
    path('confirm_record/', views.confirm_record_handle),
    # 车辆收运记录查询
    path('car_record/', views.get_car_record),
    # 街道列表
    path('street_name_list/', views.street_name_list),
    #密闭式清洁站列表
    path('cleaning_point_list/', views.cleaning_point_list),
    #密闭式清洁站出站记录手动提交
    path('cleaning_point_trans_hand_selected/', views.cleaning_point_trans_hand_selected),
    path('solid_repaire_working_record/', views.solid_repaire_working_record),  # 固废司机收运端报修记录
    path('solid_repaire_working_notice/', views.solid_repaire_working_notice),  # 固废司机收运端预警提示

    path('search_org/', views.search_org_info),  # 非居民搜索责任主体
    # path('org_list/', views.get_org_list),  # 获取已审核过后的责任主体列表   # 小程序首页，非居民下拉列表
    path('org_list/', views.get_org_list_new),  # 获取已审核过后的责任主体列表   # 小程序首页，非居民下拉列表
    path('can_add_another_org_list/', views.get_can_add_another_org_list),  # 获取可以添加另一种垃圾类型的责任主体列表   # 小程序首页，非居民下拉列表
    path('confirm_other_contract/', views.confirm_other_contract),  # 小程序确认其他垃圾非居民合同
    path('other_contract_list/', views.OtherContractNewVs.as_view()),  # 小程序待确认的其他垃圾非居民合同
    path('company_list/', views.get_company_list),  # 获取清运公司列表
    path('company_area_rubbish_list/', views.CompanyAreaRubbishesVs.as_view()),  # 获取清运公司垃圾类型（区域）
    path('org_type_list/', views.get_org_type_list),  # 获取责任主体类型列表
    path('confirm_car_flow/', views.confirm_car_flow_record_handle),  # 司机确认和末端的收运记录
    path('forward_terminal_factory/', views.forward_terminal_factory_data),  # 重写末端三联单
    path('company_street/', views.company_street_edit),  # 编辑公司区域下街道
    path('deal_appeal/', views.deal_appeal),  # 非居民  申诉处理   清运公司和区审核
    path('repeat_appeal/', views.repeat_appeal),  # 非居民 再次申诉
    path('logout_reson/', views.logout_reson),  # 非居民 注销原因
    path('non_logout/', views.non_logout),  # 非居民注销
    path('cancel_logout/', views.cancel_logout),  # 区注销取消
    path('street_cancel_logout/', views.street_cancel_logout),  # 街道取消注销
    path('city_audit_non_declare/', views.city_audit_nonresident_declare),  # 市级审核区注销非居民
    path('area_audit_nonresident_declare/', views.area_audit_nonresident_declare),  # 区级审核区注销非居民

    path('audit_non_statis/', views.audit_nonresident_statis),  # 市级审核区注销非居民统计
    path('org_mapping/', views.org_mapping),  # 非居民绑定变更
    path('org_allowany/', views.org_allowany),  # 非居民扫码主体信息

    # 邀请成员
    path('employee/', views.EmployeeView.as_view()),
    # 移动端轮询车载数据接口
    path('get_car_record/', views.get_car_record_view),  # 扣码流程
    # 移动端轮询车载数据接口
    path('scan_car_record/', views.scan_car_record_view),  # 理想流程
    # 清运记录确认
    path('confirm_feedback/', views.confirm_feedback_view),

    # 清运记录 根据收运记录统计
    path('clear_record_by_car_record/', views.clear_record_by_car_record),
    path('org_sub_type_id/', views.get_sub_org_type_list),  # 获取子类责任主体列表

    # 获取排放登记编码/简码
    path('clean/<str:clean_type>/<str:clean_subtype>/', views.get_clean_code_no),
    path('export_car_count/', views.export_car_count),  # 车辆数量
    path('export_car_info/', views.export_car_info),  # 车辆详情
    path('export_company_count/', views.export_company_count),  # 公司企业类型数量
    path('export_company_info/', views.export_company_info),  # 公司详情
    path('export_org_nonresident_count/', views.export_org_nonresident_count),  # 非居民类型数量
    path('export_org_nonresident_info/', views.export_org_nonresident_info),  # 非居民类型详情

    path('rfid_org_rec/', views.rfid_org_rec_view),  # 非居民打标签主体识别
    path('org_rfid_stat/', views.org_rfid_stat),  # 获取标签统计信息
    path('org_rfid_mess_stat/', views.org_rfid_mess_stat),  # 获取打标签信息列表 区，街道，市

    # 微信
    path('get_template_id/', views.get_template_id),  # 获取打标签信息列表 区，街道，市

    path('contract/nonresident/base/', views.nonresident_base),  # 基础信息(甲方(非居民))
    path('contract/preview/', views.contract_preview),  # 合同预览
    path('handle_service_date', views.handle_service_date),  # 处理历史合同服务期限
    
    path('handle_org_contract', views.handle_org_contract),  # 处理org
    path('bill_car_factory_trend/', views.get_bill_car_factory_trend),  # 非居民垃圾收运趋势
    # path('org_register_statist/', views.org_register_statist),  # 非居民每天注册统计脚本
    path('org_nonresident_produce_data/', views.org_nonresident_produce_data),  # 产生数据的非居民个数,非居民大屏
    path('org_register_cancel_trend/', views.org_register_cancel_trend),  # 非居民注册和注销曲线图
    path('org_contract_trend/', views.org_contract_trend),  # 非居民合同曲线图
    path('org_register_trend/', views.org_register_trend),  # 非居民注册
    path('charge_statis/', views.charge_statis),  # 非居民、末端厂收费统计
    path('mam_subtype_statist/', views.mam_subtype_statist),  # 餐饮服务经营子类型统计
    path('area_org_noresident/', views.area_org_noresident),  # 四大区域非居民台账
    path('bill_statist/', views.bill_statist),  # 交易、进厂联单统计
    path('noresident_sub_type_statist/', views.noresident_sub_type_statist),  # 交易、进厂联单统计
    path('org_rfid_statist/', views.org_rfid_statist),  # 桶信息数量
    path(r'nonresident_canteen_data/', views.NonresidentCanteenDataSet.as_view()),  # 非居民集体食堂数据填报
    path(r'sub_noresident/', views.sub_noresident),  # 添加集团二级非居民用户
    path(r'delete_sub_noresident/', views.delete_sub_noresident),  # 删除集团二级非居民用户

    # 以下司机端接口
    path(r'search_real_org/', views.OrganizationSearch.as_view()),  # 司机搜索责任主体
    path(r'modify_car_record/', views.modify_car_record),  # 司机端修改收运记录
    path(r'replenish_car_record/', views.replenish_car_record),  # 司机端补桶
    path(r'abnormal_car_record/', views.abnormal_car_record),  # 司机端异常车辆收运记录
    path(r'car_repair/', views.car_repair),  # 司机端异常车辆报修
    path(r'repair_car_record/', views.repair_car_record),  # 司机端异常车辆报修记录
    path(r'working_car_record/', views.working_car_record),  # 司机端收运中收运记录
    path(r'car_driver_status/', views.car_driver_status),  # 司机端车辆状态
    path(r'deal_car_repair/', views.deal_car_repair),  # 司机端车辆报修处理
    path(r'capacity_statist/', views.capacity_statist),  # 容重环形比
    path(r'capacity_ratio/', views.capacity_ratio),  # 容重次数比
    path(r'non_org_location/', views.non_org_location),  # 定位获取非居民主体
    path(r'deletet_replenish_car_record/', views.delete_replenish_car_record),  # 删除司机端补桶数据（联单提交前删除）
    path(r'replenish_cleaning_point_record/', views.replenish_cleaning_point_record),  # 垃圾楼补桶
    path(r'modify_cleaning_point_record/', views.modify_cleaning_point_record),  # 垃圾楼修改数据
    path(r'working_cleaning_point_record/', views.working_cleaning_point_record),  # 获取收运中垃圾楼收运记录
    path(r'confirm_cleaning_point_record/', views.confirm_cleaning_point_record),  # 提交当前清运记录
    path(r'deletet_replenish_lou_record/', views.delete_replenish_lou_record),  # 删除楼补桶数据（联单提交前删除）
    path('lou_scan_qrcode/', views.lou_scan_qrcode),
    path('statis_data_lou/', views.statis_data_lou),  # 非居民和司机端小程序楼清运记录统计
    # 以上司机端接口

    path(r'non_org_pay/', views.non_org_pay),  # 非居民支付
    path(r'wx_pay_callback/', views.wx_pay_callback),  # 微信支付成功回调函数
    path(r'org_pay_type/', views.get_org_pay_type),  # 微信支付成功回调函数
    path("bill_car_org/", views.BillCarOrgView.as_view()),  # 非居民小程序支付联单清运记录
    path("bill_car_org/confirm/", views.BillCarOrgConfirmView.as_view()),  # 非居民确认小程序确认支付联单清运记录
    path('org_allowany_auth/', views.other_org_allowany),  # 非居民扫码主体信息带权限验证，只有关联的账号才能获取
    path('org_mapping_auth/', views.org_mapping_auth),  # 非居民绑定变更 只能迁移自己关联的主体
    path('export_org_non_quota/', views.export_org_nonresident_quota),  # 导入非居民定额
    path('org_quota_appeal/', views.org_quota_appeal),  # 非居民定额申诉
    path('third_nonresident_quota/', views.third_nonresident_quota),  # 三方上报非居民定额
    path('quota_appeal_record/', views.quota_appeal_record),  # 定额申诉理由记录
    path('street_quota_appeal/', views.street_quota_appeal),  # 街道定额申诉
    path('area_edit_quota/', views.area_edit_quota),  # 区编辑定额
    path('edit_quota_record/', views.edit_quota_record),  # 历史修改页面
    path('car_area_update/', views.car_area_update),  # 历史修改页面
    path('non_org_balance/', views.non_org_balance),  # 非居民余额
    path('car_record_warning/', views.CarRecordWarningView.as_view()),  # 建国门的桶，出现在别的街道,收运报警
    path('non_org_wechat/', views.non_org_wechat),  # 石景山非居民小程序迁移，非居民手机号绑定关系
    path('export_org_nonresident_wechat/', views.export_org_nonresident_wechat),  # 石景山非居民小程序迁移，非居民手机号绑定关系

    # 以下为开发区非居民使用
    path('other/org_nonresident_audit/', views.other_audit_nonresident_declare),  # 收运公司、街道 对其他垃圾非居民审核
    path('other/org_list/', views.other_get_org_list),  # 获取已审核过后的责任主体列表   # 小程序首页，非居民下拉列表
    path('other/company_list/', views.other_get_company_list),  # 获取清运公司列表
    path('other/org_info/', views.other_qr_code_get_org_info),  # 根据二维码获取责任主体信息
    # 以上为开发区非居民使用

    # 居民投放数据列表
    path("bag_breaking_record/", views2.BagBreakingViewSet.as_view({'get': 'list'})),
    # 居民投放数据统计
    path("bag_breaking_record_statistic/", views2.BagBreakingStatisticViewSet.as_view({'get': 'cal_bag_breaking_record_statistic'})),
    # 居民投放垃圾分类质量统计
    path("bag_breaking_quality/", views2.BagBreakingViewSet.as_view({'get': 'cal_quality'})),
    # 居民投放垃圾分类质量趋势统计
    path("bag_breaking_quality_trend/", views2.BagBreakingViewSet.as_view({'get': 'cal_quality_trend'})),
    # 居民投放垃圾分类质量差 按居民统计
    path("bag_breaking_resident_quality/", views2.BagBreakingViewSet.as_view({'get': 'cal_resident_quality'})),
    # 桶站报警数据
    path("trash_can_alarm_record/", views2.TrashCanAlarmViewSet.as_view({'get': 'list'})),
    # 桶站报警数据统计
    path("trash_can_alarm_record_statistic/", views2.TrashCanAlarmViewSet.as_view({'get': 'cal_trash_can_alarm_record_statistic'})),
    # 厨余垃圾油水分离装置数据
    path("separation_device_record/", views2.OilWaterSeparationDeviceViewSet.as_view({'get': 'list'})),
    # 厨余垃圾油水分离装置数据统计
    path("separation_device_record_statistic/", views2.OilWaterSeparationDeviceViewSet.as_view({'get': 'cal_separation_device_record_statistic'})),
    # 缴费数据统计
    path("non_org_pay_statistic/", views2.NonOrgPayViewSet.as_view({'get': 'cal_non_org_pay_statistic'})),
    # 参与率统计
    path("bag_breaking_partin_rate/", views2.BagBreakingViewSet.as_view({'get': "bag_breaking_partin_rate"})),
    # 同比率统计
    path("bag_breaking_year_rate/", views2.BagBreakingViewSet.as_view({'get': "bag_breaking_year_rate"})),
    # 环比率统计
    path("bag_breaking_cycle_rate/", views2.BagBreakingViewSet.as_view({'get': "bag_breaking_cycle_rate"})),
    # 图片导出
    path("bag_breaking_images_export/", views2.BagBreakingViewSet.as_view({'get': "bag_breaking_images_export"})),

    # 微信用户修改头像和昵称
    path('change_wx_info/', views.WxChangeInfo.as_view()),
    path('no_record_nonresident/', views.NoRecordNonresidentExport.as_view()),  # 无收运记录的非居民台账
    
    path('pending_company_submit_statistic/', views.PendingCompanySubmitStatisticVs.as_view()),  # 统计待注册公司提交记录
    path('import_other_org/', views.ThirdParthImportOtherOrgVs.as_view()),  # 三方导入其他垃圾主体
    path('other_org_stats/', views.OtherOrgStatsVs.as_view()),  # 其他垃圾非居民填报进度统计
    path("nonresident_warning/", views.NonresidentWarning.as_view()),  # 非居民其他垃圾预警

    path("nonresident-other/screen/standing-book-stats/", views.OtherRegisterStatsViews.as_view()),  # 台账数据
    path("nonresident-other/screen/car-running-stats/", views.CarRunningStats.as_view()),  # 车运行数据
    path("nonresident-other/screen/clean-running-stats/", views.CleanRunningStats.as_view()),  # 密闭式清洁站运行数据
    path("nonresident-other/screen/collect-org-stats/", views.CollectOrgStats.as_view()),  # 收集主体个数统计
    path("nonresident-other/screen/collect-weight-stats/", views.CollectWeightStats.as_view()),  # 收集重量统计
    path("nonresident-other/org-info/", nonresident_other_views.OrgInfo.as_view()),  # 主体详情
    path("nonresident-other/cleaning-point-info/", nonresident_other_views.CleaningPointInfo.as_view()),  # 主体详情
    path("nonresident-other/org-record-bills-all/", nonresident_other_views.OrgRecordBillsALL.as_view()),
    # 责任主体收运电子联单总数
    path("nonresident-other/org-record-bills/", nonresident_other_views.OrgRecordBills.as_view()),  # 责任主体收运电子联单总数
    path("nonresident-other/warning/car-running/", nonresident_other_views.CarWaringStats.as_view()),  # 车运行数据
    path("nonresident-other/warning/cleaning-point-running/",
         nonresident_other_views.CleaningPointWaringStats.as_view()),  # 楼运行数据

    # 顺义区主体识别模块及接口
    path('sy_nonresident_kitchen_rfid/', views.SyNonresidentKitchenRfid.as_view()),  # 顺义区非居民餐厨主体识别情况
    path('sy_nonresident_other_rfid/', views.SyNonresidentOtherRfid.as_view()),  # 顺义区非居民其他主体识别情况
    path('sy_resident_rfid/', views.SyResidentRfid.as_view()),  # 顺义区居民主体识别情况
    path('sy_nonresident_other_rfid_stats/', views.SyNonresidentOtherRfidStats.as_view()),  # 顺义区非居民其他各街道主体识别率
    path('sy_nonresident_kitchen_rfid_stats/', views.SyNonresidentkitchenRfidStats.as_view()),  # 顺义区非居民餐厨各街道主体识别率
    path('sy_resident_rfid_stats/', views.SyResidentRfidStats.as_view()),  # 顺义区非居民餐厨各街道主体识别率
    
    path('statistic/expire-contract-count/', views.expire_contract_count),  # 统计过期和临期数量
]
urlpatterns += router.urls
