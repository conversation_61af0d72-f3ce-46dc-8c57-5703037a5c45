import sys
configs = {
    'port': 8528,
    'host': '***********',
    'wx_urls': {
        'login_url': f'https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code',
        'get_access_token_url': f'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s',
        'send_message_url': f'https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?access_token=%s',
        'get_user_info_url': f'https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN',
        'send_subscribe_message_url':'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s',
    },
    # 小程序账号
    'weixin_account': {
        'luxiaofen': {
            'appid': 'appid',
            'secret': 'secret'
        },
        'ljfl': {
            'appid': 'appid',
            'secret': 'secret'
        }
    },
    'debug': True,
    'mysql': {
        'host': '***********',
        'port': 3306,
        'user': 'wxserver',
        'password': 'Weixin_server20210916',
        'db': 'weixin_server'
    },
    'mongodb': {
        'host': '127.0.0.1',
        'port': 27017
    },
    'account': {
        'host': '127.0.0.1',
        'port': 28000,
        'get_user_info_cmd': 0x1001,
        'update_user_info_cmd': 0x1002
    },
}