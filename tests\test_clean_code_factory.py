#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

import os
import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.development")
django.setup()

from CollectManageApp.models_base import FactoryLocation
from CleanCodeIssuing.clean_code_issuing import CleanCodeIssuing

issuing = CleanCodeIssuing()
factory = FactoryLocation.objects.filter(is_deleted=0).all()
for fac in factory:
    result = issuing.get_clean_code(
        clean_type='ATTACH',
        clean_sub_type='FACTORY',
        clean_id=fac.factory_location_id
    )
    print(fac.factory_location_id, result)
