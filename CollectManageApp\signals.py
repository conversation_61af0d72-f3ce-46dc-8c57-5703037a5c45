from django.dispatch import receiver
from CollectManageApp.models_base import NonResidentBase, Organization
from CollectManageApp.models_transport import ContractNew
import django.dispatch

nonresident_base_save_signal = django.dispatch.Signal(["org_id", "company_id"])


@receiver(nonresident_base_save_signal, dispatch_uid="nonresident_base_save")
def nonresident_base_save(sender, **kwargs):
    """
    保存非居民信息到合同
    """
    org_id = kwargs['org_id']
    company_id = kwargs['company_id']
    rubbishes = kwargs['rubbishes']
    nonresident = NonResidentBase.objects.filter(org_id=org_id, company_id=company_id, rubbishes=rubbishes).first()
    contract = ContractNew.objects.filter(company_id=company_id, org_id=org_id, contract_status=1, rubbishes=rubbishes).first()
    if contract:
        org = Organization.objects.filter(org_id=org_id).first()
        if org.credit_code:
            contract.credit_code = org.credit_code
        if nonresident.business_license_code:
            contract.food_license = nonresident.business_license_code
        if nonresident.unit_nature:
            contract.unit_nature = nonresident.unit_nature
        if nonresident.business_license_date:
            contract.business_license_date = nonresident.business_license_date
        if nonresident.right_obligations:
            contract.second_right_obligations = nonresident.right_obligations
        if nonresident.break_duty:
            contract.second_break_duty = nonresident.break_duty
        if nonresident.convention:
            contract.convention = nonresident.convention
        if nonresident.bank_name:
            contract.bank_name = nonresident.bank_name
        if nonresident.account_name:
            contract.account_name = nonresident.account_name
        if nonresident.account:
            contract.account = nonresident.account
        contract.save()
