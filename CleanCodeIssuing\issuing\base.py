#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from ..logger import logger

from CollectManageApp.models import CleanCodeSequence, CleanCodeNoMapping


class BaseCleanCodeGenerator:
    CLEAN_TYPE = ''
    CLEAN_SUBTYPE = ''
    CLEAN_CODE_LOCK_KEY = 'clean_code_lock'

    def _get_clean_code_sequence(self, prefix=''):
        """
        获取排放登记编码序列值 - 需要放在事务内
        :param prefix: prefix为空时生成统一编号
        :return:
        """
        clean_type, clean_subtype = self.CLEAN_TYPE, self.CLEAN_SUBTYPE
        default_value = 0
        if prefix == '':
            clean_type, clean_subtype = 'CLEAN_NO', 'CLEAN_NO'
            default_value = 10000000
        sequence = CleanCodeSequence.objects.filter(clean_type=clean_type,
                                                    clean_subtype=clean_subtype,
                                                    prefix=prefix).first()
        if not sequence:
            sequence = CleanCodeSequence()
            sequence.clean_type = clean_type
            sequence.clean_subtype = clean_subtype
            sequence.prefix = prefix
            sequence.sequence = default_value
        return sequence

    def _get_clean_code(self, clean_code_prefix, clean_id, with_no=True, code_format='04d', middle_fix=''):
        """
        生成排放登记编码/简码
        :param clean_code_prefix:
        :param clean_id:
        :param with_no: 是否自动生成简码
        :param code_format: 排放登记编码格式化
        :param middle_fix: 中间位置内容
        :return:
        """
        # 生成排放登记编码/简码
        if with_no:
            no_sequence = self._get_clean_code_sequence()
            no_sequence.sequence += 1
            no_sequence.save()
            clean_no = f'{no_sequence.sequence:08d}'
        else:
            clean_no = ''
        code_sequence = self._get_clean_code_sequence(prefix=clean_code_prefix)
        code_sequence.sequence += 1

        sequence = f'{code_sequence.sequence:{code_format}}' if code_format else ''
        clean_code = f'{clean_code_prefix}{middle_fix}{sequence}'

        # 生成凭证
        CleanCodeNoMapping.objects.create(**dict(
            clean_type=self.CLEAN_TYPE,
            clean_subtype=self.CLEAN_SUBTYPE,
            clean_id=clean_id,
            clean_code=clean_code,
            clean_no=clean_no
        ))

        code_sequence.save()
        return clean_code, clean_no
