#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import json
import datetime
import time
import uuid
from functools import wraps

from Base.api import ApiResponse, ConstCode
from Base.server import base_server


def auto_data_validate(serializer):
    def _wrapper(function):
        @wraps(function)
        def __wrapper(request, *args, **kwargs):
            serial = serializer(data=request.data)
            if not serial.is_valid():
                return ApiResponse(code=ConstCode.BadRequest,
                                   msg='提交数据有误!',
                                   errors=serial.errors)
            request.serial = serial
            return function(request, *args, **kwargs)

        return __wrapper

    return _wrapper


# @user_token_required
def region(request):
    params = request.query_params
    results = base_server.get_city_region(params=params)
    return ApiResponse(data=results)


# 生成时间戳
def make_time_stamp():
    return time.time()


# 生成uuid
def make_uuid():
    return ''.join(str(uuid.uuid4()).split('-'))
