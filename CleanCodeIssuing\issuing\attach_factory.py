#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from django.core.cache import cache
from django.db import transaction

from ..logger import logger
from .base import BaseCleanCodeGenerator

from CollectManageApp.models_base import FactoryLocation, FactoryType, CityRegion


class AttachFactoryCleanCodeGenerator(BaseCleanCodeGenerator):
    """
    排放登记编码生成器: 附-处理设施
    """
    CLEAN_TYPE = 'ATTACH'
    CLEAN_SUBTYPE = 'FACTORY'

    def generate(self, clean_id):
        # 1. 获取编码前缀
        factory = FactoryLocation.objects.filter(factory_location_id=clean_id,
                                                 is_deleted=0).first()
        if not factory:
            return dict(code=400, msg='处理设施不存在.')
        if factory.clean_code:
            return dict(code=200, msg='排放登记编码已生成.', data=dict(
                clean_code=factory.clean_code,
                clean_no=factory.clean_no,
            ))

        # 1.1 获取区位编码
        region = CityRegion.objects.filter(coding=factory.area_coding, grade=2, is_deleted=0).first()
        if not region or not region.clean_code:
            return dict(code=400, msg='获取区位编码失败.')

        # 1.2 获取收运公司编码
        factory_type = FactoryType.objects.filter(factory_id=factory.factory_type,
                                                  is_deleted=0).first()
        if not factory_type or not factory_type.clean_code:
            return dict(code=400, msg='获取处理设施编码失败.')

        clean_code_prefix = f'{region.clean_code}0000000{factory_type.clean_code}'

        # 2. 锁定编码发号器
        with cache.lock(self.CLEAN_CODE_LOCK_KEY):
            # 2.2 发号逻辑
            try:
                with transaction.atomic(using='ljfl_db'):
                    with transaction.atomic(using='ljfl_declare_db'):
                        clean_code, clean_no = self._get_clean_code(clean_code_prefix, clean_id)

                        # 修改正式库
                        FactoryLocation.objects.filter(factory_location_id=clean_id,
                                                       is_deleted=0) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))

            except Exception as e:
                logger.exception(e)
                return dict(code=500, msg='排放登记编码发放失败.')

        return dict(code=200, msg='排放登记编码发放成功.', data=dict(
            clean_code=clean_code,
            clean_no=clean_no
        ))
