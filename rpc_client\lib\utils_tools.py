#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import
import os
import sys
import xmltodict
import json
import uuid
import time
import datetime


class Dict(dict):
    '''
    Simple dict but support access as x.y style.
    '''

    def __init__(self, names=(), values=(), **kw):
        super(Dict, self).__init__(**kw)
        for k, v in zip(names, values):
            self[k] = v

    def __getattr__(self, key):
        try:
            return self[key]
        except KeyError:
            raise AttributeError(r"'Dict' object has no attribute '%s'" % key)

    def __setattr__(self, key, value):
        self[key] = value


def merge(defaults, override):
    r = {}
    for k, v in defaults.items():
        if k in override:
            if isinstance(v, dict):
                r[k] = merge(v, override[k])
            else:
                r[k] = override[k]
        else:
            r[k] = v
    return r


def toDict(d):
    D = Dict()
    for k, v in d.items():
        D[k] = toDict(v) if isinstance(v, dict) else v
    return D


# def xml_to_json(xml_str):
#     # parse是的xml解析器
#     xml_parse = xmltodict.parse(xml_str, process_namespaces=True)
#     # json库dumps()是将dict转化成json格式,loads()是将json转化成dict格式。
#     # dumps()方法的ident=1,格式化json
#     json_str = json.dumps(xml_parse, indent=1)
#     return json_str


def json_to_xml(json_str):
    # 参数pretty是格式化xml
    xml_str = xmltodict.unparse(json_str, pretty=1)
    return xml_str


def get_uuid():
    return str(''.uuid.uuid1().split('-'))


def timestamp2date(timestamp):
    if not timestamp:
        timestamp = int(time.time())
        # 转换为其他日期格式,如:"%Y-%m-%d %H:%M:%S"
    timeArray = time.localtime(int(timestamp))
    date = time.strftime("%Y-%m-%d", timeArray)
    return date


if __name__ == '__main__':
    timestamp = 1631860552
    print(timestamp2date(timestamp))
