#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from enum import IntEnum


class ConstEnumBase:
    """枚举基类"""
    __map__ = {}

    __map_reversed__ = None

    @classmethod
    def get_name(cls, _type):
        return cls.__map__.get(_type, '')

    @classmethod
    def get_value(cls, name):
        if not cls.__map_reversed__:
            cls.__map_reversed__ = {value: key for key, value in cls.__map__.items()}
        return cls.__map_reversed__.get(name, 0)


class ConstSection:
    """阶段"""
    # 投放
    THROW = 'tf'
    # 收运
    TRANSPORT = 'sy'
    # 收集
    COLLECT = 'sj'
    # 运输
    CAE_TRANSPORT = 'ys'


class ConstDuration:
    """统计时间区间"""
    DAILY = 'daily'
    WEEKLY = 'weekly'
    MONTHLY = 'monthly'
    QUARTERLY = 'quarterly'
    YEARLY = 'yearly'
    HISTORY = ''


class ConstTable:
    """
    Temp记录表名称
    """
    RESIDENT_RECORD = 'resident_record'
    CAR_RECORD = 'car_record'
    CLEANING_POINT_RECORD = 'cleaning_point_record'


# todo 责任主体类型和集合名对应关系
class ConstOrgType:
    """
        责任主体类型
    """
    CITY_TYPE = "037b3e5cbf5411eaa8a9000c29d3cc31"
    VILLAGE_TYPE = "7ae4d984bf5511eaa8a9000c29d3cc31"
    OFFICE_TYPE = "d74a392ec02911eaa8a9000c29d3cc31"
    PUBLIC_TYPE = "d7ad2520c02911eaa8a9000c29d3cc31"
    MANAGEMENT_TYPE = "d7e15818c02911eaa8a9000c29d3cc31"
    PARK_TYPE = "d82f168ec02911eaa8a9000c29d3cc31"
    FACILITIES_TYPE = "d855cb26c02911eaa8a9000c29d3cc31"
    TRAFFICE_TYPE = "d88a2592c02911eaa8a9000c29d3cc31"
    CONSTRUCTION_TYPE = "d8c564c2c02911eaa8a9000c29d3cc31"
    OTHER_TYPE = "d8e27648c02911eaa8a9000c29d3cc31"
    ORG_TYPE_LIST = ["037b3e5cbf5411eaa8a9000c29d3cc31", "7ae4d984bf5511eaa8a9000c29d3cc31",
                     "d74a392ec02911eaa8a9000c29d3cc31",
                     "d7ad2520c02911eaa8a9000c29d3cc31", "d7e15818c02911eaa8a9000c29d3cc31",
                     "d82f168ec02911eaa8a9000c29d3cc31",
                     "d855cb26c02911eaa8a9000c29d3cc31", "d88a2592c02911eaa8a9000c29d3cc31",
                     "d8c564c2c02911eaa8a9000c29d3cc31",
                     "d8e27648c02911eaa8a9000c29d3cc31"]


class ConstTime:
    """统计车辆收运时间范围"""
    DAILY = 31


class ConstRubbishType:
    """
        垃圾类型
    """
    CHUYU = "25a7185abf5611eaa8a9000c29d3cc31"
    KITCHEN = "b84b760ec02a11eaa8a9000c29d3cc31"
    OTHER = "b8c900bac02a11eaa8a9000c29d3cc31"
    RECYCLABLE = "b88403d4c02a11eaa8a9000c29d3cc31"
    HARMFUL = "b9e7b14ec02a11eaa8a9000c29d3cc31"
    STOOL = "8ab7c988e35011eab60bfa163ed3b670"
    LARGE = 'b90018f2c02a11eaa8a9000c29d3cc31'

    RubbishTypeList = ["25a7185abf5611eaa8a9000c29d3cc31", "b84b760ec02a11eaa8a9000c29d3cc31",
                       "b8c900bac02a11eaa8a9000c29d3cc31",
                       "b88403d4c02a11eaa8a9000c29d3cc31", "b9e7b14ec02a11eaa8a9000c29d3cc31"]
    RubbishTypeListNew = ["25a7185abf5611eaa8a9000c29d3cc31", "b84b760ec02a11eaa8a9000c29d3cc31",
                          "b8c900bac02a11eaa8a9000c29d3cc31",
                          "b88403d4c02a11eaa8a9000c29d3cc31", "b9e7b14ec02a11eaa8a9000c29d3cc31",
                          'b90018f2c02a11eaa8a9000c29d3cc31']
    rubbish_dict = {
        'chuyu': "25a7185abf5611eaa8a9000c29d3cc31",
        'other': "b8c900bac02a11eaa8a9000c29d3cc31",
        'recyclable': "b88403d4c02a11eaa8a9000c29d3cc31",
        'harmful': "b9e7b14ec02a11eaa8a9000c29d3cc31",
        'canchu': 'b84b760ec02a11eaa8a9000c29d3cc31'
    }


class ConstDeclareType:
    """
    非居民申报类型
    """
    Normal = 'Normal'
    NoCredit = 'NoCredit'
    RepeatCredit = 'RepeatCredit'
    ChildOrg = 'ChildOrg'


class ConstWeightType(ConstEnumBase):
    """
    重量来源
    """
    CAR = 'CAR'
    WEIGHT = 'WEIGHT'
    CAPACITY = 'CAPACITY'

    __map__ = {
        CAR: '称重',
        WEIGHT: '称重',
        CAPACITY: '容重',
    }


class NonresidentRubbishes:
    """非居民类型"""
    RESTAURANTS = 'RESTAURANTS'
    OTHER = 'OTHER'
    BOTH_NONRESIDENT_RUBBISHES = f'{RESTAURANTS},{OTHER}'
    ALL = 'ALL'


# 两种非居民类型都有
BOTH_NONRESIDENT_RUBBISHES = f'{NonresidentRubbishes.RESTAURANTS},{NonresidentRubbishes.OTHER}'

# 垃圾类型名称
NONRESIDENT_RUBBISHES_NAME = {
    NonresidentRubbishes.RESTAURANTS: '餐厨垃圾',
    NonresidentRubbishes.OTHER: '其他垃圾',
    NonresidentRubbishes.BOTH_NONRESIDENT_RUBBISHES: '餐厨垃圾,其他垃圾',
}
WARNING_DAY_MAP = {
    "10": 1,  # 1天
    "20": 3,  # 3天
    "30": 5,  # 5天
    "40": 7,  # 7天
    "50": 15,  # 15天
    "100": 10000  # 全部
}


class ContractStatus(IntEnum):
    NORMAL = 1
    EXPIRED = 2
    VOID = 3
    EXTENDED = 4

    @classmethod
    def label(cls, status):
        return {
            cls.NORMAL: '正常',
            cls.EXPIRED: '过期',
            cls.VOID: '作废',
            cls.EXTENDED: '顺延'
        }.get(status, '')


class RfidTypeIdEnum:
    """标签类型"""
    CHUYU = "d7904dfa17364eccb4db652116869dd0"  # 厨余垃圾桶
    KITCHEN = "9991aedf2c36452b97e89e16ab49e096"  # 餐厨垃圾桶
    OTHER = "73db639d03bc4e5e9ff7b3c1654a8167"  # 其他垃圾桶
    RECYCLABLE = "ea687db594c04adead315b6656fb2d1a"  # 可回收物桶
    HARMFUL = "qt234dbb9e2948248ad91062c9f10012"  # 有害垃圾桶
    CAR = "48198cc8dc9b4a26ab3161a5e3c496d2"  # 垃圾收运小车
