#!/usr/bin/python
# coding: utf-8
import ipaddress
import json
import logging
from rest_framework import exceptions
from django.conf import settings
from gmssl import func
from django.db.models import QuerySet, Q
from django.http import QueryDict, HttpResponse
from django.utils.deprecation import MiddlewareMixin
import requests
from rest_framework.permissions import BasePermission
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.utils.serializer_helpers import ReturnDict
from ztbory_django_shield.base.core import cryptor_shield
from django.core.serializers.json import DjangoJSONEncoder
from django.utils.translation import gettext_lazy as _
from Base.api import ConstCode
from Base.server import auth_server
from CollectManageApp.models import AuthorizedOrg
from CollectManageApp.models_base import TransportCompany
from CollectManageApp.models_transport import TransportUser
from Lic.licenseholder import lic


def get_logger(logger_name='django'):
    """
    获取日志句柄
    :param logger_name:
    :return:
    """
    return logging.getLogger(logger_name)


logger = get_logger()


class ApiMiddleWare(MiddlewareMixin):
    """Api中间件"""

    @staticmethod
    def process_request(request):
        ok, exc = lic.verify()
        if not ok:
            response = HttpResponse(content='{"code": %s, "msg": "%s"}' % (401, str(exc)),
                                    content_type='application/json',
                                    status=401)
            return response

        content_type = request.content_type
        logger.info('[URL] url=%s', request.get_full_path())
        auth_token = request.META.get('HTTP_AUTHORIZATION')
        logger.info('[TOKEN] token=%s', auth_token)
        if content_type == 'application/x-www-form-urlencoded':
            data = QueryDict(request.body)
            logger.info('[REQUEST] Form=%s', json.dumps(data))
        elif content_type == 'application/json':
            data = request.body.decode('utf-8')
            logger.info('[REQUEST] Body=%s', json.loads(data) if data else '')

    def process_exception(self, request, exception):
        error_logger = get_logger('errorLogger')
        from Base.server import AuthServerError, BaseServerError, WeChatServeError
        if isinstance(exception, (AuthServerError, BaseServerError, WeChatServeError)):
            http_status_code, code, msg = exception.code, exception.code, exception.message
            error_logger.info('CODE:%s MESSAGE:%s', code, msg)
        else:
            error_logger.exception(exception)
            logger.exception(exception)
            http_status_code, code, msg = ConstCode.ServerError

        response = HttpResponse(content='{"code": %s, "msg": "%s"}' % (code, msg),
                                content_type='application/json',
                                status=http_status_code)
        return response


class ApiJsonRender(JSONRenderer):
    """rest-framework 自带方法结果调整"""

    def render(self, data, accepted_media_type=None, renderer_context=None):
        sm2_cryptor = cryptor_shield.cryptor
        if (not data or 'code' not in data) and renderer_context:
            status_code = renderer_context.get('response').status_code
            status_text = ConstCode.get_default_msg(status_code)
            key = 'data'
            if status_code >= 300:
                key = ''
                if isinstance(data, ReturnDict):
                    key = 'errors'

            ret = {
                'msg': status_text,
                'code': status_code,
            }
            if key and (data or data == [] or isinstance(data, QuerySet)):
                ret[key] = data
            result = ret
        else:
            result = data
        
        logger.info('[RESPONSE] %s', result)

        request = renderer_context.get("request")
        if request and request.path in ["/v1/auth/live_area/login/"]:
            sign_content = json.dumps(result, sort_keys=True, separators=(",", ":"), ensure_ascii=False, cls=DjangoJSONEncoder)
            signature = sm2_cryptor.sign(sign_content.encode("utf-8"), func.random_hex(sm2_cryptor.para_len))
            result["signature"] = signature

        return super().render(result, accepted_media_type, renderer_context)


# 权限判定
def _permission(request):
    if '/v1/auth' in request.path:
        return True

    request.is_interface = False
    if request.GET.get('source') == 'interface':
        request.is_interface = True
        return True

    auth_token = request.META.get('HTTP_AUTHORIZATION')
    if auth_token:
        auth_token = auth_token.split(' ')[-1]
    info = auth_server.info(auth_token, raise_exception=False)
    if info is None:
        return False

    manager_data = info.get('data').get('manager')
    role = manager_data.get('role')
    org_id = request.GET.get('org_id')

    if role not in [
        'SocialOrganization',
        'StreetManager',
        'Qualification',
        'AreaManager',
        'CityManager',
        'Platform',
        'QualificationDriver',
        'TransportDriver',
        'CommManager',
        'CleaningPointCollectorManager',
        'TransferPointManager'
    ]:
        return False

    if role == 'SocialOrganization':
        authorized = AuthorizedOrg.objects.filter(Q(relation_id=manager_data.get('relation_id')) |
                                                  Q(username=manager_data.get('username'))).first()
        if not authorized:
            return False
        if authorized.relation_id != manager_data.get('relation_id'):
            authorized.relation_id = manager_data.get('relation_id')
            authorized.save()
        request.authorized_org = authorized

        # # 如果传org_id， 判断是否可管理该主体
        # if org_id:
        #     mapping = AuthorizedOrgRelationId.objects.filter(authorized_org_relation_id=manager_data.get('relation_id'),
        #                                                      org_id=org_id).first()
        #     if not mapping:
        #         return False

    request.token = auth_token
    if manager_data.get('role') == 'Platform':
        manager_data['role'] = 'CityManager'
    request.manager = manager_data

    return True


def _permission_account_(request):
    """机构注册中心授权服务

    对接角色： 市、区
    """
    auth_token = request.META.get('HTTP_AUTHORIZATION')
    if auth_token:
        auth_token = auth_token.split(' ')[-1]
    url = settings.ACCOUNT_SERVER_URL + "/auth/userinfo"
    headers = {"Authorization": f"Bearer {auth_token}"}
    response = requests.get(url, headers=headers, timeout=5)
    if not response.ok:
        return False
    user = response.json()
    if not user:
        return False
    institution = user.get("institution", {})

    # 对接区
    if institution.get("admin_level") == 20:
        role = "AreaManager"
        area_relation_id = user.get("uid")
        area_coding = institution.get("uid")
        area_username = user.get("username")

        request.manager = dict(
            role=role,
            relation_id=area_relation_id,
            area_coding=area_coding,
            street_coding="",
            comm_coding="",
            username=area_username,
        )
        return True
    # 对接街道
    elif institution.get("admin_level") == 30:
        role = "StreetManager"
        street_relation_id = user.get("uid")
        street_coding = institution.get("uid")
        area_coding = street_coding[:6] + "000000"
        street_username = user.get("username")

        request.manager = dict(
            role=role,
            relation_id=street_relation_id,
            area_coding=area_coding,
            street_coding=street_coding,
            comm_coding="",
            username=street_username,
        )
        return True
    # 目前对接收集企业, 统一中心 和 清运单位账号映射绑定
    if institution.get("company_type") not in [40]:
        return False

    company = TransportCompany.objects.filter(transport_company_id=institution.get("uid")).first()
    if not company:
        return False

    t_user = TransportUser.objects.filter(company_uid=company.transport_company_id, user_status=1).first()
    if not t_user:
        return False
    user["username"] = t_user.username

    role = "Qualification"

    request.manager = dict(
        role=role,
        relation_id=user.get("uid"),
        area_coding="",
        street_coding="",
        comm_coding="",
        username=user.get("username"),
        extra_configure=dict(
            transport_company_id=company.transport_company_id
        )
    )
    return True


class MyPermission(BasePermission):
    def has_permission(self, request, view):
        ok, exc = lic.verify()
        if not ok:
            return False
        # 从token 中获取用户的角色信息
        has_permission = False
        if _permission(request):
            has_permission = True
        # 对接统一认证平台
        # elif hasattr(settings, "ACCOUNT_SERVER_URL") and _permission_account_(request):
        #     has_permission = True

        return has_permission


def get_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


class IsInternalNetworkOrAuthenticated(BasePermission):
    """
    允许内网IP免认证访问，外网请求需认证
    """

    def has_permission(self, request, view):

        try:
            _permission(request)
        except:
            pass

        if request.user and request.user.is_authenticated:
            check_coding(request, request.manager)
            return True

        if hasattr(request, 'manager'):
            return True

        if settings.INTERNAL_NETWORKS:
            client_ip = get_ip(request)
            logger.info(f"Client IP: {client_ip}")
            logger.info(f"HTTP_X_FORWARDED_FOR：{request.META.get('HTTP_X_FORWARDED_FOR')}")
            logger.info(f"REMOTE_ADDR：{request.META.get('REMOTE_ADDR')}")
            # 检查IP是否属于内网范围
            try:
                client_ip_obj = ipaddress.ip_address(client_ip)
                for network in settings.INTERNAL_NETWORKS:
                    if client_ip_obj in ipaddress.ip_network(network):
                        return True
            except ValueError as e:
                logger.warning(f"Invalid IP or network: {e}")
                return False
            return False
        return True


class CheckCodingPermission(BasePermission):
    """
    允许内网IP免认证访问，外网请求需认证
    """

    def has_permission(self, request, view):
        if request.manager:
            check_coding(request, request.manager)

        return True


def check_coding(request, manager):
    """区域编码权限验证"""

    # TODO: 增加其它类型角色校验
    get = request.GET.copy()
    query_params = request.GET.copy()
    if manager.get("role") == "AreaManager":
        if 'area_coding' in get and get['area_coding']:
            if get['area_coding'] != manager.get("area_coding"):
                raise exceptions.PermissionDenied(_('您没有权限访问该数据'))
        else:
            get['area_coding'] = manager.get("area_coding")
            query_params['area_coding'] = manager.get("area_coding")
    elif manager.get("role") == "StreetManager":
        if 'area_coding' in get and get['area_coding']:
            if not get['area_coding'].startswith(manager.get("street_coding")[0:6]):
                raise exceptions.PermissionDenied(_('您没有权限访问该数据'))
        else:
            get['area_coding'] = manager.get("street_coding")[0:6] + "000000"
            query_params['area_coding'] = manager.get("street_coding")[0:6] + "000000"
        if 'street_coding' in get and get['street_coding']:
            if get['street_coding'] != manager.get("street_coding"):
                raise exceptions.PermissionDenied(_('您没有权限访问该数据'))
        else:
            get['street_coding'] = manager.get("street_coding")
            query_params['street_coding'] = manager.get("street_coding")
    request._request.GET = query_params
    request.GET = get
