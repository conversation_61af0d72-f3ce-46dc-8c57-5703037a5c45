"""导入三方的其他垃圾"""
import io
import time
import uuid

import openpyxl
import requests
from dataclasses import asdict, dataclass
try:
    from PIL import Image as PILImage
except ImportError:
    PILImage = False

from openpyxl.styles import Color, Font
from openpyxl.workbook import Workbook
from openpyxl.worksheet.worksheet import Worksheet

from Base.api import get_logger
from Base.utils.const import ConstDeclareType, NonresidentRubbishes
from Base.utils.tools import gcj02_to_bd09, gcj02towgs84
from CollectManageApp.auto_audit_organization_declare import declare_data_verify
from CollectManageApp.import_scripts.import_other.openpyxl_image_loader import MySheetImageLoader
from CollectManageApp.models import OrgNonresidentDeclare
from CollectManageApp.models_base import CityRegion, OrgDetail, OrgType, Organization, TransportCompany, TransportCompanyArea
from CollectManageApp.scripts import get_declare_data_from_org_table
from CollectManageApp.serializers import OrgNonresidentDeclareSer


logger = get_logger()

class ImportException(Exception):
    pass



def _import_image(img):
    if not PILImage:
        raise ImportError('You must install Pillow to fetch image objects')

    if not isinstance(img, PILImage.Image):
        img = PILImage.open(img)
    return img

def get_sheet_image_bytes(image):
    # image._data()函数会关闭文件导致无法导出，这里重写，不关闭文件
    img = _import_image(image.ref)
    # don't convert these file formats
    if image.format in ['gif', 'jpeg', 'png']:
        img.fp.seek(0)
        fp = img.fp
    else:
        fp = io.BytesIO()
        img.save(fp, format="png")
        fp.seek(0)
    
    data = fp.read()
    return data


def upload_img(img):
    url = 'https://api.ztbory.com/v3/upload/images'
    data = {
        'mkdir': 'collectmanage'
    }
    files = {
        'files': (f'图片.{img.format.lower()}', get_sheet_image_bytes(img))
    }
    response = requests.post(url, data=data, files=files)
    if response.ok:
        res_data = response.json()
        return res_data['data']
    else:
        logger.error('导入文件上传图片出错')
        logger.error(str(response.text))
        return None



@dataclass
class NewData:
    """新数据"""
    area_name: str
    street_name: str
    comm_name: str
    # 门头名称
    name: str
    # 管理责任人
    liabler: str
    # 联系人
    contacts: str
    # 联系电话
    phone: str
    
    # 单位名称
    official_org_name: str
    # 单位类型
    org_sub_type_name: str
    # 单位注册地址
    official_address: str
    # 单位详细地址
    address: str
    # 经度
    longitude: float
    # 纬度
    latitude: float
    # 统一社会信用代码
    credit_code: str
    # 营业面积(m²)
    floor_area: float
    # 日均人流量
    loating: float
    # 门头照片
    cover: bytes
    # 收运公司
    transport_company_name: str
    # 120L容器数量
    restaurant_trash_120: int
    # 240L容器数量
    restaurant_trash_240: int
    # 预估量
    restaurant_predict_weight: int
    
    area_coding: str = ''
    street_coding: str = ''
    comm_coding: str = ''
    org_sub_type_id: str = ''
    transport_company_id: str = ''
    
    
    def to_dict(self):
        dict_data = asdict(self)
        return dict_data


@dataclass
class AnotherData:
    """补充其他垃圾数据"""
    org_id: str
    # 收运公司
    transport_company_name: str
    # 120L容器数量
    restaurant_trash_120: int
    # 240L容器数量
    restaurant_trash_240: int
    # 预估量
    restaurant_predict_weight: int
    
    transport_company_id: str = ''
    
    def to_dict(self):
        dict_data = asdict(self)
        return dict_data


class ImportThirdOther:
    third_creator = [
        # 110111000000  房山区
        '太和世纪环保三方',
        # 110114000000  昌平区
        '昌平联运三方',
        # 110102000000  西城区
        '创亿新世纪',
        # 110108000000  海淀区
        '海淀三方',
        # 110107000000  石景山区
        '石景山管委',
        # 110108000000  海淀区
        '昌平联运三方',
        # 110114000000  昌平区
        '海淀三方',
    ]
    serializer_class = OrgNonresidentDeclareSer
    
    
    def __init__(self, path, creator, name=None, save_error_file=False):
        if creator not in self.third_creator:
            raise ImportException('三方不存在')
        self.path = path
        self.creator = creator
        
        
        self.save_error_file = save_error_file
        if not name and isinstance(path, str):
            name, exp = path.rsplit('.', 1)
        else:
            name, exp = name.rsplit('.', 1)
        self.error_name = f'{name}_error_{int(time.time())}.{exp}'
        
        self.excel = None
        self.sheet = None
        
        self._cache_city_region = {}
        self.has_error = False
    
    
    def read_excel_and_save_line(self):
        """保存每行数据"""
        excel: Workbook = openpyxl.load_workbook(self.path)
        
        sheet: Worksheet = excel.active
        self.excel = excel
        self.sheet = sheet
        red_font = Font(name='Arial', size=10, italic=False, bold=False, color=Color(rgb='00FF0000'))
        
        org_type = OrgType.objects.filter(is_deleted=0, parent_id='60b5ef4bef5311ebbe73fa163e3babe8').values_list('org_type_id', 'name')
        org_type_mapping = {n: o for o, n in org_type}
        
        
        data_list = []
        has_error = False
        for row, line in enumerate(sheet.iter_rows(min_row=2, values_only=True), 2):
            error_msgs = []
            change_type = line[1]
            if change_type == '新增':
                data = self.get_new_data(line)
                data, error_msgs = self.format_new_data(data, org_type_mapping, error_msgs)
                if not error_msgs:
                    dict_data = data.to_dict()
                    dict_data['creator'] = self.creator
                    dict_data['rubbishes'] = NonresidentRubbishes.OTHER
                    verify = declare_data_verify(dict_data)
                    if verify:
                        error_msgs.append(verify['msg'])
                    else:
                        data_list.append(['新增', dict_data])
                        self.save_data(dict_data)
                        continue
            elif change_type == '补充':
                data = self.get_another_data(line)
                data, error_msgs = self.format_another_data(data, error_msgs)
                
                error_msgs = self.check_another(data.org_id, error_msgs)
                if not error_msgs:
                    dict_data = data.to_dict()
                    # 获取已经审核通过的数据
                    old_data = self.get_another_type_db_data(data.org_id, NonresidentRubbishes.RESTAURANTS)
                    old_data.update(dict_data)
                    dict_data = old_data
                    dict_data['creator'] = self.creator
                    dict_data['rubbishes'] = NonresidentRubbishes.OTHER
                    dict_data['add_another_type'] = 1
                    verify = declare_data_verify(dict_data)
                    if verify:
                        error_msgs.append(verify['msg'])
                    else:
                        self.save_data(dict_data)
                        continue
            else:
                has_error = True
                sheet.cell(row, 24, value='添加类型错误').font = red_font
                continue
            
            if error_msgs:
                has_error = True
                sheet.cell(row, 24, value='\n'.join(error_msgs)).font = red_font
                continue
        if self.save_error_file:
            self.excel.save(self.error_name)
    
    
    
    def read_excel_all_and_save(self):
        """读取完所有数据再保存"""
        data_list = self.read_excel_data()
        if self.has_error:
            if self.save_error_file:
                self.excel.save(self.error_name)
        else:
            # return
            self.save_data_list(data_list)
    
    
    def read_excel_data(self):
        excel: Workbook = openpyxl.load_workbook(self.path)
        
        sheet: Worksheet = excel.active
        self.excel = excel
        self.sheet = sheet
        image_loader = MySheetImageLoader(sheet)
        red_font = Font(name='Arial', size=10, italic=False, bold=False, color=Color(rgb='00FF0000'))
        
        org_type = OrgType.objects.filter(is_deleted=0, parent_id='60b5ef4bef5311ebbe73fa163e3babe8').values_list('org_type_id', 'name')
        org_type_mapping = {n: o for o, n in org_type}
        
        data_list = []
        has_error = False
        for row, line in enumerate(sheet.iter_rows(min_row=2, values_only=True), 2):
            logger.info(f'导入数据{row}行：{line}')
            error_msgs = []
            change_type = line[1]
            if change_type == '新增':
                data = self.get_new_data(line)
                
                data, error_msgs = self.format_new_data(data, org_type_mapping, error_msgs)
                if not error_msgs:
                    if not data.cover:
                        # 获取图片文件
                        image_cell = f'S{row}'
                        if image_loader.image_in(image_cell):
                            image = image_loader.get(image_cell)
                            cover = upload_img(image)
                            if not cover:
                                error_msgs.append('上传图片失败')
                                continue
                            data.cover = cover
                        else:
                            error_msgs.append('缺少图片')
                            continue
                    dict_data = data.to_dict()
                    dict_data['creator'] = self.creator
                    dict_data['is_reduce_device'] = 0
                    dict_data['is_local_device'] = 0
                    dict_data['rubbishes'] = NonresidentRubbishes.OTHER
                    verify = declare_data_verify(dict_data)
                    if verify:
                        error_msgs.append(verify['msg'])
                    else:
                        data_list.append(dict_data)
                        # self.save_data(dict_data)
                        continue
            elif change_type == '补充':
                data = self.get_another_data(line)
                data, error_msgs = self.format_another_data(data, error_msgs)
                
                error_msgs = self.check_another(data.org_id, error_msgs)
                if not error_msgs:
                    dict_data = data.to_dict()
                    # 获取已经审核通过的数据
                    old_data = self.get_another_type_db_data(data.org_id, NonresidentRubbishes.RESTAURANTS)
                    old_data.update(dict_data)
                    dict_data = old_data
                    dict_data['creator'] = self.creator
                    dict_data['rubbishes'] = NonresidentRubbishes.OTHER
                    dict_data['add_another_type'] = 1
                    verify = declare_data_verify(dict_data)
                    if verify:
                        error_msgs.append(verify['msg'])
                    else:
                        data_list.append(dict_data)
                        # self.save_data(dict_data)
                        continue
            else:
                has_error = True
                sheet.cell(row, 24, value='添加类型错误').font = red_font
                continue
            
            if error_msgs:
                has_error = True
                sheet.cell(row, 24, value='\n'.join(error_msgs)).font = red_font
                continue
        self.has_error = has_error
        return data_list
    
    
    def save_data_list(self, data_list):
        
        for data in data_list:
            self.save_data(data)
    
    
    def check_another(self, org_id, error_msg):
        if OrgNonresidentDeclare.objects.filter(
                is_status_succeed=1,
                check_type=1,
                status=1,
                rubbishes=NonresidentRubbishes.RESTAURANTS,
                org_id=org_id
        ).exists():
            error_msg.append('主体数据正在审核中，请通过审核之后再补充排放垃圾类型')
        return error_msg
    
    
    def get_new_data(self, line):
        """新添加数据"""
        org_id, _, *datas = line
        return NewData(*datas[:21])
    
    
    def get_another_data(self, line):
        """获取补充数据"""
        
        org_id = line[0]
        # 收运公司
        transport_company_name = line[19]
        # 120L容器数量
        restaurant_trash_120 = line[20]
        # 240L容器数量
        restaurant_trash_240 = line[21]
        # 预估量
        restaurant_predict_weight = line[22]
        
        return AnotherData(
            org_id,
            transport_company_name,
            restaurant_trash_120,
            restaurant_trash_240,
            restaurant_predict_weight
        )
    
    
    def format_new_data(self, data: NewData, org_type_mapping: dict, error_msgs=None):
        """新数据"""
        area_coding = self.get_coding(data.area_name)
        if area_coding:
            data.area_coding = area_coding
            street_coding = self.get_coding(data.street_name, parent_coding=area_coding, grade=3)
            if street_coding:
                data.street_coding = street_coding
                
                comm_coding = self.get_coding(data.comm_name, parent_coding=street_coding, grade=4)
                if comm_coding:
                    data.comm_coding = comm_coding
                else:
                    if '居委会' in data.comm_name:
                        new_name = data.comm_name.replace('居委会', '')
                        comm_coding = self.get_coding(new_name, parent_coding=street_coding, grade=4)
                        if comm_coding:
                            data.comm_coding = comm_coding
                        else:
                            error_msgs.append('社区名称不存在')
            else:
                error_msgs.append('街道名称不存在')
            company_id, error_msgs = self.get_company(data.transport_company_name, area_coding, error_msgs)
            if company_id:
                data.transport_company_id = company_id
            org_sub_type_id = org_type_mapping.get(data.org_sub_type_name)
            if org_sub_type_id:
                data.org_sub_type_id = org_sub_type_id
            else:
                error_msgs.append('单位类型不存在')
            return data, error_msgs
        else:
            error_msgs.append('区名称不存在')
            return data, error_msgs
    
    
    def format_another_data(self, data: AnotherData, error_msgs=None):
        """补充数据"""
        if not data.org_id:
            error_msgs.append('缺少参数org_id')
            return data, error_msgs
        else:
            area_coding = Organization.objects.filter(
                is_declare=1, is_deleted=0, org_id=data.org_id, logout_status=0
            ).values_list('area_coding', flat=True).first()
            if not area_coding:
                error_msgs.append('org_id不存在')
                return data, error_msgs
            company_id, error_msgs = self.get_company(data.transport_company_name, area_coding, error_msgs)
            if company_id:
                data.transport_company_id = company_id
            return data, error_msgs
    
    
    def get_company(self, company_name, area_coding, error_msgs):
        company_id = TransportCompany.objects.filter(
            company=company_name, is_deleted=0, is_declare=1
        ).values_list('transport_company_id', flat=True).first()
        if company_id:
            other_area_company = TransportCompanyArea.objects.filter(
                transport_company_id=company_id,
                area_coding=area_coding,
                is_deleted=0,
                rubbishes=NonresidentRubbishes.OTHER
            )
            if not other_area_company.exists():
                error_msgs.append('清运公司服务区域不正确')
        else:
            error_msgs.append('收运公司不存在')
        return company_id, error_msgs
    
    
    def get_coding(self, name, parent_coding='', grade=2):
        key = parent_coding + name
        coding = self._cache_city_region.get(key)
        if coding:
            return coding
        
        coding_queryset = CityRegion.objects.filter(is_deleted=0, name=name, grade=grade)
        if parent_coding and grade != 2:
            # 街道和社区需要判断父级coding
            like_coding = parent_coding[:(grade - 1) * 3]
            
            coding_queryset = coding_queryset.filter(coding__icontains=like_coding)
        coding = coding_queryset.values_list('coding', flat=True).first()
        if coding:
            coding = str(coding)
            self._cache_city_region[key] = coding
            return coding
        else:
            return ''
    
    
    def get_another_type_db_data(self, org_id, other_rubbishes=NonresidentRubbishes.RESTAURANTS):
        # 获取已存在数据
        fields = [
            'address', 'area_coding', 'comm_coding', 'contacts', 'cover', 'credit_code', 'declare_type', 'floor_area', 'is_local_device', 'is_reduce_device', 'latitude',
            'liabler', 'loating', 'local_capacity', 'local_factory', 'local_out_dregs_weight', 'local_out_solid_weight', 'local_out_water_weight', 'local_process', 'longitude', 'mam_subtype',
            'mam_type', 'name', 'official_address', 'official_org_name', 'org_sub_type_id', 'permission_code', 'phone', 'reduce_oil_device',
            'reduce_water_device', 'restaurant_predict_weight', 'street_coding',
            'no_credit_reason'
        ]
        
        # 审核通过的数据
        old_data = OrgNonresidentDeclare.objects.filter(is_status_succeed=1, status=2, org_id=org_id).order_by('-id').values(*fields).first()
        if not old_data:
            # 如果不是审核已通过状态，则获取主体表里面的数据
            org_data = Organization.objects.filter(is_declare=1, is_deleted=0, logout_status=0, org_id=org_id).values().first()
            org_detail = OrgDetail.objects.filter(is_deleted=0, org_id=org_id).values().first()
            old_data = get_declare_data_from_org_table(org_data, org_detail, other_rubbishes)
        longitude = old_data['longitude'] or 0
        old_data['longitude'] = float(longitude)
        latitude = old_data['latitude'] or 0
        old_data['latitude'] = float(latitude)
        return old_data
    
    
    def save_data(self, data):
        declare_type = data.get('declare_type') or ConstDeclareType.Normal  # Normal|NoCredit|RepeatCredit
        
        org_id = data.get('org_id')
        if not org_id:
            data['org_id'] = ''.join(str(uuid.uuid1().hex))
        data['org_type_id'] = '60b5ef4bef5311ebbe73fa163e3babe8'
        data['declare_id'] = ''.join(str(uuid.uuid1()).split('-'))
        data['declare_type'] = declare_type
        data['restaurant_trash'] = int(data.get('restaurant_trash_120', 0) or 0) + int(
            data.get('restaurant_trash_240', 0) or 0)
        data['create_time'] = int(time.time())
        data['source_type'] = 'INTERFACE'
        creator = data.get('creator', '')
        
        data['auto_audit'] = 1
        data['creator'] = creator
        # 高德转原始
        longitude = data.get('longitude', 0)
        latitude = data.get('latitude', 0)
        long, lat = gcj02towgs84(longitude, latitude)
        data['longitude'], data['latitude'] = round(long, 11), round(lat, 11)
        # 高德转百度
        longitude_b, latitude_b = gcj02_to_bd09(longitude, latitude)
        data['longitude_b'], data['latitude_b'] = round(longitude_b, 8), round(latitude_b, 8)
        data['longitude_g'] = round(longitude, 8)
        data['latitude_g'] = round(latitude, 8)
        serializer = self.serializer_class(data=data)
        serializer.is_valid(raise_exception=True)
        serializer.save()


def import_excel(path, creator):
    path = r'D:\ztbr-work\非居民\bj_collectmanage_admin\CollectManageApp\import_other\房山区非居民其他垃圾主体台账0319_01.xlsx'
    # path = r'D:\ztbr-work\非居民\bj_collectmanage_admin\CollectManageApp\import_other\非居民其他垃圾导入模板-fs-test.xlsx'
    import_helper = ImportThirdOther(str(path), creator, save_error_file=True)
    import_helper.read_excel_all_and_save()
