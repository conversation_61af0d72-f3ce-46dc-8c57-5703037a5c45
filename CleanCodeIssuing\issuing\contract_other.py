#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

import datetime

from django.core.cache import cache
from django.db import transaction

from Base.utils.const import NonresidentRubbishes
from ..logger import logger
from .base import BaseCleanCodeGenerator

from CollectManageApp.models_base import TransportCompany, TransportCompanyArea, CityRegion
from CollectManageApp.models_transport import Company, CompanyArea, ContractNew


class TransportContractOtherCleanCodeGenerator(BaseCleanCodeGenerator):
    """
    排放登记编码生成器: 非居民合同-其他垃圾非居民合同编号
    确认合同之后，生成合同编号，新合同同步到居民表，同时废除别的合同
    合同编号规则 SHLJ-街道-生效年月-终止年月-顺序号
    contract_num = f'SHLJ-{org.street_coding}-{start}-{end}-{num}'
    """
    CLEAN_TYPE = 'TRANSPORT'
    CLEAN_SUBTYPE = 'OTHER-CONTRACT'

    def generate(self, clean_id):
        
        contract = ContractNew.objects.filter(
            is_delete=0,
            contract_id=clean_id,
            rubbishes=NonresidentRubbishes.OTHER,
            status=1
        ).only('contract_num', 'company_id', 'street_coding', 'service_start_date', 'service_end_date').first()
        
        # 1. 获取编码前缀
        if not contract:
            return dict(code=400, msg='合同不存在.')
        
        if contract.contract_num:
            return dict(code=200, msg='合同编码已生成.', data=dict(
                clean_code=contract.contract_num
            ))
        
        transport = TransportCompany.objects.filter(
            transport_company_id=contract.company_id,
            is_declare=1,
            is_deleted=0
        ).only('clean_code').first()
        if not transport:
            return dict(code=400, msg='收运公司不存在.')
        clean_code_prefix = f'{transport.clean_code}'
        service_start_date = contract.service_start_date.strftime('%Y%m')
        service_end_date = contract.service_end_date.strftime('%Y%m')
        contract_num_prefix = f'SHLJ-{contract.street_coding}-{service_start_date}-{service_end_date}'
        # 2. 锁定编码发号器
        with cache.lock(self.CLEAN_CODE_LOCK_KEY):
            # 2.2 发号逻辑
            try:
                with transaction.atomic(using='ljfl_db'):
                    with transaction.atomic(using='transport_db'):
                        clean_code, clean_no = self._get_clean_code(clean_code_prefix, clean_id, with_no=False, code_format='05d', middle_fix='-')
                        sequence = clean_code.split('-')[-1]
                        contract_num = f'{contract_num_prefix}-{sequence}'
                        # 修改正式库
                        ContractNew.objects.filter(contract_id=clean_id, is_delete=0) \
                            .update(
                            contract_num=contract_num,
                            sign_date=str(datetime.date.today())
                        )
            except Exception as e:
                logger.exception(e)
                return dict(code=500, msg='排放登记编码发放失败.')

        return dict(code=200, msg='排放登记编码发放成功.', data=dict(
            clean_code=contract_num,
            clean_no=clean_no
        ))
