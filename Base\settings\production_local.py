#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import *

DEBUG = True

ALLOWED_HOSTS = [
    '*'
]
DATABASES = {
    'default': {},
    'ljfl_declare_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_declare_db',
        'HOST': '************',
        'USER': 'collectmanage',
        'PASSWORD': 'Collectmanage_20220625',
        'PORT': 33306,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'HOST': '************',
        'USER': 'collectmanage',
        'PASSWORD': 'Collectmanage_20220625',
        'PORT': 33306,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'ljfl_db_replica': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'HOST': '************',
        'USER': 'collectmanage',
        'PASSWORD': 'Collectmanage_20220625',
        'PORT': 33306,
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'transport_db': {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': '************',  # 生产环境
        'PORT': '33307',
        'USER': 'transport',
        'PASSWORD': 'Transport_db20210727',
        'NAME': 'transport_db',
        'OPTIONS': {'charset': 'utf8mb4'},
    },
    "tidb_ljfl_db": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "ljfl_db",
        "USER": "cityljfl",
        "PASSWORD": "City_ljfl20221024",
        "HOST": "************",
        "PORT": 33310,
        "OPTIONS": {"charset": "utf8mb4"},
    },
    # 'solid': {
    #     'ENGINE': 'django.db.backends.mysql',
    #     'NAME': 'solid_logistics',
    #     'USER': 'solidlogistics',
    #     'PASSWORD': 'solidlogistics20200107',
    #     'HOST': '***********',
    #     'PORT': 3306,
    #     'OPTIONS': {'charset': 'utf8mb4'},
    # },
}

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',  # 用于区分不同项目或环境的标识符
        # 可选配置：超时时间、最大条目数等
        'TIMEOUT': 300,    # 默认缓存超时时间（单位：秒）
        'OPTIONS': {
            'MAX_ENTRIES': 1000  # 缓存中最大条目数
        }
    }
}

# 管理授权
AUTH_APPID = *********
AUTH_APPSECRET = 'jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7'
# AUTH_HOST = '***********:8296'
AUTH_HOST = 'auth.ztbory.com'
# 基础服务
BASE_HOST = '***********:8290'
# OSS服务
OSS_HOST = 'filemanager.ztbory.com'
# 资质审核服务
QUALIFI_HOST = '***********:8520'

COMPANY_SYS_IP = 'http://***********:8520'

# 市级服务
CITY_IP = 'http://***********:8094'

INTERNAL_NETWORKS = ["***********/24", "************"]
