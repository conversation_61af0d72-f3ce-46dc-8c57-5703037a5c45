#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import datetime
from collections import namedtuple

from dateutil.relativedelta import relativedelta
from django.db import connections
from django.db.models import Count, Case, When, Sum
from django.db.models.expressions import RawSQL

from Base.utils.const import NonresidentRubbishes, ConstRubbishType
from Base.utils.tools import TimeFilterTools, timestamp_to_datetime
from CollectManageApp.models import OtherOrgNonresidentDeclare
from CollectManageApp.models_base import OrgGroupBase, Organization, Car, CleaningPoint, TransportCompanyArea, \
    TransportCompany, CarRecord, CleaningPointRecord, TerminalFactoryRecord, CityRegion


def namedtuple_fetchall(cursor):
    desc = cursor.description
    nt_result = namedtuple("Result", [col[0] for col in desc])
    return [nt_result(*row) for row in cursor.fetchall()]


def exe_tidb_ljfl_db(sql):
    with connections["tidb_ljfl_db"].cursor() as cursor:
        cursor.execute(sql)
        results = namedtuple_fetchall(cursor)
        data = list()
        for result in results:
            data.append(dict(zip(*map(list, (result._fields, result)))))
    return data


class NonresidentOtherService:

    def __init__(self, request):
        self.request = request
        second_org_ids = OrgGroupBase.objects.using("tidb_ljfl_db").filter(status=1, logout_status=0).values_list(
            'org_id', flat=True)
        self.org_queryset = Organization.objects.using("tidb_ljfl_db").filter(is_deleted=0,
                                                                              is_declare=1,
                                                                              logout_status=0,
                                                                              rubbishes__contains=NonresidentRubbishes.OTHER,
                                                                              ).exclude(org_id__in=second_org_ids)
        self.car_queryset = Car.objects.using("tidb_ljfl_db").filter(standing_book=1,
                                                                     is_declare=1,
                                                                     type_id=ConstRubbishType.OTHER).only("car_num")
        self.clean_queryset = CleaningPoint.objects.using("tidb_ljfl_db").filter(is_deleted__in=(0, 1), hidden=0,
                                                                                 transport_company_id__isnull=False).exclude(
            transport_company_id='')
        self.area_company = TransportCompanyArea.objects.using("tidb_ljfl_db").filter(is_deleted=0,
                                                                                      rubbishes=NonresidentRubbishes.OTHER)
        self.transport_company = TransportCompany.objects.using("tidb_ljfl_db").filter(is_deleted=0)

    def get_org(self, **kwargs):
        queryset = self.org_queryset
        params = self.request.GET.dict()
        area_coding = params.get('area_coding')
        street_coding = params.get('street_coding')
        comm_coding = params.get('comm_coding')
        org_id = params.get('org_id')
        name_search = params.get('name_search')
        clean_no = params.get('clean_no')
        if area_coding:
            queryset = queryset.filter(area_coding=area_coding)
        if street_coding:
            queryset = queryset.filter(street_coding=street_coding)
        if comm_coding:
            queryset = queryset.filter(comm_coding=comm_coding)
        if org_id:
            queryset = queryset.filter(org_id=org_id)
        if name_search:
            queryset = queryset.filter(name__icontains=name_search)
        if clean_no:
            queryset = queryset.filter(clean_no=clean_no)
        return queryset

    def get_org_standing_book_stats(self, **kwargs):
        queryset = self.get_org(**kwargs)
        stats = queryset.aggregate(
            org_count=Count("org_id"),
            have_contract_count=Count(Case(When(other_have_contract=1, then='org_id'))),
        )
        return dict(
            org_count=stats.get("org_count") or 0,
            have_contract_count=stats.get("have_contract_count") or 0,
        )

    def get_cleaning_point(self, **kwargs):
        queryset = self.clean_queryset
        params = self.request.GET.dict()
        area_coding = params.get('area_coding')
        street_coding = params.get('street_coding')
        transport_company_id = params.get('transport_company_id')
        transport_company_name_search = params.get('transport_company_name_search')
        name_search = params.get('name_search')
        if area_coding:
            queryset = queryset.filter(area_coding=area_coding)
        if street_coding:
            queryset = queryset.filter(street_coding=street_coding)
        if transport_company_id:
            queryset = queryset.filter(transport_company_id=transport_company_id)
        if transport_company_name_search:
            company = self.transport_company.filter(company__icontains=transport_company_name_search)
            company_ids = company.values_list("transport_company_id", flat=True)
            queryset = queryset.filter(transport_company_id__in=company_ids)
        if name_search:
            queryset = queryset.filter(name__icontains=name_search)
        return queryset

    def get_cleaning_point_stats(self, **kwargs):
        queryset = self.get_cleaning_point(**kwargs)
        stats = queryset.aggregate(
            cleaning_point_count=Count("cleaning_point_id")
        )
        return dict(
            cleaning_point_count=stats.get("cleaning_point_count") or 0
        )

    def get_car(self, **kwargs):
        queryset = self.car_queryset
        params = self.request.GET.dict()
        area_coding = params.get('area_coding')
        street_coding = params.get('street_coding')
        transport_company_id = params.get('transport_company_id')
        car_num_search = params.get('car_num_search')
        transport_company_name_search = params.get('transport_company_name_search')
        if area_coding:
            queryset = queryset.filter(area_coding=area_coding)
        if street_coding:
            queryset = queryset.filter(street_coding__icontains=street_coding)
        if transport_company_name_search:
            company = self.transport_company.filter(company__icontains=transport_company_name_search)
            company_ids = company.values_list("transport_company_id", flat=True)
            queryset = queryset.filter(transport_company_id__in=company_ids)
        if car_num_search:
            queryset = queryset.filter(car_num__icontains=car_num_search)
        if transport_company_id:
            queryset = queryset.filter(transport_company_id=transport_company_id)
        return queryset

    def get_car_stats(self, **kwargs):
        queryset = self.get_car(**kwargs)
        stats = queryset.aggregate(
            car_count=Count("car_num")
        )
        return dict(
            car_count=stats.get("car_count") or 0
        )

    def get_company(self, **kwargs):
        queryset = self.area_company
        params = self.request.GET.dict()
        area_coding = params.get('area_coding')
        street_coding = params.get('street_coding')
        end_date = params.get('end_date')
        if area_coding:
            queryset = queryset.filter(area_coding=area_coding)
        if street_coding:
            queryset = queryset.filter(street_coding__icontains=street_coding)
        if end_date:
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d") + relativedelta(days=1)
            queryset = queryset.filter(create_time__lt=end_date.timestamp())
        transport_company_ids = list(self.transport_company.values_list("transport_company_id",flat=True))
        queryset = queryset.filter(transport_company_id__in=transport_company_ids)
        return queryset

    def get_company_stats(self, **kwargs):
        queryset = self.get_company(**kwargs)
        return dict(
            company_count=queryset.values("transport_company_id").distinct().count() or 0
        )

    def get_car_record(self, **kwargs):
        queryset = CarRecord.objects.using("tidb_ljfl_db").filter(is_deleted=0, type_id=ConstRubbishType.OTHER)
        params = self.request.GET.dict()
        area_coding = params.get('area_coding')
        street_coding = params.get('street_coding')
        comm_coding = params.get('comm_coding')
        org_id = params.get('org_id')
        duration = params.get('duration')
        if area_coding:
            queryset = queryset.filter(area_coding=area_coding)
        if street_coding:
            queryset = queryset.filter(street_coding=street_coding)
        if comm_coding:
            queryset = queryset.filter(comm_coding=comm_coding)
        if org_id:
            queryset = queryset.filter(org_id=org_id)
        if duration:
            queryset = TimeFilterTools.create_time_filter(self.request, queryset, duration)
        return queryset

    def get_terminal_factory_record(self, **kwargs):
        queryset = TerminalFactoryRecord.objects.using("tidb_ljfl_db").filter(is_deleted=0, is_abnormal=0)
        params = self.request.GET.dict()
        area_coding = params.get('area_coding')
        street_coding = params.get('street_coding')
        duration = params.get('duration')
        if area_coding:
            queryset = queryset.filter(area_coding=area_coding)
        if street_coding:
            queryset = queryset.filter(street_coding=street_coding)
        if duration:
            queryset = TimeFilterTools.weightptime_filter(self.request, queryset, duration)
        return queryset

    def get_car_org_stats_by_sql(self):
        area_coding = self.request.GET.get("area_coding")
        duration = self.request.GET.get("duration")
        where_list = []
        if area_coding:
            where_list.append(f"area_coding='{area_coding}'")
        if duration:
            s_date, e_date = TimeFilterTools.get_date_duration(self.request, duration)
            where_list.append(f"create_time >= {s_date.timestamp()}")
            where_list.append(f"create_time < {e_date.timestamp()}")
        where_str = " AND " + " AND ".join(where_list)
        sql = f"""
            SELECT
            /*+ READ_FROM_STORAGE(TIFLASH[`car_record`]) */
                car_num,org_id
            FROM
                car_record
            WHERE
                (
                    is_deleted = 0
                    AND type_id = 'b8c900bac02a11eaa8a9000c29d3cc31'
                    {where_str}
                    AND car_num IN (
                        SELECT
                            car_num
                        FROM
                            car
                        WHERE
                            is_declare = 1
                            AND standing_book = 1
                            AND type_id = 'b8c900bac02a11eaa8a9000c29d3cc31'
                            AND area_coding = car_record.area_coding
                            
                    )
                )
            GROUP BY
                car_num,org_id
                """
        cursor = connections['tidb_ljfl_db'].cursor()
        cursor.execute(sql)
        result = cursor.fetchall()
        return result

    def get_cleaning_point_org_stats_by_sql(self):
        area_coding = self.request.GET.get("area_coding")
        duration = self.request.GET.get("duration")
        where_list = []
        if area_coding:
            where_list.append(f"area_coding='{area_coding}'")
        if duration:
            s_date, e_date = TimeFilterTools.get_date_duration(self.request, duration)
            where_list.append(f"create_time >= {s_date.timestamp()}")
            where_list.append(f"create_time < {e_date.timestamp()}")
        where_str = " AND " + " AND ".join(where_list)
        sql = f"""
            SELECT
            /*+ READ_FROM_STORAGE(TIFLASH[`cleaning_point_record`]) */
                cleaning_point_id,org_id
            FROM
                cleaning_point_record
            WHERE
                (
                    is_deleted = 0
                    AND type_id = 'b8c900bac02a11eaa8a9000c29d3cc31'
                    {where_str}
                    AND cleaning_point_id IN (
                        SELECT
                            cleaning_point_id
                        FROM
                            cleaning_point
                        WHERE
                            is_deleted in(0,1)
                            AND hidden = 0
                            AND transport_company_id <> ''
                            AND area_coding = cleaning_point_record.area_coding
                    )
                    
                )
            GROUP BY
                cleaning_point_id,org_id
                """
        cursor = connections['tidb_ljfl_db'].cursor()
        cursor.execute(sql)
        result = cursor.fetchall()
        return result

    def get_car_running_stats(self):
        org = self.get_org()
        car = self.get_car()
        terminal_record = self.get_terminal_factory_record()
        org_ids = set(org.values_list("org_id", flat=True))
        car_nums = set(car.values_list("car_num", flat=True))

        terminal_car_nums = terminal_record.filter(platenumber__in=car_nums).count()
        car_org_stats = self.get_car_org_stats_by_sql()

        run_car_nums = set()
        run_org_ids = set()
        for item in car_org_stats:
            car_num, org_id = item
            if car_num:
                run_car_nums.add(car_num)
            if org_id:
                run_org_ids.add(org_id)

        running_org_count = len(org_ids & run_org_ids)
        running_facilities_count = len(car_nums & run_car_nums)
        org_count = len(org_ids)
        facilities_count = len(car_nums)
        terminal_car_count = terminal_car_nums
        return dict(
            org_count=org_count,
            facilities_count=facilities_count,
            running_org_count=running_org_count,
            running_facilities_count=running_facilities_count,
            stopping_org_count=org_count - running_org_count,
            stopping_facilities_count=facilities_count - running_facilities_count,
            terminal_car_count=terminal_car_count
        )

    def get_cleaning_point_record(self, **kwargs):
        queryset = CleaningPointRecord.objects.using("tidb_ljfl_db").filter(is_deleted=0,
                                                                            type_id=ConstRubbishType.OTHER)
        params = self.request.GET.dict()
        area_coding = params.get('area_coding')
        street_coding = params.get('street_coding')
        comm_coding = params.get('comm_coding')
        org_id = params.get('org_id')
        duration = params.get('duration')
        if area_coding:
            queryset = queryset.filter(area_coding=area_coding)
        if street_coding:
            queryset = queryset.filter(street_coding=street_coding)
        if comm_coding:
            queryset = queryset.filter(comm_coding=comm_coding)
        if org_id:
            queryset = queryset.filter(org_id=org_id)
        if duration:
            queryset = TimeFilterTools.create_time_filter(self.request, queryset, duration)
        return queryset

    def get_clean_running_stats(self):
        org = self.get_org()
        cleaning_point = self.get_cleaning_point()
        org_ids = set(org.values_list("org_id", flat=True))
        cleaning_point_ids = set(cleaning_point.values_list("cleaning_point_id", flat=True))

        clean_org_stats = self.get_cleaning_point_org_stats_by_sql()

        running_facilities_ids = set()
        run_org_ids = set()
        for item in clean_org_stats:
            cleaning_point_id, org_id = item
            if cleaning_point_id:
                running_facilities_ids.add(cleaning_point_id)
            if org_id:
                run_org_ids.add(org_id)
        running_org_count = len(org_ids & run_org_ids)
        running_facilities_count = len(cleaning_point_ids & running_facilities_ids)
        facilities_count = len(cleaning_point_ids)
        org_count = len(org_ids)
        return dict(
            org_count=org_count,
            facilities_count=facilities_count,
            running_org_count=running_org_count,
            running_facilities_count=running_facilities_count,
            stopping_org_count=org_count - running_org_count,
            stopping_facilities_count=facilities_count - running_facilities_count,
        )

    def get_collect_org_stats(self):
        org = self.get_org()
        org_ids = set(org.values_list("org_id", flat=True))

        car = self.get_car()
        car_nums = car.values_list("car_num", flat=True)
        car_record = self.get_car_record().filter(car_num__in=car_nums)
        car_record_org_ids = set(car_record.values_list("org_id", flat=True).distinct())
        car_run_org_ids = org_ids & car_record_org_ids

        cleaning_point = self.get_cleaning_point()
        cleaning_point_ids = set(cleaning_point.values_list("cleaning_point_id", flat=True).distinct())
        cleaning_point_record = self.get_cleaning_point_record().filter(cleaning_point_id__in=cleaning_point_ids)
        clean_org_ids = set(cleaning_point_record.values_list("org_id", flat=True).distinct())
        clean_run_org_ids = org_ids & clean_org_ids

        collect_org_count = len(car_run_org_ids | clean_run_org_ids)
        org_count = len(org_ids)
        no_collect_org_count = org_count - collect_org_count
        return dict(
            org_count=org_count,
            collect_org_count=collect_org_count,
            no_collect_org_count=no_collect_org_count,
        )

    def get_register_time_mapping(self, org_id_list):
        org_ids = set(org_id for org_id in org_id_list)
        non_resident_declare_qs = OtherOrgNonresidentDeclare.objects.filter(status=2, org_id__in=org_ids,
                                                                            check_type__in=[0, 1])

        return {i.org_id: i.audit_time for i in non_resident_declare_qs}

    def get_org_info_by_org_id(self):
        org_id = self.request.GET.get('org_id')
        org_info = Organization.objects.filter(is_deleted=0, is_declare=1, logout_status=0, org_id=org_id,
                                               rubbishes__contains=NonresidentRubbishes.OTHER).first()
        if not org_info:
            return {}
        region_map = {i.coding: i.name for i in
                      CityRegion.objects.using("tidb_ljfl_db").filter(is_deleted=0).only("coding", "name")}
        company_map = {i.transport_company_id: i.company for i in
                       TransportCompany.objects.using("tidb_ljfl_db").filter(is_deleted=0)}
        register_map = self.get_register_time_mapping([org_id])
        results = dict(
            area_coding=org_info.area_coding,
            street_coding=org_info.street_coding,
            comm_coding=org_info.comm_coding,
            area_name=region_map.get(org_info.area_coding),
            street_name=region_map.get(org_info.street_coding),
            comm_name=region_map.get(org_info.comm_coding),
            org_id=org_info.org_id,
            cover=org_info.cover.split(',')[0],
            name=org_info.name,
            clean_code=org_info.clean_code,
            clean_no=org_info.clean_no,
            credit_code=org_info.credit_code,
            address=org_info.address,
            contacts=org_info.contacts,
            phone=org_info.phone,
            other_transport_company_id=org_info.other_transport_company_id or "",
            other_transport_company_name=company_map.get(org_info.other_transport_company_id) or "",
            register_time_str=timestamp_to_datetime(register_map.get(org_id) or org_info.create_time),
            create_time_str=timestamp_to_datetime(org_info.create_time)
        )
        return results

    def get_cleaning_point_info_by_id(self):
        id = self.request.GET.get('cleaning_point_id')
        info = CleaningPoint.objects.filter(is_deleted__in=(0, 1), cleaning_point_id=id).first()
        if not info:
            return {}
        region_map = {i.coding: i.name for i in
                      CityRegion.objects.using("tidb_ljfl_db").filter(is_deleted=0).only("coding", "name")}
        company_map = {i.transport_company_id: i.company for i in
                       TransportCompany.objects.using("tidb_ljfl_db").filter(is_deleted=0)}
        results = dict(
            area_coding=info.area_coding,
            street_coding=info.street_coding,
            comm_coding=info.comm_coding,
            area_name=region_map.get(info.area_coding),
            street_name=region_map.get(info.street_coding),
            comm_name=region_map.get(info.comm_coding),
            cover=info.cover,
            name=info.name,
            address=info.address,
            contacts=info.contacts,
            phone=info.phone,
            transport_company_id=info.transport_company_id or "",
            transport_company_name=company_map.get(info.transport_company_id) or "",
            create_time_str=timestamp_to_datetime(info.create_time)
        )
        return results

    def get_collect_weight_stats(self):
        org = self.get_org()
        org_ids = org.values_list("org_id", flat=True)

        car = self.get_car()
        car_nums = car.values_list("car_num", flat=True)
        car_record = self.get_car_record().filter(car_num__in=car_nums)
        car_weight = car_record.aggregate(weight=Sum('weight')).get("weight") or 0

        cleaning_point = self.get_cleaning_point()
        cleaning_point_ids = set(cleaning_point.values_list("cleaning_point_id", flat=True).distinct())
        cleaning_point_record = self.get_cleaning_point_record().filter(cleaning_point_id__in=cleaning_point_ids,
                                                                        org_id__in=org_ids)
        cleaning_point_weight = cleaning_point_record.aggregate(weight=Sum('weight')).get("weight") or 0
        return dict(
            total_weight=car_weight + cleaning_point_weight,
            car_weight=car_weight,
            cleaning_point_weight=cleaning_point_weight,
        )

    def get_company_map(self):
        return {i.get("transport_company_id"): i.get("company") for i in
                self.transport_company.values("transport_company_id", "company")}

    def get_area_name_map(self):
        return {i.get("coding"): i.get("name") for i in
                CityRegion.objects.filter(is_deleted=0, grade=2).values("coding", "name")}

    def get_car_warning_stats(self):
        duration = self.request.GET.get("duration")
        page = self.request.GET.get("page")
        page_size = self.request.GET.get("page_size")
        s_date, e_date = TimeFilterTools.get_date_duration(self.request, duration)
        date_sql = RawSQL("DATE_FORMAT(FROM_UNIXTIME(create_time),'%%Y-%%m-%%d')", [])
        car = self.get_car().order_by("id")
        car_record = self.get_car_record()

        car_nums = car.values_list("car_num", flat=True)
        car_record = car_record.filter(car_num__in=car_nums)
        stats = set(car_record.annotate(date=date_sql).values_list("car_num", "date").distinct())
        running_car_date_map = {i[1] + "_" + i[0]: 1 for i in stats}
        company_map = self.get_company_map()

        results = []
        car = list(car.values("car_num", "area_coding", "transport_company_id", "district"))
        while e_date > s_date:
            e_date += relativedelta(days=-1)
            now_date = str(e_date.date())
            for car_info in car:
                area_coding = car_info.get("area_coding")
                car_num = car_info.get("car_num")
                transport_company_id = car_info.get("transport_company_id")
                transport_company_name = company_map.get(transport_company_id)
                district = car_info.get("district")
                key = now_date+"_"+car_num
                if running_car_date_map.get(key):
                    continue
                data = dict(
                    date=now_date,
                    car_num=car_num,
                    area_coding=area_coding,
                    area_name=district,
                    transport_company_id=transport_company_id,
                    transport_company_name=transport_company_name,
                )
                results.append(data)

        total_count = len(results)
        if page and page_size:
            s_page = (int(page) - 1) * int(page_size)
            page_size = int(page_size) * int(page)
            results = results[s_page: page_size]
        return dict(
            count=total_count,
            results=results
        )

    def get_cleaning_point_warning_stats(self):
        duration = self.request.GET.get("duration")
        page = self.request.GET.get("page")
        page_size = self.request.GET.get("page_size")
        s_date, e_date = TimeFilterTools.get_date_duration(self.request, duration)
        date_sql = RawSQL("DATE_FORMAT(FROM_UNIXTIME(create_time),'%%Y-%%m-%%d')", [])
        facilities = self.get_cleaning_point()
        record = self.get_cleaning_point_record()

        facilities_ids = facilities.values_list("cleaning_point_id", flat=True)
        record = record.filter(cleaning_point_id__in=facilities_ids)
        stats = set(record.annotate(date=date_sql).values_list("cleaning_point_id", "date").distinct())
        running_date_map = {i[1] + "_" + i[0]: 1 for i in stats}
        company_map = self.get_company_map()
        area_name_map = self.get_area_name_map()
        results = []
        facilities_list = list(
            facilities.values("cleaning_point_id", "area_coding", "transport_company_id", "area_coding", "name"))
        while e_date > s_date:
            e_date += relativedelta(days=-1)
            now_date = str(e_date.date())
            for info in facilities_list:
                area_coding = info.get("area_coding")
                cleaning_point_id = info.get("cleaning_point_id")
                name = info.get("name")
                transport_company_id = info.get("transport_company_id")
                transport_company_name = company_map.get(transport_company_id)
                area_name = area_name_map.get(area_coding)
                key = now_date+"_"+cleaning_point_id
                if running_date_map.get(key):
                    continue
                data = dict(
                    date=now_date,
                    cleaning_point_id=cleaning_point_id,
                    name=name,
                    area_coding=area_coding,
                    area_name=area_name,
                    transport_company_id=transport_company_id,
                    transport_company_name=transport_company_name,
                )
                results.append(data)
        total_count = len(results)
        if page and page_size:
            s_page = (int(page) - 1) * int(page_size)
            page_size = int(page_size) * int(page)
            results = results[s_page: page_size]
        return dict(
            count=total_count,
            results=results
        )
