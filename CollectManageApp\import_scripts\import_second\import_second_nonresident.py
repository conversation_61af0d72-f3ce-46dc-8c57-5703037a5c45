"""导入三方二级用户 按照小程序规则"""
import uuid
from dataclasses import asdict, dataclass
from datetime import datetime

import openpyxl
from django.db import transaction
from ksuid import Ksuid
from openpyxl.styles import PatternFill
from openpyxl.worksheet.worksheet import Worksheet
from typing import List, Tuple

from Base.utils.const import ConstDeclareType, NonresidentRubbishes
from CollectManageApp.models_base import OrgGroup, OrgGroupBase, Organization

class RowException(Exception):
    pass


rubbishes_mapper = {
    '餐厨垃圾': NonresidentRubbishes.RESTAURANTS,
    '其他垃圾': NonresidentRubbishes.OTHER,
}


@dataclass
class SubResidentData:
    org_group_id: str
    name: str
    contacts: str
    phone: str
    cover: str
    restaurant_trash_120: int
    restaurant_trash_240: int
    restaurant_predict_weight: int
    rubbishes: str
    credit_code: str = ""
    
    def __post_init__(self):
        if not self.credit_code:
            self.credit_code = ""
    
    
    
    def to_dict(self):
        return asdict(self)


def add_sub_noresident(data: SubResidentData):
    org_group_id = data.org_group_id
    rubbishes = data.rubbishes
    
    # 提前查询并缓存结果
    org_group = Organization.objects.filter(
        org_id=org_group_id,
        is_declare=1,
        is_deleted=0,
        logout_status=0,
        rubbishes__icontains=rubbishes,
    ).values('credit_code', 'name', 'clean_no',
             'area_coding', 'street_coding',
             'comm_coding', 'org_sub_type_id').first()
    
    if not org_group:
        raise RowException('您绑定到的非居民不存在或不存在该垃圾类型！')
    
    org_data = {key: org_group[key] for key in org_group}
    
    name = data.name
    contacts = data.contacts
    phone = data.phone
    credit_code = data.credit_code
    cover = data.cover
    restaurant_predict_weight = data.restaurant_predict_weight
    restaurant_trash_120 = data.restaurant_trash_120
    restaurant_trash_240 = data.restaurant_trash_240
    restaurant_trash = restaurant_trash_120 + restaurant_trash_240
    org_id = str(uuid.uuid1()).replace('-', '')
    timestamp = int(datetime.now().timestamp())
    
    if OrgGroup.objects.filter(org_id=org_id, logout_status=0, status=1).exists():
        raise RowException('该非居民为二级用户，不能创建子用户.')
    
    exists_second_orgs = Organization.objects.filter(
            name=name,
            area_coding=org_data['area_coding'],
            street_coding=org_data['street_coding'],
            comm_coding=org_data['comm_coding'],
            source_type='SECOND',
            is_declare=1,
            is_deleted=0,
            logout_status=0,
            rubbishes__icontains=rubbishes,
    ).values_list('org_id', flat=True)
    # 可能有多个重名的二级用户
    exists_second_orgs = list(exists_second_orgs)
    if exists_second_orgs:
        if OrgGroup.objects.filter(org_group_id=org_group_id, org_id__in=exists_second_orgs, status=1, rubbishes=rubbishes).exists():
            raise RowException('该二级用户已存在！')
    # raise RowException('位置问题')
    with transaction.atomic(using='ljfl_db'):
        with transaction.atomic(using='ljfl_declare_db'):
            is_group_second = 0
            is_group_second_other = 0
            if NonresidentRubbishes.RESTAURANTS in rubbishes:
                is_group_second = 1
            if NonresidentRubbishes.OTHER in rubbishes:
                is_group_second_other = 1

            Organization.objects.create(
                **{
                    'name': name,
                    'contacts': contacts,
                    'phone': phone,
                    'credit_code': credit_code,
                    'cover': cover,
                    'restaurant_predict_weight': restaurant_predict_weight,
                    'restaurant_trash_120': restaurant_trash_120,
                    'restaurant_trash_240': restaurant_trash_240,
                    'source_type': 'SECOND',
                    'org_id': org_id,
                    'is_declare': 1,
                    'is_group': 0,
                    'is_group_second': is_group_second,
                    'is_group_other': 0,
                    'is_group_second_other': is_group_second_other,
                    'org_type_id': '60b5ef4bef5311ebbe73fa163e3babe8',
                    'is_reduce_device': 0,
                    'is_local_device': 0,
                    'restaurant_trash': restaurant_trash,
                    'is_deleted': 0,
                    'restaurant_trash_rfid': 0,
                    'create_time': timestamp,
                    'update_time': timestamp,
                    'area_coding': org_data['area_coding'],
                    'street_coding': org_data['street_coding'],
                    'comm_coding': org_data['comm_coding'],
                    'declare_type': ConstDeclareType.ChildOrg,
                    'org_sub_type_id': org_data['org_sub_type_id'],
                    'rubbishes': rubbishes
                }
            )
            if NonresidentRubbishes.RESTAURANTS in rubbishes:
                Organization.objects.filter(org_id=org_group_id).update(is_group=1)
            if NonresidentRubbishes.OTHER in rubbishes:
                Organization.objects.filter(org_id=org_group_id).update(is_group_other=1)
            OrgGroup.objects.create(
                org_group_id=org_group_id,
                org_id=org_id,
                org_group_credit_code=org_data['credit_code'],
                org_group_clean_no=org_data['clean_no'],
                org_group_name=org_data['name'],
                uuid=Ksuid(),
                status=1,
                rubbishes=rubbishes
            )


def rede_excel(path):
    excel = openpyxl.load_workbook(path)
    sheet: Worksheet = excel.active
    
    errors: List[Tuple[int, List[str]]] = []
    
    for row_num, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), 2):
        try:
            org_group_id, name, contacts, phone, credit_code, restaurant_trash_120, restaurant_trash_240, restaurant_predict_weight, rubbishes, cover, *others = row
            
            if not all([org_group_id, name, contacts, phone, rubbishes, cover]) or None in [restaurant_trash_120, restaurant_trash_240, restaurant_predict_weight]:
                raise ValueError('缺少字段')
            
            restaurant_trash_120 = int(restaurant_trash_120)
            restaurant_trash_240 = int(restaurant_trash_240)
            restaurant_predict_weight = int(restaurant_predict_weight)
            
            if rubbishes not in rubbishes_mapper:
                raise ValueError('垃圾类型错误')
            
            add_sub_noresident(SubResidentData(
                org_group_id=org_group_id,
                name=name,
                contacts=contacts,
                phone=phone,
                credit_code=credit_code,
                rubbishes=rubbishes_mapper[rubbishes],
                restaurant_trash_120=restaurant_trash_120,
                restaurant_trash_240=restaurant_trash_240,
                restaurant_predict_weight=restaurant_predict_weight,
                cover=cover,
            ))
        
        except (ValueError, RowException) as e:
            errors.append((row_num, [str(e)]))
    errors_dict = {row_num: row_errors for row_num, row_errors in errors}
    for row_num in range(1, sheet.max_row + 1):
        if row_num in errors_dict:
            print(f'[导入出错][行：{row_num}][{errors_dict[row_num]}]')
            sheet.cell(row=row_num, column=11).value = '\n'.join(errors_dict[row_num])
            sheet.cell(row=row_num, column=11).fill = PatternFill(start_color="FF0000", end_color="FF0000", fill_type='solid')
        else:
            sheet.cell(row=row_num, column=11).value = ''
            sheet.cell(row=row_num, column=11).fill = PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type='solid')
    excel.save(path)

def start():
    print('[start import_second_nonresident]')
    path = r'D:\ztbr-work\非居民\bj_collectmanage_admin\CollectManageApp\import_scripts\import_second\非居民二级用户导入-3.xlsx'
    rede_excel(path)
    print('[end import_second_nonresident]')

"""
'4037f533c75a11efac129c2976c4ef22',
'3ffd292fc75a11efa5a39c2976c4ef22',
'3fc21cb7c75a11efbef29c2976c4ef22',
'3f4e3aa3c75a11ef82669c2976c4ef22',
'3ee338a5c75a11efb4ac9c2976c4ef22',
'3ead9a90c75a11efb3899c2976c4ef22',
'3e71218bc75a11efa98a9c2976c4ef22',
'3e373c69c75a11efaf689c2976c4ef22',
'3df62c8bc75a11efb2669c2976c4ef22',
'3dbd5b88c75a11efa6029c2976c4ef22',
'3d84f224c75a11efa09e9c2976c4ef22',
'3d4d30c0c75a11efb5599c2976c4ef22',
'3d15b71ec75a11efbefe9c2976c4ef22',
'3cdc1987c75a11ef93f39c2976c4ef22',
'3c80fc1bc75a11efbae29c2976c4ef22'
"""
