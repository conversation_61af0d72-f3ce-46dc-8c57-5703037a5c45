#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

import os
import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Base.settings.development")
django.setup()

from CleanCodeIssuing.clean_code_issuing import CleanCodeIssuing

issuing = CleanCodeIssuing()
result = issuing.get_clean_code(
    clean_type='ORG',
    clean_sub_type='NONRESIDENT',
    clean_id='2d79d574f1b011eb8ab590324b3e42ee'
)
print(result)
