# #!/usr/bin/python
# # coding: utf-8
from __future__ import absolute_import
import datetime
import atexit
import fcntl

import pytz
from django.core.cache import cache
from apscheduler.schedulers.background import BackgroundScheduler

from CollectManageApp.scheduler.tasks import org_group_tranfer, timed_task_update_terminal_record_status, clean_code_no_generate, \
    organization_declare_auto_audit, timed_task_update_appeal_status, timed_task_handle_apply_clean_order, \
    organization_statist, timed_task_non_bill_confirm, timed_task_non_pay_info, other_clean_code_no_generate, \
    other_nonresident_auto_failed, timed_task_quota_appeal, timed_task_month_pay


scheduler = BackgroundScheduler(timezone=pytz.timezone("Asia/Shanghai"))
scheduler.add_job(timed_task_update_terminal_record_status, 'cron', minute='*/10')
scheduler.add_job(clean_code_no_generate, 'cron', second='*/10')
scheduler.add_job(organization_declare_auto_audit, 'cron', second='*/30')
scheduler.add_job(timed_task_update_appeal_status, 'interval', hours=6)
scheduler.add_job(timed_task_handle_apply_clean_order, 'cron', minute='*/30', hour='9-20')
scheduler.add_job(organization_statist, 'cron', hour=1, minute=30)
scheduler.add_job(timed_task_non_bill_confirm, 'cron', minute='*/10')
# scheduler.add_job(timed_task_non_pay_info, 'cron',  minute='*/1')
scheduler.add_job(other_clean_code_no_generate, 'cron', second='*/10')
# 其他垃圾非居民选择未注册的清运公司 72小时候后自动审核失败
scheduler.add_job(other_nonresident_auto_failed, 'cron', minute='*/1')
scheduler.add_job(timed_task_quota_appeal, 'cron', hour=23, minute=50)
scheduler.add_job(timed_task_month_pay, 'cron', year='*', month='*', day=1)
scheduler.add_job(org_group_tranfer, 'cron', second='*/10')

locker = open('scheduler.lock', 'wb')


def scheduler_init():
    try:
        fcntl.flock(locker, fcntl.LOCK_EX | fcntl.LOCK_NB)
        scheduler.start()
    except:
        pass

    def unlock():
        fcntl.flock(locker, fcntl.LOCK_UN)
        locker.close()

    atexit.register(unlock)
