# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: protoc.proto
# Protobuf Python Version: 4.25.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0cprotoc.proto\x12\x07MiniApp\"/\n\x0cLoginRequest\x12\x11\n\tclient_id\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\"\xa4\x01\n\rLoginResponse\x12\x0f\n\x07\x65rrcode\x18\x01 \x01(\r\x12\x0e\n\x06\x65rrmsg\x18\x02 \x01(\t\x12\x11\n\tclient_id\x18\x03 \x01(\t\x12\x0e\n\x06openid\x18\x04 \x01(\t\x12\x13\n\x0bsession_key\x18\x05 \x01(\t\x12\x0f\n\x07unionid\x18\x06 \x01(\t\x12)\n\x04\x64\x61ta\x18\x07 \x01(\x0b\x32\x1b.MiniApp.Jscode2SessionInfo\"]\n\x12Jscode2SessionInfo\x12\x11\n\tclient_id\x18\x01 \x01(\t\x12\x0e\n\x06openid\x18\x02 \x01(\t\x12\x13\n\x0bsession_key\x18\x03 \x01(\t\x12\x0f\n\x07unionid\x18\x04 \x01(\t\"\'\n\x12\x41\x63\x63\x65ssTokenRequest\x12\x11\n\tclient_id\x18\x01 \x01(\t\"t\n\x12\x41\x63\x63\x65ssTokenRespons\x12\x0f\n\x07\x65rrcode\x18\x01 \x01(\r\x12\x0e\n\x06\x65rrmsg\x18\x02 \x01(\t\x12\x11\n\tclient_id\x18\x03 \x01(\t\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x04 \x01(\t\x12\x14\n\x0c\x65xpires_time\x18\x05 \x01(\x05\"&\n\x12GetUserInfoRequest\x12\x10\n\x08username\x18\x01 \x01(\t\"U\n\x13GetUserInfoResponse\x12\x0f\n\x07\x65rrcode\x18\x01 \x01(\r\x12\x0e\n\x06\x65rrmsg\x18\x02 \x01(\t\x12\x10\n\x08username\x18\x03 \x01(\t\x12\x0b\n\x03sex\x18\x04 \x01(\x05\"H\n\x11SaveFormidRequest\x12\x11\n\tclient_id\x18\x01 \x01(\t\x12\x10\n\x08username\x18\x02 \x01(\t\x12\x0e\n\x06\x66ormid\x18\x03 \x01(\t\"5\n\x12SaveFormidResponse\x12\x0f\n\x07\x65rrcode\x18\x01 \x01(\r\x12\x0e\n\x06\x65rrmsg\x18\x02 \x01(\t\"\xde\x01\n\x0eSendMsgRequest\x12\x11\n\tclient_id\x18\x01 \x01(\t\x12\x13\n\x0btemplate_id\x18\x02 \x01(\t\x12\x0f\n\x07to_user\x18\x03 \x01(\t\x12\x0f\n\x07type_id\x18\x04 \x01(\t\x12\x13\n\x0bsubmit_time\x18\x05 \x01(\x05\x12\x0e\n\x06marker\x18\x06 \x01(\t\x12\x0e\n\x06reason\x18\x07 \x01(\t\x12\x33\n\tdata_list\x18\x08 \x03(\x0b\x32 .MiniApp.SendMsgRequest.DataList\x1a\x18\n\x08\x44\x61taList\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t\"2\n\x0fSendMsgResponse\x12\x0f\n\x07\x65rrcode\x18\x01 \x01(\r\x12\x0e\n\x06\x65rrmsg\x18\x02 \x01(\t\"8\n\x12RecvWxEventRequest\x12\x11\n\tclient_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\"6\n\x13RecvWxEventResponse\x12\x0f\n\x07\x65rrcode\x18\x01 \x01(\r\x12\x0e\n\x06\x65rrmsg\x18\x02 \x01(\t\"J\n\x10PushWxMsgRequest\x12\x11\n\tclient_id\x18\x01 \x01(\t\x12\x0e\n\x06msg_id\x18\x02 \x01(\t\x12\x13\n\x0btemplate_id\x18\x03 \x01(\t\"4\n\x11PushWxMsgResponse\x12\x0f\n\x07\x65rrcode\x18\x01 \x01(\r\x12\x0e\n\x06\x65rrmsg\x18\x02 \x01(\t\":\n\x14GetTemplateIdRequest\x12\x11\n\tclient_id\x18\x01 \x01(\t\x12\x0f\n\x07type_id\x18\x02 \x01(\t\"\xde\x01\n\x15GetTemplateIdResponse\x12\x0f\n\x07\x65rrcode\x18\x01 \x01(\r\x12\x0e\n\x06\x65rrmsg\x18\x02 \x01(\t\x12;\n\x04\x64\x61ta\x18\x03 \x03(\x0b\x32-.MiniApp.GetTemplateIdResponse.TemplateIdInfo\x1ag\n\x0eTemplateIdInfo\x12\x11\n\tclient_id\x18\x01 \x01(\t\x12\x0f\n\x07type_id\x18\x02 \x01(\t\x12\x13\n\x0btemplate_id\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0e\n\x06marker\x18\x05 \x01(\t2\x9d\x04\n\x12XiaoChengXuService\x12\x38\n\x05login\x12\x15.MiniApp.LoginRequest\x1a\x16.MiniApp.LoginResponse\"\x00\x12L\n\rget_user_info\x12\x1b.MiniApp.GetUserInfoRequest\x1a\x1c.MiniApp.GetUserInfoResponse\"\x00\x12H\n\x0bsave_formid\x12\x1a.MiniApp.SaveFormidRequest\x1a\x1b.MiniApp.SaveFormidResponse\"\x00\x12L\n\rrecv_wx_event\x12\x1b.MiniApp.RecvWxEventRequest\x1a\x1c.MiniApp.RecvWxEventResponse\"\x00\x12\x46\n\x0bpush_wx_msg\x12\x19.MiniApp.PushWxMsgRequest\x1a\x1a.MiniApp.PushWxMsgResponse\"\x00\x12R\n\x0fget_template_id\x12\x1d.MiniApp.GetTemplateIdRequest\x1a\x1e.MiniApp.GetTemplateIdResponse\"\x00\x12K\n\x14send_template_notice\x12\x17.MiniApp.SendMsgRequest\x1a\x18.MiniApp.SendMsgResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'protoc_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_LOGINREQUEST']._serialized_start=25
  _globals['_LOGINREQUEST']._serialized_end=72
  _globals['_LOGINRESPONSE']._serialized_start=75
  _globals['_LOGINRESPONSE']._serialized_end=239
  _globals['_JSCODE2SESSIONINFO']._serialized_start=241
  _globals['_JSCODE2SESSIONINFO']._serialized_end=334
  _globals['_ACCESSTOKENREQUEST']._serialized_start=336
  _globals['_ACCESSTOKENREQUEST']._serialized_end=375
  _globals['_ACCESSTOKENRESPONS']._serialized_start=377
  _globals['_ACCESSTOKENRESPONS']._serialized_end=493
  _globals['_GETUSERINFOREQUEST']._serialized_start=495
  _globals['_GETUSERINFOREQUEST']._serialized_end=533
  _globals['_GETUSERINFORESPONSE']._serialized_start=535
  _globals['_GETUSERINFORESPONSE']._serialized_end=620
  _globals['_SAVEFORMIDREQUEST']._serialized_start=622
  _globals['_SAVEFORMIDREQUEST']._serialized_end=694
  _globals['_SAVEFORMIDRESPONSE']._serialized_start=696
  _globals['_SAVEFORMIDRESPONSE']._serialized_end=749
  _globals['_SENDMSGREQUEST']._serialized_start=752
  _globals['_SENDMSGREQUEST']._serialized_end=974
  _globals['_SENDMSGREQUEST_DATALIST']._serialized_start=950
  _globals['_SENDMSGREQUEST_DATALIST']._serialized_end=974
  _globals['_SENDMSGRESPONSE']._serialized_start=976
  _globals['_SENDMSGRESPONSE']._serialized_end=1026
  _globals['_RECVWXEVENTREQUEST']._serialized_start=1028
  _globals['_RECVWXEVENTREQUEST']._serialized_end=1084
  _globals['_RECVWXEVENTRESPONSE']._serialized_start=1086
  _globals['_RECVWXEVENTRESPONSE']._serialized_end=1140
  _globals['_PUSHWXMSGREQUEST']._serialized_start=1142
  _globals['_PUSHWXMSGREQUEST']._serialized_end=1216
  _globals['_PUSHWXMSGRESPONSE']._serialized_start=1218
  _globals['_PUSHWXMSGRESPONSE']._serialized_end=1270
  _globals['_GETTEMPLATEIDREQUEST']._serialized_start=1272
  _globals['_GETTEMPLATEIDREQUEST']._serialized_end=1330
  _globals['_GETTEMPLATEIDRESPONSE']._serialized_start=1333
  _globals['_GETTEMPLATEIDRESPONSE']._serialized_end=1555
  _globals['_GETTEMPLATEIDRESPONSE_TEMPLATEIDINFO']._serialized_start=1452
  _globals['_GETTEMPLATEIDRESPONSE_TEMPLATEIDINFO']._serialized_end=1555
  _globals['_XIAOCHENGXUSERVICE']._serialized_start=1558
  _globals['_XIAOCHENGXUSERVICE']._serialized_end=2099
# @@protoc_insertion_point(module_scope)
