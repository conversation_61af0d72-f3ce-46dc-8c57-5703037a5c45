from typing import Any, Dict, List


class ResponseDataFilter:
    """响应数据脱敏过滤器"""
    
    # 需要脱敏的字段名
    SENSITIVE_FIELDS = [
        'phone', 'mobile', 'telephone', 'tel',
        'contacts', 'contact', 'contact_person',
        'address', 'addr', 'location',
        'building_num', 'building_number',
        'collector', 'collector_phone',
        'id_card', 'identity_card', 'card_no', 'admin',
        'email', 'mail', 'transport_company_admin', 'other_transport_company_admin',
        'other_transport_company_phone',
        'liabler', 'official_address', 'principal',
        'bank_card', 'card_number',  # 银行卡号
    ]
    
    @classmethod
    def filter_sensitive_data(cls, data: Any) -> Any:
        """递归过滤敏感数据"""
        if isinstance(data, dict):
            return cls._filter_dict(data)
        elif isinstance(data, list):
            return cls._filter_list(data)
        else:
            return data
    
    @classmethod
    def _filter_dict(cls, data: Dict) -> Dict:
        """过滤字典数据"""
        filtered_data = {}
        for key, value in data.items():
            if key.lower() in cls.SENSITIVE_FIELDS:
                filtered_data[key] = cls._mask_field_value(key.lower(), value)
            elif isinstance(value, (dict, list)):
                filtered_data[key] = cls.filter_sensitive_data(value)
            else:
                filtered_data[key] = value
        return filtered_data
    
    @classmethod
    def _filter_list(cls, data: List) -> List:
        """过滤列表数据"""
        return [cls.filter_sensitive_data(item) for item in data]
    
    @classmethod
    def _mask_field_value(cls, field_name: str, value: Any) -> str:
        """根据字段类型进行脱敏处理"""
        if not value:
            return value
            
        # 转换为字符串处理
        if not isinstance(value, str):
            value = str(value)
            
        value = value.strip()
        if not value:
            return value
            
        # 手机号脱敏
        if field_name in ['phone', 'mobile', 'telephone', 'tel', 'collector_phone']:
            return cls._mask_phone(value)
        
        # 联系人脱敏
        elif field_name in ['contacts', 'contact', 'contact_person', 'collector', 'admin']:
            return cls._mask_contact(value)
        
        # 地址脱敏
        elif field_name in ['address', 'addr', 'location', 'building_num', 'building_number']:
            return cls._mask_address(value)
        
        # 邮箱脱敏
        elif field_name in ['email', 'mail']:
            return cls._mask_email(value)
        
        # 身份证脱敏
        elif field_name in ['id_card', 'identity_card', 'card_no']:
            return cls._mask_id_card(value)
        
        # 密码和令牌完全隐藏
        elif field_name in ['password', 'pwd', 'passwd', 'token', 'access_token', 'refresh_token']:
            return "***"
        
        # 银行卡号脱敏
        elif field_name in ['bank_card', 'card_number']:
            return cls._mask_bank_card(value)
        
        # 默认脱敏：后4位变成***
        else:
            return cls._mask_default(value)
    
    @classmethod
    def _mask_phone(cls, phone: str) -> str:
        """手机号脱敏"""
        if len(phone) == 11:
            return phone[:3] + "****" + phone[-4:]
        elif 4 < len(phone) < 11:
            return phone[:2] + "****"
        else:
            return phone
    
    @classmethod
    def _mask_contact(cls, contact: str) -> str:
        """联系人脱敏"""
        if len(contact) > 1:
            return contact[0] + "**"
        else:
            return contact
    
    @classmethod
    def _mask_address(cls, address: str) -> str:
        """地址脱敏"""
        if len(address) > 6:
            return address[:6] + "******"
        else:
            return "******"
    
    @classmethod
    def _mask_email(cls, email: str) -> str:
        """邮箱脱敏"""
        try:
            name, domain = email.split("@")
            if len(name) > 2:
                return f"{name[0]}{'*' * (len(name) - 2)}{name[-1]}@{domain}"
            else:
                return f"{name[0]}*@{domain}"
        except Exception:
            return email
    
    @classmethod
    def _mask_id_card(cls, id_card: str) -> str:
        """身份证脱敏"""
        if len(id_card) >= 8:
            return id_card[:4] + "****" + id_card[-4:]
        else:
            return "****"
    
    @classmethod
    def _mask_bank_card(cls, card: str) -> str:
        """银行卡号脱敏"""
        if len(card) >= 8:
            return card[:4] + "****" + card[-4:]
        else:
            return "****"
    
    @classmethod
    def _mask_default(cls, value: str) -> str:
        """默认脱敏：后4位变成***"""
        if len(value) > 4:
            return value[:-4] + "***"
        else:
            return "***"
