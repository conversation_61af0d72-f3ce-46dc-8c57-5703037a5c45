import base64
import json

from Cryptodome.Cipher import AES

from Base.api import logger


class WXBizDataCrypt(object):
    def __init__(self, app_id, session_key):
        self.appId = app_id
        self.sessionKey = session_key

    def decrypt(self, encrypted_data, iv):
        session_key = base64.b64decode(self.sessionKey)
        encrypted_data = base64.b64decode(encrypted_data)
        iv = base64.b64decode(iv)

        cipher = AES.new(session_key, AES.MODE_CBC, iv)
        data = self._unpad(cipher.decrypt(encrypted_data))

        if not isinstance(data, (bytes, str)):
            raise Exception(f'{data}转码错误')
        elif isinstance(data, bytes):
            result = str(data, encoding='utf-8')
        else:
            result = data

        decrypted = json.loads(result)
        logger.info(f"decrypted:{decrypted}")
        # if decrypted['watermark']['appid'] != self.appId:
        #     raise Exception('Invalid Buffer')

        return decrypted

    def _unpad(self, s):
        return s[:-ord(s[len(s) - 1:])]


xcx_default_info_version = (2, 27, 1)


def is_default_info_by_version(xcx_version):
    """通过小程序基础库版本号判断用户信息是否默认信息
    
    20221109 微信更新接口不再返回用户信息
    https://developers.weixin.qq.com/community/develop/doc/00022c683e8a80b29bed2142b56c01
    """
    
    if not xcx_version:
        return False
    try:
        v1, v2, v3 = xcx_version.split('.')
        version = (int(v1), int(v2), int(v3))
        if version >= xcx_default_info_version:
            return True
        else:
            return False
    except Exception as e:
        pass
    return True


# class WXBizDataCrypt:
#     def __init__(self, appId, sessionKey):
#         self.appId = appId
#         self.sessionKey = sessionKey
#
#     def decrypt(self, encryptedData, iv):
#         # base64 decode
#         sessionKey = base64.b64decode(self.sessionKey)
#         encryptedData = base64.b64decode(encryptedData)
#         iv = base64.b64decode(iv)
#
#         cipher = AES.new(sessionKey, AES.MODE_CBC, iv)
#
#         decrypted = json.loads(self._unpad(cipher.decrypt(encryptedData)))
#
#         if decrypted['watermark']['appid'] != self.appId:
#             raise Exception('Invalid Buffer')
#
#         return decrypted
#
#     def _unpad(self, s):
#         return s[:-ord(s[len(s)-1:])]
