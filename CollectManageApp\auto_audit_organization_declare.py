#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

import copy
import datetime
import json
import threading
import uuid
import time

from django.db import transaction
from django.forms import model_to_dict
from django.http import HttpResponseBadRequest

from Base.api import get_logger
from Base.utils.const import BOTH_NONRESIDENT_RUBBISHES, ConstDeclareType, NonresidentRubbishes
from Base.utils.base import get_coding
from Base.utils.cryptor import sm4_cryptor
from CollectManageApp.models import OrgNonresidentDeclare, AuthorizedOrgRelationId, OrgNonresidentDeclareOperate, \
    OrgNonresidentIssued, AuthorizedOrg, AuthorizedOrgRelationId, OtherOrgNonresidentDeclare, StreetMapScope
from CollectManageApp.models_base import Organization, OrgDetail, CityRegion, OrgType, TransportCompanyArea, \
    OrganizationOther
from .models_other_transport import User as OtherUser, CompanyArea as OtherCompanyArea
from .models_transport import ContractNew
from .scripts import is_tongzhou_other_rubbishes, other_rubbishes_db, windingNumber
from .serializers import OrgNonresidentDeclareSer
from CleanCodeIssuing import issuing
from rpc_client.client import wx_client

logger = get_logger('django')



def org_exists(queryset, org_id, credit_code, rubbishes=''):
    if org_id:
        queryset = queryset.exclude(org_id=org_id)
    queryset = queryset.filter(credit_code=credit_code, is_deleted=0, is_declare=1, logout_status=0)
    if rubbishes:
        queryset = queryset.filter(rubbishes__icontains=rubbishes)
    return queryset.exclude(declare_type=ConstDeclareType.ChildOrg).exists()


def get_org_no_rubbishes_no_org_id(queryset, credit_code):
    """获取别的非居民垃圾类型的非居民主体是否存在"""
    return queryset.filter(
        credit_code=credit_code, is_deleted=0, is_declare=1
    ).exclude(
        declare_type=ConstDeclareType.ChildOrg
    ).first()



def rpc_not_pass(auth_org, reason):
    try:
        if auth_org:
            # rpc连接
            rpc = wx_client()
            now_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(time.time())))
            # 消息推送改为后台任务
            data = {
                'client_id': auth_org.client_id,
                'to_user': auth_org.open_id,
                'type_id': 'NonresidentExamine',
                'marker': 'reject',
                'data_list': [
                    {'data': now_time},
                    {'data': '非居民注册'},
                    {'data': reason}
                ]
            }
            t = threading.Thread(target=rpc.send_template_notice_req, args=[data])
            t.setDaemon(True)
            t.start()
    except Exception as e:
        logger.exception(e)


def rpc_pass(auth_org, declare):
    try:
        if auth_org:
            # rpc连接
            rpc = wx_client()
            now_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(time.time())))
            if declare.create_time:
                now_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(declare.create_time)))
            
            rpc_data = {
                'client_id': '',
                'to_user': 'auth_org.open_id',
                'type_id': 'NonresidentExamine',
                'marker': 'pass',  # 通过
                'reason': '无',
                'data_list': [{'data': now_time},
                              {'data': '点击查看'},
                              {'data': '审核通过'}
                              ]
            }
            if auth_org.client_id:
                rpc_data['client_id'] = auth_org.client_id
            if auth_org.open_id:
                rpc_data['to_user'] = auth_org.open_id
            if declare.reason:
                rpc_data['reason'] = declare.reason
            rpc = wx_client()
            # rpc.send_template_notice_req(
            #     rpc_data
            # )
            # 消息推送改为后台任务
            data = rpc_data
            t = threading.Thread(target=rpc.send_template_notice_req, args=[data])
            t.setDaemon(True)
            t.start()
    except Exception as e:
        logger.exception(e)




def audit(data, role, area_coding, street_coding, relation_id, username, is_auto_audit=False):
    declare_id = data.get('declare_id')
    status = data.get('status')
    reason = data.get('reason', '')

    queryset = OrgNonresidentDeclare.objects.filter(declare_id=declare_id)
    if role == 'AreaManager':
        queryset = queryset.filter(area_coding=area_coding)
    elif role == 'StreetManager':
        queryset = queryset.filter(street_coding=street_coding)
    elif role == 'Interface':
        pass
    else:
        queryset = queryset.none()
    declare = queryset.first()
    if not declare:
        return {'code': 4005, 'msg': '主体不存在.'}
    # is_tz = declare.area_coding == '110112000000'
    is_tz = False
    declare_rubbishes = declare.rubbishes
    db = other_rubbishes_db(declare.area_coding, declare_rubbishes)
    if declare.status != 1:
        msg_map = {
            0: '该主体未提交审核.',
            2: '该主体审核已通过审核，不可再次审核.',
            3: '该主体审核未通过审核，不可再次审核.',
        }
        return {'code': 4005, 'msg': msg_map.get(declare.status)}
    if int(status) not in (2, 3):
        return {'code': 400, 'msg': '无效的审核状态.'}
    if int(status) in (3,) and not reason:
        return {'code': 400, 'msg': '请填写审核不通过原因.'}

    timestamp = int(datetime.datetime.now().timestamp())
    declare.audit_time = timestamp
    declare.auditor = relation_id
    declare.auditor_username = username
    declare.reason = reason
    declare.status = status

    #获取关联用户的openid
    auth_org_queryset = AuthorizedOrg.objects.all()
    auth_org = ''
    open_id = ''
    try:
        if declare.auto_audit == 0 and declare.is_status_succeed == 0:
            auth_org = auth_org_queryset.filter(relation_id=declare.creator).first()
        elif declare.auto_audit == 0 and declare.is_status_succeed == 1:
            auth_mapping = AuthorizedOrgRelationId.objects.filter(org_id=declare.org_id).first()
            auth_org = auth_org_queryset.filter(relation_id=auth_mapping.authorized_org_relation_id).first()
    except Exception as e:
        logger.exception(e)


    if int(status) == 3:
        # TODO: 不通过
        rpc_not_pass(auth_org, reason)
        declare.save()
        return model_to_dict(declare)
    
    original_data = model_to_dict(declare)
    declare_type = declare.declare_type or ConstDeclareType.Normal
    
    if declare_type == ConstDeclareType.Normal:
        # 如果当前非居民主体没有org_id，但是已经注册过相同垃圾类型的非居民，停止下面逻辑
        check_org_queryset = Organization.objects.all()
        org = org_exists(check_org_queryset, declare.org_id, declare.credit_code, declare_rubbishes)
        if not org:
            check_org_queryset = Organization.objects.using("ljfl_declare_db").all()
            org = org_exists(check_org_queryset, declare.org_id, declare.credit_code)
        if org:
            return {'code': 400, 'msg': '该主体已被注册,信用代码重复.'}


    try:
        with transaction.atomic(using='ljfl_db'):
            with transaction.atomic(using='ljfl_declare_db'):
                credit_code = declare.credit_code
                original_credit_code = declare.credit_code
                # 没有org_id，并且是重复共享模式，生成虚拟的统一信用代码
                # 有org_id，表示是有一种垃圾类型，补充另一种的情况
                if not declare.is_status_succeed and not declare.org_id:
                    if declare_type in [ConstDeclareType.NoCredit, ConstDeclareType.RepeatCredit]:
                        credit = issuing.get_clean_code('ORG', 'CREDIT_NO', declare_id)
                        if credit['code'] != 200:
                            raise HttpResponseBadRequest
                        credit_code = credit['data']['credit_no']
                declare_data = model_to_dict(declare, exclude=('id',
                                                               'declare_id',
                                                               'status',
                                                               'reason',
                                                               'audit_time',
                                                               'update_time',
                                                               'creator',
                                                               'auditor',
                                                               'auditor_username',
                                                               'auto_audit',
                                                               'no_credit_reason',
                                                               'is_status_succeed',
                                                               'restaurant_trash_rfid',
                                                               'clean_code',
                                                               'clean_no',
                                                               'have_contract',
                                                               'check_type',
                                                               'logout_reson',
                                                               'logout_content',
                                                               'add_another_type'
                                                               ))
                if not declare.is_status_succeed:
                    declare_data['restaurant_trash_rfid'] = 0
                    declare_data['have_contract'] = 0
                    declare_data['clean_code'] = ''
                    declare_data['clean_no'] = ''
                is_other_rubbishes = declare.rubbishes == NonresidentRubbishes.OTHER

                if not is_tz:
                    # declare_data数据转化
                    if is_other_rubbishes:
                        # 转换成其他垃圾数据
                        declare_data['other_trash_120'] = declare_data.pop('restaurant_trash_120', 0)
                        declare_data['other_trash_240'] = declare_data.pop('restaurant_trash_240', 0)
                        declare_data['other_trash'] = declare_data.pop('restaurant_trash', 0)
                        declare_data['other_trash_rfid'] = declare_data.pop('restaurant_trash_rfid', 0)
                        declare_data['other_predict_weight'] = declare_data.pop('restaurant_predict_weight', 0)
                        declare_data['other_transport_company_id'] = declare_data.pop('transport_company_id', '')
                        declare_data['other_have_contract'] = declare_data.pop('have_contract', 0)
                declare_data['credit_code'] = credit_code
                current_data = copy.deepcopy(declare_data)
                floor_area = declare_data.pop('floor_area')
                loating = declare_data.pop('loating')
                org_id = declare_data['org_id']
                if not org_id:
                    org_id = uuid.uuid1().hex
                declare_data['org_id'] = org_id
                declare_data['update_time'] = timestamp
                declare_data['is_declare'] = 1
                declare_data['is_deleted'] = 0
                declare_data['remark'] = 'nonresident'
                declare_data['examine_org'] = 0
                declare_data['non_resident_mark'] = 1
                if not declare.is_status_succeed:
                    declare_data['create_time'] = timestamp

                # 修改主体信息
                org_queryset = Organization.objects.using(db).filter(org_id=org_id, is_deleted=0, is_declare=1)
                org = org_queryset.first()
                if org:
                    declare_data.pop('clean_code', '')
                    declare_data.pop('clean_no', '')
                    org_old_rubbishes = org.rubbishes
                    update_org_data = dict()
                    for k, v in declare_data.items():
                        update_org_data[k] = v
                    if org_old_rubbishes == BOTH_NONRESIDENT_RUBBISHES or org_old_rubbishes != declare_rubbishes:
                        update_org_data['rubbishes'] = BOTH_NONRESIDENT_RUBBISHES
                    if org.logout_status == 1:
                        # 原来的主体已经注销，修改原来主体的状态为未注销，清除相关的主体数据
                        if org_old_rubbishes == NonresidentRubbishes.RESTAURANTS:
                            update_org_data['restaurant_trash_120'] = 0
                            update_org_data['restaurant_trash_240'] = 0
                            update_org_data['restaurant_trash'] = 0
                            update_org_data['restaurant_trash_rfid'] = 0
                            update_org_data['restaurant_predict_weight'] = 0
                            update_org_data['transport_company_id'] = ''
                            update_org_data['have_contract'] = 0
                        elif org_old_rubbishes == NonresidentRubbishes.OTHER:
                            update_org_data['other_trash_120'] = 0
                            update_org_data['other_trash_240'] = 0
                            update_org_data['other_trash'] = 0
                            update_org_data['other_trash_rfid'] = 0
                            update_org_data['other_predict_weight'] = 0
                            update_org_data['other_transport_company_id'] = ''
                            update_org_data['other_have_contract'] = 0
                        update_org_data['logout_status'] = 0
                        update_org_data['rubbishes'] = declare_rubbishes
                        update_org_data['logout_time'] = None
                    # 如果主体原来已存在，则更新原来主体的排放登记代码到当前类型的数据上
                    if not declare.clean_code and not declare.clean_no:
                        declare.clean_code = org.clean_code
                        declare.clean_no = org.clean_no

                    # 更新公司信息
                    transport_company_id = declare.transport_company_id
                    if is_other_rubbishes:
                        old_transport_company_id = org.other_transport_company_id
                    else:
                        old_transport_company_id = org.transport_company_id
                    if transport_company_id != old_transport_company_id:
                        change_org_data = {}
                        if is_other_rubbishes:
                            update_org_data['other_have_contract'] = 0
                        else:
                            update_org_data['have_contract'] = 0
                        declare.have_contract = 0
                    else:
                        if is_other_rubbishes:
                            update_org_data['other_have_contract'] = declare.have_contract
                        else:
                            update_org_data['have_contract'] = declare.have_contract

                    org_queryset.update(
                        **update_org_data
                    )

                    # 修改合同信息
                    contract_old_queryset = ContractNew.objects.filter(org_id=org_id, is_delete=0, rubbishes=declare_rubbishes)
                    if contract_old_queryset.exists():
                        if transport_company_id != old_transport_company_id:
                            contract_old_queryset.filter(company_id=old_transport_company_id, status=3).update(contract_status=3)
                        street_coding = declare_data['street_coding']
                        street_name = CityRegion.objects.filter(coding=street_coding).values_list('name', flat=True)
                        street_name = street_name[0] if street_name and street_name[0] else ''
                        contract_old_queryset.update(
                            service_area=declare_data['area_coding'],
                            street_coding=street_coding,
                            detaile_address=declare_data['address'],
                            food_license=declare_data['permission_code'],
                            organization=declare_data['name'],
                            credit_code=declare_data['credit_code'],
                            street_name=street_name
                        )
                else:
                    org_old_rubbishes = declare_rubbishes
                    org = Organization.objects.using(db).create(**declare_data)

                if declare_type == ConstDeclareType.RepeatCredit:
                    # 更新同社会信用代码主体申报类型
                    Organization.objects.using(db).filter(credit_code=original_credit_code, is_declare=1).update(
                        declare_type=ConstDeclareType.RepeatCredit
                    )
                    # 有通过历史的申报记录更改申报类型
                    OrgNonresidentDeclare.objects.filter(credit_code=original_credit_code,
                                                         is_status_succeed=1).update(
                        declare_type=ConstDeclareType.RepeatCredit
                    )
                if not is_tz or (is_tz and NonresidentRubbishes.RESTAURANTS in declare.rubbishes):
                    org_detail = OrgDetail.objects.filter(org_id=org_id, is_deleted=0).first()
                    detail_data = {
                        'org_id': org_id,
                        'floor_area': floor_area,
                        'loating': loating,
                        'is_deleted': 0
                    }
                    if org_detail:
                        for k, v in detail_data.items():
                            setattr(org_detail, k, v)
                        org_detail.save()
                    else:
                        OrgDetail.objects.create(**detail_data)

                if not declare.is_status_succeed and declare.creator:
                    if not AuthorizedOrgRelationId.objects.filter(authorized_org_relation_id=declare.creator, org_id=org_id).exists():
                        AuthorizedOrgRelationId.objects.create(**{
                            'authorized_org_relation_id': declare.creator,
                            'org_id': org_id,
                            'is_admin': 1,
                            'create_time': datetime.datetime.now()
                        })

                # 如果更新过，更改推送状态
                if declare.is_status_succeed:
                    # todo 三方推送可能需要改 非居民类型
                    OrgNonresidentIssued.objects.filter(org_id=org_id).update(**dict(
                        transport_company_id=declare.transport_company_id,
                        status=0
                    ))

                declare.credit_code = credit_code
                declare.org_id = org_id
                declare.is_status_succeed = 1
                declare.save()
                # TODO: 通过
                rpc_pass(auth_org, declare)

    except Exception as e:
        logger.exception(e)
        return {'code': 400, 'msg': '审核失败,请重新审核.'}

    declare_operate_log(declare.declare_id,
                        declare.org_id,
                        current_data)
    return model_to_dict(declare)


class ConstOperateType:
    # 审核通过
    AuditSuccess = 'AUDIT_SUCCESS'
    # 修改
    Modify = 'MODIFY'


def declare_operate_log(declare_id, org_id, current):
    """
    非居民责任主体操作日志
    :param declare_id: 申报uuid
    :param org_id: 责任主体uuid
    :param current: 提交的数据
    :return:
    """
    log = OrgNonresidentDeclareOperate.objects.filter(declare_id=declare_id).order_by('-id').first()
    original_change_data = json.loads(log.current_data) if log else []
    operate_type = ConstOperateType.AuditSuccess if not log else ConstOperateType.Modify
    original = {c['field']: c['current'] for c in original_change_data}
    current_data = {key: value for key, value in current.items() if hasattr(OrgNonresidentDeclare, key)}
    current = OrgNonresidentDeclareSer(OrgNonresidentDeclare(**current_data))
    change_data = []
    compare_schema = dict(
        area_name='区',
        street_name='街道',
        comm_name='社区',
        org_sub_type_name='单位类型',

        name='门头名称',
        address='单位详细地址',

        official_org_name='单位名称',
        official_address='单位注册地址',
        credit_code='统一社会信用代码',
        permission_code='食品经营许可证编号',

        liabler='管理责任人',
        contacts='联系人',
        phone='联系电话',

        mam_type='经营形式',
        mam_subtype='经营子类型',
        floor_area='营业面积(m²)',
        loating='日就餐规模(人)',

        cover='门头照片',

        is_reduce_device='是否有减量套件',
        reduce_water_device='控水控杂设备套数',
        reduce_oil_device='油水分离设备套数',

        is_local_device='是否有就地处理设施',
        local_factory='设备厂家',
        local_capacity='日处理能力(公斤)',
        local_process='处理工艺',

        local_out_solid_weight='产物(公斤/天)',
        local_out_water_weight='废水(公斤/天)',
        local_out_dregs_weight='废渣(公斤/天)',

        restaurant_trash_120='120L',
        restaurant_trash_240='240L',
        restaurant_predict_weight='预估量(日)',

        transport_company_name='收运公司',

        # three_guarantees_agreement='门前三包责任书',
        # three_guarantees_board='门前三包责任图',
        rubbishes='垃圾类型',
    )
    save_current_data = []
    for field, title in compare_schema.items():
        o_value = original.get(field, '') or ''
        c_value = current.data.get(field, '') or ''
        if field == "phone" and o_value:
            # 解密
            try:
                o_value = sm4_cryptor.decrypt(o_value)
            except Exception as e:
                pass
        if field == "phone" and c_value:
            # 解密
            try:
                c_value = sm4_cryptor.decrypt(c_value)
            except Exception as e:
                pass
        row = dict(
            field=field,
            title=title,
            current=c_value
        )
        save_current_data.append(row)
        if operate_type == ConstOperateType.AuditSuccess or str(o_value) != str(c_value):
            row['original'] = o_value

            change_data.append(row)

    OrgNonresidentDeclareOperate.objects.create(**dict(
        declare_id=declare_id,
        org_id=org_id,
        operate_type=operate_type,
        current_data=json.dumps(save_current_data, ensure_ascii=False),
        change_data=json.dumps(change_data, ensure_ascii=False),
        operate_time=datetime.datetime.now()
    ))


def check_declare_exists(
        declare_queryset,
        exists_msg='该社会统一信用代码已注册,请确认统一社会信用代码是否正确，或切换共享模式进行信息申报！',
        audit_msg='该统一社会信用代码已存在正在审核中主体，请等待审核通过后再进行信息申报！'
):
    org_last_status = declare_queryset.order_by('-id').values('check_type', 'status', 'is_status_succeed').first()
    if org_last_status:
        check_type = org_last_status.get('check_type') or 0
        status = org_last_status.get('status') or 0
        # 已注销
        logout_status = check_type in [2, 3, 4] and status == 2
        if not logout_status:
            is_status_succeed = org_last_status.get('is_status_succeed') or 0
            if is_status_succeed:
                return {'code': 4005, 'msg': exists_msg}
            if status == 1:
                return {'code': 4006, 'msg': audit_msg}


def declare_data_verify(data, instance=None):
    required_fields = dict(
        area_coding='区编码',
        street_coding='街道编码',
        comm_coding='社区编码',
        org_sub_type_id='单位类型',

        name='门头名称',
        address='单位详细地址',

        official_org_name='单位名称',

        liabler='管理责任人',
        contacts='联系人',
        phone='联系电话',

        cover='门头照片',
    )

    rubbishes = data.get('rubbishes')
    add_another_type = data.get('add_another_type', 0)

    # is_tz = data.get("area_coding") == "110112000000"
    is_tz = False

    if not is_tz:
        default_required_fields = dict(
            restaurant_trash_120='120L桶数量',
            restaurant_trash_240='240L桶数量',
            restaurant_predict_weight='预估量(公斤/日)',

            transport_company_id='收运公司'
        )
        if not add_another_type:
            # 不是新责任主体
            required_fields = default_required_fields
        else:
            required_fields.update(default_required_fields)
        if rubbishes == 'RESTAURANTS':
            # 餐厨
            required_fields.update(
                mam_type='经营形式',
                floor_area='营业面积(m²)',
                loating='日就餐规模(人)',
                is_reduce_device='是否有减量套件',
                is_local_device='是否有就地处理设施',
            )
        elif rubbishes == 'OTHER':
            pass
    else:
        # 通州特殊处理
        if "RESTAURANTS" in rubbishes:
            required_fields.update(
                mam_type='经营形式',
                floor_area='营业面积(m²)',
                loating='日就餐规模(人)',

                is_reduce_device='是否有减量套件',

                is_local_device='是否有就地处理设施',

                restaurant_trash_120='120L桶数量',
                restaurant_trash_240='240L桶数量',
                restaurant_predict_weight='预估量(公斤/日)',

                transport_company_id='收运公司'
            )
        # required_fields["three_guarantees_agreement"] = "门前三包责任书"

    for key, value in required_fields.items():
        if data.get(key) in [None, '']:
            return {'code': 4001, 'msg': f'请提供{value}信息.'}

    comm_coding = data.get('comm_coding', '')
    area_coding = get_coding(comm_coding, 2)
    street_coding = get_coding(comm_coding, 3)
    if data.get('area_coding') != area_coding or data.get('street_coding') != street_coding:
        return {'code': 4002, 'msg': '请提供正确的区域信息.'}

    region = CityRegion.objects.filter(coding=comm_coding, is_deleted=0, grade=4).first()
    if not region:
        return {'code': 4002, 'msg': '请提供正确的社区信息.'}

    org_sub_type_id = data.get('org_sub_type_id')
    org_type = OrgType.objects.filter(parent_id='60b5ef4bef5311ebbe73fa163e3babe8',
                                      org_type_id=org_sub_type_id,
                                      is_deleted=0).first()
    if not org_type:
        return {'code': 4003, 'msg': '请提供正确的单位类型.'}

    declare_type = data.get('declare_type', ConstDeclareType.Normal)
    credit_code = data.get('credit_code')
    no_credit_reason = data.get('no_credit_reason', '')
    org_id = data.get('org_id')

    if not instance or not instance.is_status_succeed:
        if declare_type in [ConstDeclareType.Normal, ConstDeclareType.RepeatCredit]:
            if not credit_code:
                return {'code': 4001, 'msg': '统一信用代码必填.'}
        elif declare_type in [ConstDeclareType.NoCredit]:
            if not no_credit_reason:
                return {'code': 4004, 'msg': '请提供无法提供统一社会信用代码的原因.'}

        if declare_type in [ConstDeclareType.Normal]:
            rubbishes_list = [NonresidentRubbishes.RESTAURANTS, NonresidentRubbishes.OTHER]
            if add_another_type and org_id:
                # 已经有一种垃圾类型，补充另一种的时候, 判断是否已经存在
                queryset = OrgNonresidentDeclare.objects.filter(org_id=org_id, rubbishes=rubbishes).exclude(check_type__in=[2, 3, 4], status=3)
                error = check_declare_exists(queryset, exists_msg='该主体垃圾类型已经注册', audit_msg='该主体垃圾类型已经注册正在审核中，请等待审核通过后再进行信息申报！')
                if error:
                    return error
                if not is_tz:
                    rubbishes_list = [rubbishes]
            # 判断每种状态最新一条的状态
            for rubbishes_ in rubbishes_list:
                declare_queryset = OrgNonresidentDeclare.objects.filter(credit_code=credit_code).exclude(check_type__in=[2, 3, 4], status=3)
                declare_queryset = declare_queryset.filter(rubbishes=rubbishes_)
                error = check_declare_exists(declare_queryset)
                if error:
                    return error

    longitude = data.get('longitude', 0)
    latitude = data.get('latitude', 0)
    if not all([longitude, latitude]):
        return {'code': 4007, 'msg': '请提供有效的GPS信息.'}

    if is_tongzhou_other_rubbishes(data.get('area_coding'), data.get('rubbishes')):
        return

    transport_company_id = data.get('transport_company_id')
    company_area_queryset = TransportCompanyArea.objects.filter(
        transport_company_id=transport_company_id,
        area_coding=area_coding,
        is_deleted=0,
    )
    if not is_tz:
        company_area = company_area_queryset.filter(rubbishes=rubbishes).first()
    else:
        company_area = company_area_queryset.first()
    if not company_area or street_coding not in company_area.street_coding:
        return {'code': 4008, 'msg': '该清运单位收运范围不包括该区域,请提供有效的清运单位.'}

    if rubbishes == NonresidentRubbishes.OTHER:
        return
    # 餐厨才有下面判断
    mam_type_mapping = {
        '普通餐饮(A)': ['', '特大型餐饮(A1)', '大型餐饮(A2)', '中型餐饮(A3)', '小型餐饮(A4)', '微型餐饮(A5)'],
        '单位食堂(B)': ['', '学生食堂(B1)', '托幼机构食堂(B2)', '职工食堂(B3)', '工地食堂(B4)', '养老机构食堂(B5)', '其他食堂(B6)'],
        '中央厨房(C)': ['中央厨房(C)'],
        '集体用餐配送单位(D)': ['集体用餐配送单位(D)'],
        '其他(E)': ['其他(E)'],
    }
    mam_type = data.get('mam_type')
    mam_subtype = data.get('mam_subtype')
    if mam_type not in mam_type_mapping.keys():
        return {'code': 4009, 'msg': '请提供正确的经营形式.'}
    if mam_subtype not in mam_type_mapping.get(mam_type):
        return {'code': 4010, 'msg': '请提供正确的经营形式子类型.'}


def other_declare_data_verify(data, instance=None):
    # 开发区 其他垃圾非居民
    required_fields = dict(
        area_coding='区编码',
        street_coding='街道编码',
        comm_coding='社区编码',
        org_sub_type_id='单位类型',

        name='门头名称',
        address='单位详细地址',

        official_org_name='单位名称',

        liabler='管理责任人',
        contacts='联系人',
        phone='联系电话',

        mam_type='经营形式',
        floor_area='营业面积(m²)',
        loating='日就餐规模(人)',

        cover='门头照片',

        # is_reduce_device='是否有减量套件',

        # is_local_device='是否有就地处理设施',

        other_trash_120='其他垃圾120L桶数量',
        other_trash_240='其他垃圾240L桶数量',
        other_predict_weight='其他垃圾预估量(公斤/日)',

        # transport_company_id='收运公司'
    )
    if not data.get("transport_company_id"):
        required_fields["transport_company_info"] = "收运公司信息"
    else:
        required_fields["transport_company_id"] = "收运公司id"

    for key, value in required_fields.items():
        if data.get(key) in [None, '']:

            return {'code': 4001, 'msg': f'请提供{value}信息.'}

    comm_coding = data.get('comm_coding', '')
    area_coding = get_coding(comm_coding, 2)
    street_coding = get_coding(comm_coding, 3)
    if data.get('area_coding') != area_coding or data.get('street_coding') != street_coding:
        return {'code': 4002, 'msg': '请提供正确的区域信息.'}

    region = CityRegion.objects.filter(coding=comm_coding, is_deleted=0).first()
    if not region:
        return {'code': 4002, 'msg': '请提供正确的区域信息.'}

    org_sub_type_id = data.get('org_sub_type_id')
    org_type = OrgType.objects.filter(parent_id='60b5ef4bef5311ebbe73fa163e3babe8',
                                      org_type_id=org_sub_type_id,
                                      is_deleted=0).first()
    if not org_type:
        return {'code': 4003, 'msg': '请提供正确的单位类型.'}

    declare_type = data.get('declare_type', ConstDeclareType.Normal)
    credit_code = data.get('credit_code')
    no_credit_reason = data.get('no_credit_reason', '')
    if not instance or not instance.is_status_succeed:
        if declare_type in [ConstDeclareType.Normal, ConstDeclareType.RepeatCredit]:
            if not credit_code:
                return {'code': 4001, 'msg': '统一信用代码必填.'}
        elif declare_type in [ConstDeclareType.NoCredit]:
            if not no_credit_reason:
                return {'code': 4004, 'msg': '请提供无法提供统一社会信用代码的原因.'}

        if declare_type in [ConstDeclareType.Normal]:
            _org = OtherOrgNonresidentDeclare.objects.filter(credit_code=credit_code)
            logout_org = _org.filter(check_type__in=[2, 3, 4], status=2).first()
            if not logout_org:
                org = _org.filter(is_status_succeed=1).first()
                if org:
                    return {'code': 4005, 'msg': '该社会统一信用代码已注册,请确认统一社会信用代码是否正确，或切换共享模式进行信息申报！'}
                is_org = _org.filter(status=1).first()
                if is_org:
                    return {'code': 4006, 'msg': '该统一社会信用代码已存在正在审核中主体，请等待审核通过后再进行信息申报！'}

    longitude = data.get('longitude', 0)
    latitude = data.get('latitude', 0)
    if not all([longitude, latitude]):
        return {'code': 4007, 'msg': '请提供有效的GPS信息.'}

    transport_company_id = data.get('transport_company_id')
    if transport_company_id:
        company_area = OtherCompanyArea.objects.filter(company_id=transport_company_id,
                                                           area_coding=area_coding,
                                                           is_delete=0).first()
        if not company_area or street_coding not in company_area.street_coding:
            return {'code': 4008, 'msg': '该清运单位收运范围不包括该区域,请选择有效的清运单位.'}

    mam_type_mapping = {
        '普通餐饮(A)': ['', '特大型餐饮(A1)', '大型餐饮(A2)', '中型餐饮(A3)', '小型餐饮(A4)', '微型餐饮(A5)'],
        '单位食堂(B)': ['', '学生食堂(B1)', '托幼机构食堂(B2)', '职工食堂(B3)', '工地食堂(B4)', '养老机构食堂(B5)', '其他食堂(B6)'],
        '中央厨房(C)': ['中央厨房(C)'],
        '集体用餐配送单位(D)': ['集体用餐配送单位(D)'],
        '其他(E)': ['其他(E)'],
    }
    mam_type = data.get('mam_type')
    mam_subtype = data.get('mam_subtype')
    if mam_type not in mam_type_mapping.keys():
        return {'code': 4009, 'msg': '请提供正确的经营形式.'}
    if mam_subtype not in mam_type_mapping.get(mam_type):
        return {'code': 4010, 'msg': '请提供正确的经营形式子类型.'}


def other_audit(data, role, area_coding, street_coding, relation_id, username, is_auto_audit=False):
    # 开发区 其他垃圾非居民
    declare_id = data.get('declare_id')
    status = data.get('status')
    reason = data.get('reason', '')

    queryset = OtherOrgNonresidentDeclare.objects.filter(declare_id=declare_id)
    if role == 'AreaManager':
        queryset = queryset.filter(area_coding=area_coding)
    elif role == 'StreetManager':
        queryset = queryset.filter(street_coding=street_coding)
    elif role == 'Qualification':
        company_uid = OtherUser.objects.filter(user_status=1, relation_id=relation_id).first().company_uid
        queryset = queryset.filter(transport_company_id=company_uid)
    elif role == 'Interface':
        pass
    else:
        queryset = queryset.none()

    declare = queryset.first()
    if not declare:
        return {'code': 4005, 'msg': '主体不存在.'}

    if declare.status not in [1, 4]:
        msg_map = {
            0: '该主体未提交审核.',
            2: '该主体审核已通过审核，不可再次审核.',
            3: '该主体审核未通过审核，不可再次审核.',
            4: '该主体审核未通过公司审核.',
        }
        return {'code': 4005, 'msg': msg_map.get(declare.status)}
    if int(status) not in (2, 3, 4):
        return {'code': 400, 'msg': '无效的审核状态.'}
    if int(status) in (3,) and not reason:
        return {'code': 400, 'msg': '请填写审核不通过原因.'}

    timestamp = int(datetime.datetime.now().timestamp())
    declare.audit_time = timestamp
    declare.auditor = relation_id
    declare.auditor_username = username
    declare.reason = reason
    declare.status = status

    #获取关联用户的openid
    auth_org_queryset = AuthorizedOrg.objects.all()
    auth_org = ''
    open_id = ''
    try:
        if declare.auto_audit == 0 and declare.is_status_succeed == 0:
            auth_org = auth_org_queryset.filter(relation_id=declare.creator).first()
        elif declare.auto_audit == 0 and declare.is_status_succeed == 1:
            auth_mapping = AuthorizedOrgRelationId.objects.filter(org_id=declare.org_id).first()
            auth_org = auth_org_queryset.filter(relation_id=auth_mapping.authorized_org_relation_id).first()
    except Exception as e:
        logger.exception(e)

    if int(status) == 3:
        # 注册申请 驳回不通过
        try:
            if auth_org:
                # rpc连接
                rpc = wx_client()
                now_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(time.time())))
                rpc.send_template_notice_req(
                    {
                    'client_id': auth_org.client_id,
                     'to_user': auth_org.open_id,
                    'type_id':'NonresidentExamine',
                    'marker': 'reject',
                    'data_list': [{'data': now_time},
                                  {'data': '非居民注册'},
                                  {'data': reason}
                                  ]
                    }
                )
        except Exception as e:
            logger.exception(e)
        declare.save()
        print("***********")
        return model_to_dict(declare)
    if int(status) == 4:
        # 公司审核通过
        declare.save()
        return model_to_dict(declare)
    # 以下为街道审核通过

    original_data = model_to_dict(declare)
    declare_type = declare.declare_type or ConstDeclareType.Normal

    if declare_type == ConstDeclareType.Normal:
        org = OrganizationOther.objects.exclude(org_id=declare.org_id) \
            .filter(credit_code=declare.credit_code, is_deleted=0, is_declare=1, logout_status=0).\
            exclude(declare_type=ConstDeclareType.ChildOrg).first()
        if org:
            return {'code': 400, 'msg': '该主体已被注册,信用代码重复.'}

    try:
        with transaction.atomic(using='ljfl_db'):
            with transaction.atomic(using='ljfl_declare_db'):
                credit_code = declare.credit_code
                original_credit_code = declare.credit_code
                if not declare.is_status_succeed:
                    if declare_type in [ConstDeclareType.NoCredit, ConstDeclareType.RepeatCredit]:
                        credit = issuing.get_other_clean_code('ORG', 'CREDIT_NO', declare_id)
                        print("credit", credit)
                        if credit['code'] != 200:
                            raise HttpResponseBadRequest
                        credit_code = credit['data']['credit_no']

                declare_data = model_to_dict(declare, exclude=('id',
                                                               'declare_id',
                                                               'status',
                                                               'reason',
                                                               'audit_time',
                                                               'creator',
                                                               'auditor',
                                                               'auditor_username',
                                                               'auto_audit',
                                                               'no_credit_reason',
                                                               'is_status_succeed',
                                                               'restaurant_trash_rfid',
                                                               'clean_code',
                                                               'clean_no',
                                                               'have_contract',
                                                               'check_type',
                                                               'logout_reson',
                                                               'logout_content',
                                                               'transport_company_info',
                                                               'add_another_type'
                                                               ))
                if not declare.is_status_succeed:
                    declare_data['restaurant_trash_rfid'] = 0
                    declare_data['have_contract'] = 0
                    declare_data['clean_code'] = ''
                    declare_data['clean_no'] = ''
                declare_data['credit_code'] = credit_code
                current_data = copy.deepcopy(declare_data)
                floor_area = declare_data.pop('floor_area')
                loating = declare_data.pop('loating')
                car_num = declare_data.pop('car_num')
                contract_start_date = declare_data.pop('contract_start_date')
                contract_end_date = declare_data.pop('contract_end_date')
                contract_pic = declare_data.pop('contract_pic')
                is_declare = 2
                org_id = declare_data['org_id']
                if not org_id:
                    org_id = str(uuid.uuid1()).replace('-', '')
                else:
                    # 查询餐厨是否注册，已注册则使用原org_id的clean_no
                    print("org_id",org_id)
                    canchu_org = OrgNonresidentDeclare.objects.filter(status=2, is_status_succeed=1,
                                                                      org_id=org_id).first()
                    if canchu_org and canchu_org.clean_code:
                        declare_data['clean_code'] = canchu_org.clean_code
                    if canchu_org and canchu_org.clean_no:
                        declare_data['clean_no'] = canchu_org.clean_no

                declare_data['org_id'] = org_id
                declare_data['update_time'] = timestamp
                declare_data['is_declare'] = is_declare
                declare_data['is_deleted'] = 0
                declare_data['remark'] = 'nonresident'
                declare_data['examine_org'] = 0
                declare_data['non_resident_mark'] = 1
                if not declare.is_status_succeed:
                    declare_data['create_time'] = timestamp
                org = OrganizationOther.objects.filter(org_id=org_id, is_deleted=0, is_declare=is_declare).first()
                if org:
                    for k, v in declare_data.items():
                        setattr(org, k, v)
                    org.save()
                else:
                    print(declare_data)
                    OrganizationOther.objects.create(**declare_data)

                if declare_type == ConstDeclareType.RepeatCredit:
                    # 更新同社会信用代码主体申报类型
                    OrganizationOther.objects.filter(credit_code=original_credit_code, is_declare=is_declare).update(
                        declare_type=ConstDeclareType.RepeatCredit
                    )
                    # 有通过历史的申报记录更改申报类型
                    OtherOrgNonresidentDeclare.objects.filter(credit_code=original_credit_code,
                                                         is_status_succeed=1).update(
                        declare_type=ConstDeclareType.RepeatCredit
                    )

                if not declare.is_status_succeed and declare.creator:
                    if not AuthorizedOrgRelationId.objects.filter(authorized_org_relation_id=declare.creator, org_id=org_id).exists():
                        AuthorizedOrgRelationId.objects.create(**{
                            'authorized_org_relation_id': declare.creator,
                            'org_id': org_id,
                            'is_admin': 1,
                            'create_time': datetime.datetime.now()
                        })

                # 如果更新过，更改推送状态
                # if declare.is_status_succeed:
                #     OrgNonresidentIssued.objects.filter(org_id=org_id).update(**dict(
                #         transport_company_id=declare.transport_company_id,
                #         status=0
                #     ))

                declare.credit_code = credit_code
                declare.clean_no = declare_data['clean_no']
                declare.clean_code = declare_data['clean_code']
                declare.org_id = org_id
                declare.is_status_succeed = 1
                declare.save()
                print("审核成功")
                # TODO: 通过
                try:
                    if auth_org:
                        # rpc连接
                        rpc = wx_client()
                        now_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(time.time())))
                        if declare.create_time:
                            now_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(declare.create_time)))

                        rpc_data = {
                            'client_id': '',
                            'to_user': 'auth_org.open_id',
                            'type_id': 'NonresidentExamine',
                            'marker': 'pass',  # 通过
                            'reason': '无',
                            'data_list': [{'data': now_time},
                                          {'data': '点击查看'},
                                          {'data': '审核通过'}
                                          ]
                        }
                        if auth_org.client_id:
                            rpc_data['client_id'] = auth_org.client_id
                        if auth_org.open_id:
                            rpc_data['to_user'] = auth_org.open_id
                        if declare.reason:
                            rpc_data['reason'] = declare.reason
                        rpc = wx_client()
                        rpc.send_template_notice_req(
                            rpc_data
                        )
                except Exception as e:
                    logger.exception(e)

    except Exception as e:
        logger.exception(e)
        return {'code': 400, 'msg': '审核失败,请重新审核.'}
    return model_to_dict(declare)