from __future__ import absolute_import
from Base.api import logger
import json
import requests


class WeChatServeError(Exception):

    def __init__(self, message='', code=500):
        super(WeChatServeError, self).__init__()
        self.code = code
        if not message:
            message = '微信服务调用异常'

        self.message = message


class WeChatServe:
    api_url = 'https://api.weixin.qq.com'

    def send_get_request(self, url, data=None, **kwargs):
        """
        :param url:
        :param data:
        :return:
        """
        try:
            params = self.deal_api_params(data)
            response = requests.get(url, params=params, **kwargs)
        except Exception as e:
            logger.info(f'>>WeChatServe Exception << url: {url} params: {data} error: {str(e)}')
            raise WeChatServeError()

        if response.status_code != 200:
            raise WeChatServeError(code=response.status_code)

        result = json.loads(response.text)
        logger.info(json.dumps(params))
        logger.info(f'>>WeChatServe Code 200 Response {result}]<<')
        # 正常返回data内没有code码
        if result.get('errcode'):
            raise WeChatServeError()

        return result

    def send_post_request(self, url, data=None, **kwargs):
        """
        :param url:
        :param data:
        :return:
        """
        try:
            params = self.deal_api_params(data)
            response = requests.post(url, data=params, **kwargs)
        except Exception as e:
            logger.info(f'>>InternalAuthServe Exception << url: {url} params: {data} error: {str(e)}')
            raise WeChatServeError()

        if response.status_code != 200:
            raise WeChatServeError(code=response.status_code)

        result = json.loads(response.text)
        logger.info(f'>>WeChatServe Response {result}]<<')
        # 正常返回data内没有code码
        if result.get('errcode'):
            raise WeChatServeError()

        return result

    @staticmethod
    def deal_api_params(data):
        """接口参数处理
        :param data:
        :return:
        """
        params = {}
        if data is not None and isinstance(data, dict):
            params = dict((k, v) for k, v in data.items() if v)
        return params

    def get_js_code_session(self, app_id, app_secret, js_code):
        """
        根据js_code 换取 session_key open_id
        :param app_id:
        :param app_secret:
        :param js_code:
        :return:
        """
        spec = {
            "appid": app_id,
            "secret": app_secret,
            'js_code': js_code
        }
        url = self.api_url + '/sns/jscode2session'
        return self.send_get_request(url, spec)
