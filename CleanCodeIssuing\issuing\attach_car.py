#!/usr/bin/python3
# coding: utf-8
from __future__ import absolute_import

from django.core.cache import cache
from django.db import transaction

from ..logger import logger
from .base import BaseCleanCodeGenerator

from CollectManageApp.models_base import Car as TransportCar, CityRegion, TransportCompany, \
    TransportCompanyArea
from CollectManageApp.models_transport import CompanyArea, Car


class AttachCarCleanCodeGenerator(BaseCleanCodeGenerator):
    """
    排放登记编码生成器: 附-收运车辆
    """
    CLEAN_TYPE = 'ATTACH'
    CLEAN_SUBTYPE = 'CAR'

    def generate(self, clean_id):
        # 1. 获取编码前缀
        car = TransportCar.objects.filter(car_id=clean_id, is_declare=1, is_deleted=0).first()
        if not car:
            return dict(code=400, msg='收运车辆不存在.')
        if car.clean_code:
            return dict(code=200, msg='排放登记编码已生成.', data=dict(
                clean_code=car.clean_code,
                clean_no=car.clean_no
            ))
        # 1.1 获取区位编码
        car_region = CityRegion.objects.filter(coding=car.area_coding, grade=2, is_deleted=0).first()
        if not car_region or not car_region.clean_code:
            return dict(code=400, msg='获取区位编码失败.')
        # 1.2 获取收运单位编码
        company = TransportCompany.objects.filter(transport_company_id=car.transport_company_id,
                                                  is_declare=1,
                                                  is_deleted=0).first()
        if not company or not company.clean_code:
            return dict(code=400, msg='获取收运单位编码失败.')
        company_area = TransportCompanyArea.objects.filter(transport_company_id=car.transport_company_id,
                                                           area_coding=car.area_coding,
                                                           is_deleted=0).first()
        if not company_area:
            return dict(code=400, msg='无效的收运范围.')
        # 1.3 获取收运车辆类型编码
        if not car.register_num:
            return dict(code=400, msg='获取车辆类型编码失败.')

        clean_code_prefix = f'{car_region.clean_code}{company.clean_code}{car.register_num}'

        # 2. 锁定编码发号器
        with cache.lock(self.CLEAN_CODE_LOCK_KEY):
            # 2.2 发号逻辑
            try:
                with transaction.atomic(using='ljfl_db'):
                    with transaction.atomic(using='transport_db'):
                        clean_code, clean_no = self._get_clean_code(clean_code_prefix, clean_id)

                        # 修改正式库
                        TransportCar.objects.filter(car_id=clean_id, is_declare=1, is_deleted=0) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))

                        # 修改申报库
                        Car.objects.filter(car_id=clean_id) \
                            .update(**dict(
                            clean_code=clean_code,
                            clean_no=clean_no
                        ))


            except Exception as e:
                logger.exception(e)
                return dict(code=500, msg='排放登记编码发放失败.')

        return dict(code=200, msg='排放登记编码发放成功.', data=dict(
            clean_code=clean_code,
            clean_no=clean_no
        ))
