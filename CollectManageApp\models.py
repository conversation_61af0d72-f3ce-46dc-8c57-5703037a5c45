# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each Foreign<PERSON>ey has `on_delete` set to the desired behavior.
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
import datetime

import uuid
from django.db import models
from django.db.models import Func
from django.utils import timezone

from Base.utils.tools import create_time_uuid


class AuthorizedOrg(models.Model):
    relation_id = models.CharField(max_length=45, blank=True, null=True)
    open_id = models.CharField(max_length=45, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    register_time = models.DateTimeField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    username = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True)
    phone = models.CharField(max_length=11, blank=True, null=True)
    remark = models.CharField(max_length=45, blank=True, null=True)
    wechat_picture = models.CharField(max_length=255, blank=True, null=True)
    wechat_nickname = models.CharField(max_length=255, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    client_id = models.CharField(max_length=45, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'authorized_org'
        app_label = 'ljfl_declare_db'


class OrgNonresidentDeclare(models.Model):
    declare_id = models.CharField(max_length=45)
    declare_type = models.CharField(max_length=45)
    source_type = models.CharField(max_length=45)
    org_id = models.CharField(max_length=45, blank=True)
    clean_code = models.CharField(max_length=45, blank=True)
    clean_no = models.CharField(max_length=45, blank=True)
    org_type_id = models.CharField(max_length=45)
    org_sub_type_id = models.CharField(max_length=45)
    name = models.CharField(max_length=100, blank=True)
    official_org_name = models.CharField(max_length=100, blank=True)
    official_address = models.CharField(max_length=255, blank=True)
    area_coding = models.CharField(max_length=45)
    street_coding = models.CharField(max_length=45)
    comm_coding = models.CharField(max_length=45)
    address = models.CharField(max_length=250, blank=True)
    cover = models.CharField(max_length=1000, blank=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11)
    longitude = models.DecimalField(max_digits=20, decimal_places=11)
    longitude_b = models.DecimalField(max_digits=20, decimal_places=8)
    latitude_b = models.DecimalField(max_digits=20, decimal_places=8)
    longitude_g = models.DecimalField(max_digits=20, decimal_places=8)
    latitude_g = models.DecimalField(max_digits=20, decimal_places=8)
    liabler = models.CharField(max_length=255, blank=True)
    contacts = models.CharField(max_length=256, blank=True)
    phone = models.CharField(max_length=256, blank=True)
    credit_code = models.CharField(max_length=45, blank=True)
    permission_code = models.CharField(max_length=255, blank=True)
    floor_area = models.CharField(max_length=56, default='', blank=True)
    loating = models.CharField(max_length=56, default='', blank=True)
    is_reduce_device = models.IntegerField(default=0, blank=True, null=True)
    reduce_water_device = models.CharField(max_length=56, default='', blank=True)
    reduce_oil_device = models.CharField(max_length=56, default='', blank=True)
    is_local_device = models.IntegerField(default=0, blank=True, null=True)
    local_factory = models.CharField(max_length=255, default='', blank=True)
    local_capacity = models.CharField(max_length=255, default='', blank=True)
    local_process = models.CharField(max_length=255, default='', blank=True)
    local_out_solid_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_solid_direction = models.CharField(max_length=255, default='', blank=True)
    local_out_water_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_water_direction = models.CharField(max_length=255, default='', blank=True)
    local_out_dregs_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_dregs_direction = models.CharField(max_length=255, default='', blank=True)
    licence_cover = models.CharField(max_length=255, blank=True)
    mam_type = models.CharField(max_length=255, blank=True)
    mam_subtype = models.CharField(max_length=255, blank=True)
    restaurant_predict_weight = models.IntegerField(default=0)
    restaurant_trash_120 = models.IntegerField(default=0)
    restaurant_trash_240 = models.IntegerField(default=0)
    restaurant_trash = models.IntegerField(default=0)
    restaurant_trash_rfid = models.IntegerField(default=0)
    transport_company_id = models.CharField(max_length=45, null=True, blank=True)
    is_status_succeed = models.IntegerField(default=0)
    status = models.IntegerField(default=1, verbose_name="审核状态 0-编辑中 1-提交审核 2-审核成功 3-审核失败")
    reason = models.CharField(max_length=255, blank=True)
    no_credit_reason = models.CharField(max_length=255, blank=True)
    create_time = models.IntegerField()
    update_time = models.DateTimeField(auto_now=True)
    audit_time = models.IntegerField(default=0)
    creator = models.CharField(max_length=45, blank=True)
    auditor = models.CharField(max_length=45, blank=True)
    auditor_username = models.CharField(max_length=45, blank=True)
    have_contract = models.IntegerField(default=0)
    auto_audit = models.IntegerField(default=0)
    check_type = models.IntegerField(default=0, verbose_name="审核类型  0 注册  1编辑 2 非居民注销 3区注销 4街道注销")
    logout_reson = models.IntegerField(blank=True, null=True, verbose_name='注销原因')
    logout_content = models.TextField(blank=True, null=True, verbose_name='原因描述')
    is_declare = models.IntegerField(default=1)
    rubbishes = models.CharField(max_length=64, null=True, blank=True, verbose_name="垃圾类型  RESTAURANTS OTHER")
    add_another_type = models.IntegerField(default=0, verbose_name='是否有一种垃圾类型通过审核，补充另一种垃圾类型')
    three_guarantees_agreement = models.CharField(max_length=1000, null=True, blank=True, verbose_name='门前三包责任书')
    three_guarantees_board = models.CharField(max_length=1000, null=True, blank=True, verbose_name='门前三包责任图')

    class Meta:
        managed = False
        db_table = 'org_nonresident_declare'
        app_label = 'ljfl_declare_db'


class AuthorizedOrgRelationId(models.Model):
    authorized_org_relation_id = models.CharField(max_length=45)
    org_id = models.CharField(max_length=45)
    is_admin = models.IntegerField()
    create_time = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'org_nonresident_mapping'
        app_label = 'ljfl_declare_db'


class CleanCodeNoMapping(models.Model):
    clean_type = models.CharField(max_length=36)
    clean_subtype = models.CharField(max_length=36)
    clean_id = models.CharField(max_length=36)
    clean_code = models.CharField(max_length=36)
    clean_no = models.CharField(max_length=36)

    class Meta:
        managed = False
        db_table = 'clean_code_no_mapping'
        app_label = 'ljfl_declare_db'


class OrgNonresidentDeclareOperate(models.Model):
    declare_id = models.CharField(max_length=45)
    org_id = models.CharField(max_length=45)
    operate_type = models.CharField(max_length=45)
    current_data = models.CharField(max_length=5000)
    change_data = models.CharField(max_length=5000)
    operate_time = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'org_nonresident_declare_operate'
        app_label = 'ljfl_declare_db'


class CleanCodeSequence(models.Model):
    clean_type = models.CharField(max_length=36)
    clean_subtype = models.CharField(max_length=36)
    prefix = models.CharField(max_length=36)
    sequence = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'clean_code_sequence'
        app_label = 'ljfl_declare_db'


class OrgNonresidentIssued(models.Model):
    id = models.IntegerField(primary_key=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    clean_code = models.CharField(max_length=45, blank=True, null=True)
    clean_no = models.CharField(max_length=45, blank=True, null=True)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True)
    status = models.IntegerField(default=0, blank=True)
    creator = models.CharField(max_length=45, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'org_nonresident_issued'
        app_label = 'ljfl_declare_db'


class NonresidentOrder(models.Model):
    """
    非居民调度订单
    """

    order_id = models.CharField(max_length=64, unique=True, verbose_name='订单编号', default=create_time_uuid())
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    apply_clean_time = models.DateField(blank=True, null=True, verbose_name='申请清运时间')
    org_id = models.CharField(max_length=45, blank=True, null=True)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True)
    rubbish_type_id = models.CharField(max_length=64, blank=True, null=True)
    trash_120L = models.IntegerField(default=0, blank=True)
    trash_240L = models.IntegerField(default=0, blank=True)
    # 质量判定编码  1234  优良中差
    quality_code = models.IntegerField(null=True, blank=True, verbose_name='质量判定编码')
    pic = models.CharField(max_length=255, null=True, blank=True, verbose_name='图片')
    status = models.IntegerField(default=0, blank=True,
                                 verbose_name='0:创建 1:申请调度 2:下发任务 3：任务完成 9：取消订单')
    create_time = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    update_time = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    is_distribution = models.IntegerField(default=0, blank=True, null=True, verbose_name='是否分配收运公司')
    car_num = models.CharField(default='', blank=True, null=True, verbose_name='任务下发的车辆')
    company_evaluation = models.IntegerField(default=0, blank=True, null=True)
    driver_evaluation = models.IntegerField(default=0, blank=True, null=True)
    order_evaluation = models.IntegerField(default=0, blank=True, null=True)
    is_evaluation = models.IntegerField(default=0, blank=True, null=True)
    car_record_id = models.CharField(max_length=255, blank=True, null=True, verbose_name='收运记录id', default='')
    bill_org_id = models.CharField(max_length=255, blank=True, null=True, verbose_name='联单主体id', default='')
    bill_factory_id = models.CharField(max_length=255, blank=True, null=True, verbose_name='联单末端id', default='')

    class Meta:
        managed = False
        db_table = 'nonresident_order'
        app_label = 'ljfl_db'


class NonresidentOrderWarning(models.Model):
    """
    非居民调度订单预警
    """
    uuid = models.CharField(max_length=64, unique=True, verbose_name='uuid', default=create_time_uuid())
    order_id = models.CharField(max_length=64, unique=True, verbose_name='订单编号')
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    apply_clean_time = models.DateField(blank=True, null=True, verbose_name='申请清运时间')
    org_id = models.CharField(max_length=45, blank=True, null=True)
    warning_date = models.DateField(blank=True, null=True)
    create_time = models.DateTimeField(auto_now_add=True)
    order_create_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'nonresident_order_warning'
        app_label = 'ljfl_db'


class Date(Func):
    function = 'DATE'


class NonresidentCharge(models.Model):
    """
    非居民收费
    """
    charge_type = models.IntegerField(default=0, blank=True, null=True, verbose_name='0 非居民收费, 1末端厂')
    base_price = models.FloatField(blank=True, null=True, verbose_name='基础价钱')
    service_price = models.FloatField(blank=True, null=True, verbose_name='服务费')
    service_discount = models.FloatField(blank=True, null=True, verbose_name='服务折扣')
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    create_time = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    update_time = models.DateTimeField(auto_now=True, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'nonresident_charge'
        app_label = 'ljfl_db'


class OrgNonresidentCanteenData(models.Model):
    """
    非居民食堂数据填报
    """
    record_uuid = models.CharField(max_length=45, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    type_id = models.CharField(max_length=45)
    weight = models.DecimalField(max_digits=20, decimal_places=2)
    create_time = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    update_time = models.DateTimeField(auto_now=True, blank=True, null=True)
    data_time = models.CharField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'org_nonresident_canteen_data'
        app_label = 'ljfl_declare_db'


class MyNonresidentAppointmentCollection(models.Model):
    """
    密晓分非居民预约收运
    """
    appointment_uuid = models.CharField(max_length=45, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    weight = models.DecimalField(max_digits=20, decimal_places=2, verbose_name='预约重量')
    appointment_time_start = models.DateTimeField(blank=True, null=True, verbose_name='预约时间开始')
    appointment_time_end = models.DateTimeField(blank=True, null=True, verbose_name='预约时间结束')
    create_time = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    update_time = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'my_nonresident_appointment_collection'
        app_label = 'ljfl_db'


class NonresidentStatist(models.Model):
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    count = models.IntegerField(default=0, blank=True, null=True)
    create_date = models.DateField(blank=True, null=True)
    create_time = models.DateTimeField(auto_now_add=True, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'nonresident_statist'
        app_label = 'ljfl_declare_db'


class NonresidentPayOrder(models.Model):
    order_num = models.CharField(max_length=45, blank=True, null=True, verbose_name='订单编号')
    bill_factory_id = models.CharField(max_length=45, default='')
    bill_org_id = models.CharField(max_length=45, default='')
    confirm_time = models.DateTimeField(blank=True, null=True, verbose_name='确认时间')
    status = models.IntegerField(default=0, blank=True, null=True, verbose_name='0待支付 1 已支付')
    pay_time = models.DateTimeField(blank=True, null=True, verbose_name='支付时间')
    pay_price = models.IntegerField(default=0, verbose_name='支付金额 单位分')
    org_id = models.CharField(max_length=45)
    transport_company_id = models.CharField(max_length=45)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    create_time = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    update_time = models.DateTimeField(auto_now=True, blank=True, null=True)
    transaction_id = models.CharField(max_length=45, blank=True, null=True, verbose_name='微信支付订单号')
    total_fee = models.IntegerField(default=0, verbose_name='订单支付金额 单位分')
    balance = models.IntegerField(default=0, verbose_name='账号余额 单位分')
    send_info = models.IntegerField(default=0, verbose_name='0 未发送， 1 已发送')
    pay_type = models.CharField(max_length=16,
                                verbose_name='缴费形式 预付金额  即时支付 公对公支付 预付订单 公对公支付订单')
    charge_rules = models.CharField(max_length=16, default='', verbose_name='收费规则 次缴费 重量缴费 包月')
    pay_config_id = models.CharField(max_length=45, verbose_name='uuid')
    pay_image = models.CharField(max_length=500, verbose_name='公对公支付款记录照片')

    class Meta:
        managed = False
        db_table = 'nonresident_pay_order'
        app_label = 'ljfl_declare_db'


class NonOrgBalance(models.Model):
    org_id = models.CharField(max_length=45)
    transport_company_id = models.CharField(max_length=45)
    pre_pay_balance = models.IntegerField(default=0, verbose_name='预交费,公对公 余额 单位分')
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    create_time = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    update_time = models.DateTimeField(auto_now=True, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'non_org_balance'
        app_label = 'ljfl_declare_db'


class OtherOrgNonresidentDeclare(models.Model):
    declare_id = models.CharField(max_length=45)
    declare_type = models.CharField(max_length=45)
    source_type = models.CharField(max_length=45)
    org_id = models.CharField(max_length=45, blank=True)
    clean_code = models.CharField(max_length=45, blank=True)
    clean_no = models.CharField(max_length=45, blank=True)
    org_type_id = models.CharField(max_length=45)
    org_sub_type_id = models.CharField(max_length=45)
    name = models.CharField(max_length=100, blank=True)
    official_org_name = models.CharField(max_length=100, blank=True)
    official_address = models.CharField(max_length=255, blank=True)
    area_coding = models.CharField(max_length=45)
    street_coding = models.CharField(max_length=45)
    comm_coding = models.CharField(max_length=45)
    address = models.CharField(max_length=250, blank=True)
    cover = models.CharField(max_length=1000, blank=True)
    latitude = models.DecimalField(max_digits=20, decimal_places=11)
    longitude = models.DecimalField(max_digits=20, decimal_places=11)
    longitude_b = models.DecimalField(max_digits=20, decimal_places=8)
    latitude_b = models.DecimalField(max_digits=20, decimal_places=8)
    longitude_g = models.DecimalField(max_digits=20, decimal_places=8)
    latitude_g = models.DecimalField(max_digits=20, decimal_places=8)
    liabler = models.CharField(max_length=255, blank=True)
    contacts = models.CharField(max_length=256, blank=True)
    phone = models.CharField(max_length=256, blank=True)
    credit_code = models.CharField(max_length=45, blank=True)
    permission_code = models.CharField(max_length=255, blank=True)
    floor_area = models.CharField(max_length=56, default='', blank=True)
    loating = models.CharField(max_length=56, default='', blank=True)
    is_reduce_device = models.IntegerField()
    reduce_water_device = models.CharField(max_length=56, default='', blank=True)
    reduce_oil_device = models.CharField(max_length=56, default='', blank=True)
    is_local_device = models.IntegerField()
    local_factory = models.CharField(max_length=255, default='', blank=True)
    local_capacity = models.CharField(max_length=255, default='', blank=True)
    local_process = models.CharField(max_length=255, default='', blank=True)
    local_out_solid_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_solid_direction = models.CharField(max_length=255, default='', blank=True)
    local_out_water_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_water_direction = models.CharField(max_length=255, default='', blank=True)
    local_out_dregs_weight = models.CharField(max_length=56, default='', blank=True)
    local_out_dregs_direction = models.CharField(max_length=255, default='', blank=True)
    licence_cover = models.CharField(max_length=255, blank=True)
    mam_type = models.CharField(max_length=255)
    mam_subtype = models.CharField(max_length=255, blank=True)
    restaurant_predict_weight = models.IntegerField(default=0)
    restaurant_trash_120 = models.IntegerField(default=0)
    restaurant_trash_240 = models.IntegerField(default=0)
    restaurant_trash = models.IntegerField(default=0)
    restaurant_trash_rfid = models.IntegerField(default=0)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True, default="", verbose_name="运输公司id")
    is_status_succeed = models.IntegerField(default=0)
    status = models.IntegerField(default=1,
                                 verbose_name="审核状态 0-编辑中 1-提交审核（待公司确认） 2-审核成功（街道审核完成进入台账） 3-审核失败 4-公司审核完（待街道审核）")
    reason = models.CharField(max_length=255, blank=True)
    no_credit_reason = models.CharField(max_length=255, blank=True)
    create_time = models.IntegerField()
    audit_time = models.IntegerField(default=0)
    creator = models.CharField(max_length=45, blank=True)
    auditor = models.CharField(max_length=45, blank=True)
    auditor_username = models.CharField(max_length=45, blank=True)
    have_contract = models.IntegerField(default=0)
    auto_audit = models.IntegerField(default=0)
    check_type = models.IntegerField(default=0, verbose_name="审核类型  0 注册  1编辑 2 非居民注销 3区注销 4街道注销")
    logout_reson = models.IntegerField(blank=True, null=True, verbose_name='注销原因')
    logout_content = models.TextField(blank=True, null=True, verbose_name='原因描述')
    is_declare = models.IntegerField(default=2, verbose_name='是否其他垃圾非居民注册')
    other_trash_120 = models.IntegerField(default=0)
    other_trash_240 = models.IntegerField(default=0)
    other_trash = models.IntegerField(default=0)
    other_predict_weight = models.IntegerField(default=0)
    transport_company_name = models.CharField(max_length=45, blank=True, null=True, default="",
                                              verbose_name="运输公司名称")
    floor_name = models.CharField(max_length=45, default="", blank=True, null=True, verbose_name='楼宇/商圈/建筑名称')
    floor_cover = models.TextField(default="", blank=True, null=True, verbose_name='楼宇/商圈/建筑名称照片')
    official_cover = models.TextField(default="", blank=True, null=True, verbose_name='营业执照图片')
    car_num = models.CharField(max_length=100, default="", blank=True, null=True, verbose_name='收运车辆')
    contract_pic = models.TextField(default="", blank=True, null=True, verbose_name='合同照片')
    contract_start_date = models.DateField(null=True, verbose_name='服务期限开始日期')
    contract_end_date = models.DateField(null=True, verbose_name='服务期限结束日期')
    transport_company_info = models.TextField(blank=True, null=True, verbose_name='收运公司详情')
    rubbishes = models.CharField(max_length=64, null=True, blank=True, verbose_name="垃圾类型")

    class Meta:
        managed = False
        db_table = 'other_org_nonresident_declare'
        app_label = 'ljfl_declare_db'


class OrgNonresidentQuota(models.Model):
    """
    非居民定额量
    """
    quota_id = models.CharField(max_length=45)
    org_id = models.CharField(max_length=45)
    quota = models.DecimalField(blank=True, null=True, max_digits=18, decimal_places=2, verbose_name='定额量（吨）')
    status = models.IntegerField(default=0,
                                 verbose_name='定额状态 0 待定额  1 待确认 2 申诉中|街道审核中  3 区级审核中  4 定额完成')
    quota_date = models.CharField(max_length=20, verbose_name='定额年')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name=u'编辑时间')
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    remark = models.CharField(max_length=500, blank=True, null=True, default='', verbose_name='备注')

    class Meta:
        managed = False
        db_table = 'org_nonresident_quota'
        app_label = 'ljfl_declare_db'


class QuotaEditRecord(models.Model):
    """
    非居民定额量编辑记录
    """
    quota_record_id = models.CharField(max_length=45)
    org_id = models.CharField(max_length=45)
    quota_id = models.CharField(max_length=45)
    quota = models.DecimalField(blank=True, null=True, max_digits=18, decimal_places=2, verbose_name='定额量（吨）')
    quota_date = models.CharField(max_length=20, verbose_name='定额年')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name=u'编辑时间')
    status = models.IntegerField(default=0, verbose_name='0 流程没完成，1 流程完成')
    remark = models.CharField(max_length=500, blank=True, null=True, default='', verbose_name='备注')
    is_deleted = models.IntegerField(blank=True, null=True, default=0)

    class Meta:
        managed = False
        db_table = 'quota_edit_record'
        app_label = 'ljfl_declare_db'


class QuotaAppealRecord(models.Model):
    """
    非居民定额量申诉记录
    """
    appeal_id = models.CharField(max_length=45)
    org_id = models.CharField(max_length=45)
    quota_record_id = models.CharField(max_length=45, blank=True, null=True)
    # appeal_type = models.IntegerField(default=0, verbose_name='申诉类型 0 非居民 1街道')
    appeal_desc = models.CharField(max_length=500, verbose_name='非居民申诉描述')
    org_quota = models.DecimalField(blank=True, null=True, max_digits=18, decimal_places=2, default=0,
                                    verbose_name='非居民申诉定额量（吨）')
    street_quota = models.DecimalField(blank=True, null=True, max_digits=18, decimal_places=2, default=0,
                                       verbose_name='街道申诉定额量（吨）')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name=u'编辑时间')
    is_deleted = models.IntegerField(blank=True, null=True, default=0)

    class Meta:
        managed = False
        db_table = 'quota_appeal_record'
        app_label = 'ljfl_declare_db'


class BillSourceStreet(models.Model):
    terminal_factory_record_id = models.CharField(max_length=45)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    first_time = models.IntegerField(blank=True, null=True)
    last_time = models.IntegerField(blank=True, null=True)
    bill_count = models.IntegerField(blank=True, null=True, default=0)
    is_deleted = models.IntegerField(default=0, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "bill_source_street"
        app_label = "ljfl_db"


class AppointmentRecycling(models.Model):
    """
    预约回收
    """
    recycling_id = models.CharField(max_length=45)
    recycling_num = models.CharField(max_length=45, verbose_name='预约订单号')
    org_id = models.CharField(max_length=45, blank=True, null=True)
    org_name = models.CharField(max_length=45, blank=True, null=True)
    clean_no = models.CharField(max_length=45, blank=True, null=True)
    transport_company_id = models.CharField(max_length=45, blank=True, null=True)
    car_num = models.CharField(max_length=45, blank=True, null=True)
    image = models.CharField(max_length=500, verbose_name='图片')
    contact = models.CharField(max_length=45, blank=True, null=True, verbose_name='联系人')
    phone = models.CharField(max_length=32, blank=True, null=True, verbose_name='电话')
    address = models.CharField(max_length=64, blank=True, null=True, verbose_name='地址')
    collector_date = models.DateField(blank=True, null=True, verbose_name='上门时间')
    assigned_date = models.DateTimeField(blank=True, null=True, verbose_name='指派时间')
    weight_range = models.IntegerField(default=0, blank=True, null=True,
                                       verbose_name='重量范围 0: 0-60, 1:60-220, 2:220+ kg')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name=u'编辑时间')
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    status = models.IntegerField(blank=True, null=True, default=0, verbose_name='0 未指派， 1已指派')
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'appointment_recycling'
        app_label = 'ljfl_declare_db'


class ResidentRecord(models.Model):
    resident_record_id = models.CharField(max_length=45)
    resident_id = models.CharField(max_length=45)
    type_id = models.CharField(max_length=45, blank=True, null=True)
    sub_type_id = models.CharField(max_length=45, blank=True, null=True)
    weight = models.FloatField(blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    org_name = models.CharField(max_length=45, blank=True, null=True)
    device_id = models.CharField(max_length=45, blank=True, null=True)
    cover = models.CharField(max_length=1000, blank=True, null=True)
    quality = models.CharField(max_length=32, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    reward_coin = models.FloatField(blank=True, null=True)
    civilization_coin = models.FloatField(blank=True, null=True)
    credit_coin = models.FloatField(blank=True, null=True)
    coin_type = models.CharField(max_length=45, blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    stream_number = models.CharField(max_length=45, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "resident_record"
        app_label = 'ljfl_db'


class Resident(models.Model):
    resident_id = models.CharField(max_length=45, default="")
    name = models.CharField(max_length=45, blank=True, null=True)
    farmily_id = models.CharField(max_length=45, blank=True, null=True)
    org_id = models.CharField(max_length=45, blank=True, null=True)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    street_coding = models.CharField(max_length=45, blank=True, null=True)
    comm_coding = models.CharField(max_length=45, blank=True, null=True)
    card_num = models.CharField(max_length=45, blank=True, null=True)
    card_id_num = models.CharField(max_length=45, blank=True, null=True)
    reward_coin = models.FloatField(blank=True, null=True)
    civilization_coin = models.FloatField(blank=True, null=True)
    credit_coin = models.FloatField(blank=True, null=True)
    reward_coin_amount = models.FloatField(blank=True, null=True)
    civilization_coin_amount = models.FloatField(blank=True, null=True)
    credit_coin_amount = models.FloatField(blank=True, null=True)
    address = models.CharField(max_length=250, blank=True, null=True)
    building_num = models.CharField(max_length=45, blank=True, null=True)
    unit_num = models.CharField(max_length=45, blank=True, null=True)
    room_num = models.CharField(max_length=45, blank=True, null=True)
    phone = models.CharField(max_length=250, blank=True, null=True)
    throw_num = models.IntegerField(blank=True, null=True)
    creator = models.CharField(max_length=45, blank=True, null=True)
    is_deleted = models.IntegerField(blank=True, null=True, default=0)
    remark = models.CharField(max_length=500, blank=True, null=True)
    create_time = models.IntegerField(blank=True, null=True)
    update_time = models.IntegerField(blank=True, null=True)
    wechat_id = models.CharField(max_length=45, blank=True, null=True)
    alipay_id = models.CharField(max_length=20, blank=True, null=True)
    wechat_picture = models.CharField(max_length=200, blank=True, null=True)
    alipay_picture = models.CharField(max_length=200, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "resident"
        app_label = 'ljfl_db'


class TrashCanAlarmRecord(models.Model):
    """居民投放桶站报警数据"""

    class TrashCanAlarmType:
        """居民投放桶站报警类型"""
        AlarmTypeDict = dict(
            BARREL_DEPTH="垃圾桶满冒",
            FOUL_SMELL="臭味超标",
            CAMERAL="摄像头异常",
            WEIGHT="称重异常",
            HUMITURE="温度湿度超限制",
            TAKE_UP_DEVICE="垃圾袋收取装置异常",
            BAG_BREAK_DEVICE="破袋装置异常",
            COMMUNICATION="通讯模块异常",
            POWER_SUPPLY="太阳能供电系统异常",
            BAG_BREAK_DEVICE_OK="设备恢复正常"
        )

    # uuid = models.CharField(max_length=32, verbose_name="uuid", default=uuid.uuid4().hex)
    alarm_time = models.DateTimeField(verbose_name="报警时间")
    street_coding = models.CharField(verbose_name="所属街道", max_length=32)
    street_name = models.CharField(verbose_name="所属街道", max_length=32)
    comm_coding = models.CharField(verbose_name="所属社区", max_length=32)
    comm_name = models.CharField(verbose_name="所属社区", max_length=32)
    org_name = models.CharField(verbose_name="所属小区", max_length=32)
    trash_can = models.CharField(verbose_name="桶站名称", max_length=32)
    alarm_type = models.CharField(verbose_name="报警类型", max_length=32)
    create_time = models.DateTimeField(auto_now=datetime.datetime.now())
    update_time = models.DateTimeField(auto_now=datetime.datetime.now())
    is_deleted = models.IntegerField(default=0, verbose_name="是否删除")

    class Meta:
        managed = False
        db_table = "trash_can_alarm_record"
        app_label = 'ljfl_db'


class OilWaterSeparationDeviceRecord(models.Model):
    """厨余垃圾油水分离装置数据"""

    uuid = models.CharField(max_length=32, verbose_name="uuid", default=uuid.uuid4().hex)
    facility_name = models.CharField(max_length=32, verbose_name="处理设施名称", null=True, blank=True)
    equipment_name = models.CharField(max_length=32, verbose_name="处理设备名称", null=True, blank=True)
    inbound_time = models.DateTimeField(verbose_name="进料时间", null=True, blank=True)
    inbound_weight = models.FloatField(verbose_name="进料重量")
    outbound_weight = models.FloatField(verbose_name="处理后重量")
    oil_weight = models.FloatField(verbose_name="油脂分离重量")
    water_weight = models.FloatField(verbose_name="污水分离重量")
    residue_weight = models.FloatField(verbose_name="残渣分离重量")
    other_weight = models.FloatField(verbose_name="其他分离重量")
    create_time = models.DateTimeField(auto_now=datetime.datetime.now())
    update_time = models.DateTimeField(auto_now=datetime.datetime.now())
    is_deleted = models.IntegerField(default=0, verbose_name="是否删除")

    class Meta:
        managed = False
        db_table = "oil_water_separation_device_record"
        app_label = 'ljfl_db'


class OrganizationWechat(models.Model):
    """
    石景山非居民小程序迁移，非居民手机号绑定关系
    """
    clean_no = models.CharField(max_length=45, blank=True)
    phone = models.CharField(max_length=45, blank=True)
    wechat_name = models.CharField(max_length=64, blank=True)
    name = models.CharField(max_length=64, blank=True, null=True)
    credit_code = models.CharField(max_length=45, blank=True, null=True)
    official_org_name = models.CharField(max_length=64, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'non_org_wechat'
        app_label = 'ljfl_declare_db'


class ResidentAccount(models.Model):
    """
    线上建国门居民表
    """
    resident_id = models.CharField(max_length=45, verbose_name='唯一id')
    name = models.CharField(max_length=45, null=True, blank=True, verbose_name='真实姓名')
    farmily_id = models.CharField(max_length=45, null=True, blank=True, verbose_name='家庭积分账户id')
    org_id = models.CharField(max_length=45, null=True, blank=True, verbose_name='对应的主体id')
    area_coding = models.CharField(max_length=45, null=True, blank=True, verbose_name='区')
    street_coding = models.CharField(max_length=45, null=True, blank=True, verbose_name='街道')
    comm_coding = models.CharField(max_length=45, null=True, blank=True, verbose_name='社区')
    card_num = models.CharField(max_length=45, null=True, blank=True, verbose_name='积分卡号')
    card_id_num = models.CharField(max_length=45, null=True, blank=True, verbose_name='积分卡号编号')

    class Meta:
        managed = False
        db_table = 'resident_account'
        app_label = 'ljfl_db'


# 街道范围
class StreetMapScope(models.Model):
    # 行政区编码
    street_coding = models.CharField(max_length=45, null=True, blank=True, verbose_name=("街道编码"))
    street_name = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=("街道名称")
    )
    area_coding = models.CharField(max_length=45, null=True, blank=True, verbose_name=("区编码"))
    area_name = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=("区名称")
    )
    # 经纬度
    longitude_latitude = models.TextField(blank=True, verbose_name=("经纬度"))
    # 中心点
    longitude_center = models.DecimalField(
        max_digits=20, decimal_places=11, blank=True, null=True
    )
    latitude_center = models.DecimalField(
        max_digits=20, decimal_places=11, blank=True, null=True
    )
    is_deleted = models.IntegerField(blank=True, null=True, default=0)

    class Meta:
        db_table = "street_scope"
        app_label = 'ljfl_db'


class SyNonresidentHasRecord(models.Model):
    """
    顺义区每日非居民是否有收运记录
    """
    area_coding = models.CharField(max_length=64, verbose_name="区")
    org_id = models.CharField(max_length=64, verbose_name="主体id")
    org_name = models.CharField(max_length=64, verbose_name="主体名称")
    has_restaurants = models.SmallIntegerField(max_length=64, verbose_name="收运餐厨垃圾")
    has_other = models.SmallIntegerField(max_length=64, verbose_name="收运其他垃圾")
    create_date = models.DateField(max_length=64, verbose_name="统计日期")
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "sy_nonresident_has_record"
        app_label = "ljfl_db"
        verbose_name = "顺义区每日非居民是否有收运记录"
