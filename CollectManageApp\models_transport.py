# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each Foreign<PERSON>ey has `on_delete` set to the desired behavior.
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.db import models
from django.utils import timezone


class Car(models.Model):
    company_id = models.CharField(max_length=45, blank=True, null=True)
    company_name = models.CharField(max_length=50, blank=True, null=True)
    relation_id = models.CharField(max_length=45)
    car_id = models.Char<PERSON>ield(max_length=45)
    car_num = models.CharField(max_length=45, blank=True, null=True)
    company_num = models.CharField(max_length=25, blank=True, null=True)
    company_type = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    approval_author = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    license_number = models.CharField(max_length=25, blank=True, null=True)
    power_type = models.CharField(max_length=5, blank=True, null=True)
    register_address = models.CharField(max_length=120, blank=True, null=True)
    office_address = models.CharField(max_length=120, blank=True, null=True)
    admin = models.CharField(max_length=50, blank=True, null=True)
    admin_phone = models.CharField(max_length=30, blank=True, null=True)
    equipment_count = models.IntegerField(blank=True, null=True)
    company_contacts = models.CharField(max_length=45, blank=True, null=True)
    company_phone = models.CharField(max_length=45, blank=True, null=True)
    car_type_id = models.CharField(max_length=45, blank=True, null=True)
    carry_weight = models.CharField(max_length=45, blank=True, null=True)
    is_tu = models.CharField(max_length=5, blank=True, null=True)
    is_weight = models.CharField(max_length=5, blank=True, null=True)
    is_monitor = models.CharField(max_length=5, blank=True, null=True)
    is_access = models.CharField(max_length=5, blank=True, null=True)
    is_identification = models.CharField(max_length=5, blank=True, null=True)
    factory_name = models.CharField(max_length=50, blank=True, null=True)
    factory_num = models.CharField(max_length=25, blank=True, null=True)
    is_exists = models.CharField(max_length=5, blank=True, null=True)
    factory_contacts = models.CharField(max_length=45, blank=True, null=True)
    factory_phone = models.CharField(max_length=45, blank=True, null=True)
    service_area = models.CharField(max_length=120, blank=True, null=True)
    is_delete = models.IntegerField()
    status = models.IntegerField()
    status_type = models.IntegerField()
    no_reject = models.TextField(blank=True, null=True)
    operater = models.CharField(max_length=32, blank=True, null=True)
    enter_time = models.DateTimeField(blank=True, null=True)
    enter_type = models.IntegerField(blank=True, null=True)
    submit_time = models.DateTimeField(blank=True, null=True)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    enter_operater = models.CharField(max_length=32, blank=True, null=True)
    selected_type = models.IntegerField(blank=True, null=True)
    street_coding = models.CharField(max_length=255, blank=True, null=True)
    street_name = models.CharField(max_length=500, blank=True, null=True)
    relation_id = models.CharField(max_length=54, blank=True, null=True)
    factory_address = models.CharField(max_length=255, blank=True, null=True)
    is_quality = models.CharField(max_length=5, blank=True, null=True)
    transport_type = models.CharField(max_length=5, blank=True, null=True)
    rubbish_type = models.CharField(max_length=54, blank=True, null=True)
    register_num = models.CharField(max_length=5, blank=True, null=True)
    clean_code = models.CharField(max_length=32, blank=True, null=True)
    clean_no = models.CharField(max_length=12, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'car'
        app_label = 'transport_db'


class Company(models.Model):
    company_id = models.CharField(max_length=45)
    name = models.CharField(max_length=50, blank=True, null=True)
    admin = models.CharField(max_length=32, blank=True, null=True)
    id_card = models.CharField(max_length=32, blank=True, null=True)
    phone = models.CharField(max_length=32, blank=True, null=True)
    address = models.CharField(max_length=120, blank=True, null=True)
    credit_code = models.CharField(max_length=32, blank=True, null=True)
    service_area = models.CharField(max_length=120, blank=True, null=True)
    email = models.CharField(max_length=120, blank=True, null=True)
    company_picture = models.TextField(blank=True, null=True)
    status_type = models.IntegerField()
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()
    is_delete = models.IntegerField()
    contract_file = models.TextField(blank=True, null=True)
    quali_file = models.TextField(blank=True, null=True)
    company_type = models.CharField(max_length=32, blank=True, null=True)
    add_status = models.IntegerField(blank=True, null=True)
    c_area_coding = models.CharField(max_length=45, blank=True, null=True)
    c_street_coding = models.CharField(max_length=45, blank=True, null=True)
    c_street_name = models.CharField(max_length=64, blank=True, null=True)
    c_comm_coding = models.CharField(max_length=45, blank=True, null=True)
    c_comm_name = models.CharField(max_length=64, blank=True, null=True)
    rubbish_type = models.CharField(max_length=255, blank=True, null=True)
    rubbish_type_name = models.CharField(max_length=255, blank=True, null=True)
    viald_time = models.CharField(max_length=45, blank=True, null=True)
    clean_code = models.CharField(max_length=32, blank=True, null=True)
    clean_no = models.CharField(max_length=12, blank=True, null=True)
    status = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'company'
        app_label = 'transport_db'


class CompanyArea(models.Model):
    transport_company_area_id = models.CharField(max_length=45)
    area_coding = models.CharField(max_length=45, blank=True, null=True)
    company_id = models.CharField(max_length=45)
    created_time = models.DateTimeField()
    updated_time = models.DateTimeField(blank=True, null=True)
    is_delete = models.IntegerField()
    enter_time = models.DateTimeField(blank=True, null=True)
    enter_type = models.IntegerField(blank=True, null=True)
    no_reject = models.CharField(max_length=200, blank=True, null=True)
    enter_operater = models.CharField(max_length=32, blank=True, null=True)
    selected_type = models.IntegerField(blank=True, null=True)
    selected_time = models.DateTimeField(blank=True, null=True)
    street_coding = models.TextField(blank=True, null=True)
    street_name = models.TextField(blank=True, null=True)
    status = models.IntegerField(blank=True, null=True)
    clean_code = models.CharField(max_length=32, blank=True, null=True)
    clean_no = models.CharField(max_length=12, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'company_area'
        app_label = 'transport_db'


class TransportUser(models.Model):
    username = models.CharField(max_length=150, blank=True, null=True)
    relation_id = models.CharField(max_length=54, blank=True, null=True)
    company_uid = models.CharField(max_length=50, blank=True, null=True)
    user_status = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'user'
        app_label = 'transport_db'


class ContractNew(models.Model):
    """合同信息"""
    contract_id = models.CharField(max_length=45, verbose_name="唯一id")
    company_id = models.CharField(max_length=45, null=True, blank=True, verbose_name='公司id')
    company_name = models.CharField(blank=True, null=False, max_length=50, verbose_name='公司名称')
    service_area = models.CharField(blank=True, null=True, max_length=120, verbose_name='服务区域')
    street_coding = models.CharField(max_length=255, blank=True, null=True, verbose_name='街道coding')
    street_name = models.CharField(max_length=500, blank=True, null=True, verbose_name='街道名称')
    detaile_address = models.CharField(blank=True, null=True, max_length=255, verbose_name='详细地址')
    food_license = models.CharField(blank=True, null=True, max_length=255, verbose_name='食品经营许可证')
    org_id = models.CharField(max_length=64, blank=True, null=True, verbose_name='非居民单位id')
    organization = models.CharField(max_length=64, blank=True, null=True, verbose_name='非居民单位')
    org_type = models.CharField(max_length=32, blank=True, null=True, verbose_name='单位类型')
    credit_code = models.CharField(blank=True, null=True, max_length=32, verbose_name='社会统一信用代码')
    contract_num = models.CharField(blank=True, null=True, max_length=64, verbose_name='合同编号')
    rubbish_type = models.CharField(max_length=255, null=True, blank=True, default='', verbose_name='垃圾类型id')
    rubbish_type_name = models.CharField(max_length=255, null=True, blank=True, default='', verbose_name='垃圾类型名称')
    viald_time = models.CharField(max_length=45, blank=True, null=True, verbose_name='服务期限')
    sign_date = models.CharField(blank=True, null=True, max_length=32, verbose_name='合同签订日期')
    collect_date = models.CharField(blank=True, null=True, max_length=32, verbose_name='收集时间')
    collect_method = models.IntegerField(blank=True, null=True, verbose_name='收集方式（其他垃圾合同有这个选项）：1：密闭式清洁站收集、2：直收直运车收集')
    contacts = models.CharField(max_length=45, blank=True, null=True, verbose_name='清运公司联系人')
    phone = models.CharField(max_length=30, blank=True, null=True, verbose_name='清运公司联系电话')
    f_contacts = models.CharField(max_length=45, blank=True, null=True, verbose_name='末端联系人')
    f_phone = models.CharField(max_length=30, blank=True, null=True, verbose_name='末端联系电话')
    factory = models.CharField(max_length=45, blank=True, null=True, verbose_name='设施名称')
    contract_picture = models.TextField(blank=True, null=True, verbose_name='合同照片')
    create_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name=u'编辑时间')
    is_delete = models.IntegerField(default=0, verbose_name='删除状态  0 未删除  1删除')
    status = models.IntegerField(default=0, verbose_name='审核状态：0草稿，1待处理，2未通过， 3已通过')
    status_type = models.IntegerField(default=1, verbose_name='审核类型：1新增、2变更、3注销申请')
    contract_status = models.IntegerField(default=1, verbose_name='合同状态：1正常 2过期 3作废 4顺延')
    handover_date = models.CharField(max_length=30, null=True, verbose_name='运输交接时间')
    clean_no = models.CharField(max_length=30, null=True, verbose_name='甲方排放登记代码')
    business_license_date = models.DateField(null=True, verbose_name='甲方经营许可有效期')
    unit_nature = models.CharField(max_length=30, null=True, verbose_name='甲方单位性质')
    nonresident_org_code = models.CharField(max_length=30, null=True, verbose_name='甲方组织机构代码')
    bank_name = models.CharField(max_length=30, null=True, verbose_name='甲方账户开户行')
    account_name = models.CharField(max_length=30, null=True, verbose_name='甲方账户名称')
    account = models.CharField(max_length=30, null=True, verbose_name='甲方账号')
    second_bank_name = models.CharField(max_length=30, null=True, verbose_name='乙方账户开户行')
    second_account_name = models.CharField(max_length=30, null=True, verbose_name='乙方账户名称')
    second_account = models.CharField(max_length=30, null=True, verbose_name='乙方账号')
    company_credit_code = models.CharField(max_length=30, null=True, verbose_name='乙方统一社会信用代码')
    company_org_code = models.CharField(max_length=30, null=True, verbose_name='乙方组织机构代码')
    company_unit_nature = models.CharField(max_length=30, null=True, verbose_name='乙方单位性质')
    price_standard = models.CharField(max_length=30, null=True, verbose_name='价格标准')
    kitchen_residue_predict = models.CharField(max_length=30, null=True, verbose_name='厨余预估量')
    kitchen_residue_quota = models.CharField(max_length=30, null=True, verbose_name='厨余定额量')
    billing_cycle = models.CharField(max_length=10, null=True, verbose_name='计费周期')
    payment_method = models.IntegerField(null=True, verbose_name='缴费方式')
    sign_weekdays = models.IntegerField(null=True, verbose_name='签订后几个工作日内')
    lower_money = models.CharField(max_length=30, null=True, verbose_name='小写金额')
    upper_money = models.CharField(max_length=30, null=True, verbose_name='大写金额')
    right_obligations = models.TextField(null=True, verbose_name='甲方权利和义务')
    break_duty = models.TextField(null=True, verbose_name='甲方违约责任')
    second_right_obligations = models.TextField(null=True, verbose_name='乙方权利和义务')
    second_break_duty = models.TextField(null=True, verbose_name='乙方违约责任')
    court = models.CharField(max_length=30, null=True, verbose_name='法院')
    arbitration = models.CharField(max_length=30, null=True, verbose_name='仲裁委员会')
    convention = models.TextField(null=True, verbose_name='甲方约定要求')
    second_convention = models.TextField(null=True, verbose_name='乙方约定要求')
    belong_to = models.CharField(max_length=20, null=True, default='线下', verbose_name='合同归属')
    service_start_date = models.DateField(null=True, verbose_name='服务期限开始日期')
    service_end_date = models.DateField(null=True, verbose_name='服务期限结束日期')
    delay_end_date = models.DateField(null=True, verbose_name='延期结束日期')
    pay_config_id = models.CharField(max_length=45, null=True, default='', verbose_name='支付配置id')
    rubbishes = models.CharField(default='RESTAURANTS', max_length=64, null=True, blank=True, verbose_name="垃圾类型")
    
    class Meta:
        managed = False
        db_table = 'contract_new'
        verbose_name = u"合同信息"
        app_label = 'transport_db'


# 乙方(运输单位)
class TransportUnit(models.Model):
    company_id = models.CharField(max_length=50, unique=True, db_index=True, verbose_name='公司id')
    bank_name = models.CharField(max_length=20, verbose_name='账户开户行')
    account_name = models.CharField(max_length=50, verbose_name='账户名称')
    account = models.CharField(max_length=30, verbose_name='账号')
    word_size = models.CharField(max_length=30, verbose_name='文字号', null=True)
    timeout = models.IntegerField(verbose_name='超时时间')
    liquidated_damages_percent = models.CharField(max_length=10, verbose_name='违约金百分比')
    deposit_percent = models.CharField(max_length=10, verbose_name='押金百分比')
    copies = models.IntegerField(default=2, verbose_name='份数')
    nonresident_copies = models.IntegerField(default=1, verbose_name='甲方份数')
    company_copies = models.IntegerField(default=1, verbose_name='乙方份数')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return self.company_id

    class Meta:
        managed = False
        db_table = 'transport_unit'
        verbose_name = '共有信息配置(乙方(运输单位))'
        app_label = 'transport_db'


class CarOperate(models.Model):
    """车辆修改记录"""
    operate_id = models.CharField(max_length=45, null=True, blank=True, verbose_name="修改ID")
    car_id = models.CharField(max_length=45, null=True, blank=True, verbose_name="车辆ID")
    type_o = models.CharField(max_length=12, null=True, blank=True, verbose_name="操作类型")
    username = models.CharField(max_length=24, null=True, blank=True, verbose_name=('修改人'))
    org_data = models.TextField(null=True, blank=True, verbose_name=('最新数据'))
    status = models.IntegerField(default=0, null=True, blank=True, verbose_name=('修改状态：1待处理 2未通过， 3已通过'))
    create_time = models.DateTimeField(default=timezone.now, verbose_name=('创建时间'))
    update_time = models.DateTimeField(auto_now=True, verbose_name=('编辑时间'))
    is_delete = models.IntegerField(default=2, verbose_name=('删除状态  0未删除  1删除 2待处理'))

    class Meta:
        db_table = 'car_operate'
        verbose_name = u"车辆修改记录"
        app_label = 'transport_db'


class CarOperateDetail(models.Model):
    """车辆修改记录详情"""
    operate_id = models.CharField(max_length=45, null=True, blank=True, verbose_name="修改ID")
    update_data = models.CharField(max_length=24, null=True, blank=True, verbose_name=('修改项'))
    old_content = models.CharField(max_length=500, null=True, blank=True, verbose_name=('修改前'))
    new_content = models.CharField(max_length=500, null=True, blank=True, verbose_name=('修改后'))
    create_time = models.DateTimeField(default=timezone.now, verbose_name=('创建时间'))
    update_time = models.DateTimeField(auto_now=True, verbose_name=('编辑时间'))

    class Meta:
        db_table = 'car_operate_detail'
        verbose_name = u"车辆修改记录详情"
        app_label = 'transport_db'


class PayConfig(models.Model):
    pay_config_id = models.CharField(max_length=45, verbose_name='uuid')
    name = models.CharField(max_length=64, verbose_name='规则名称 ')
    pay_type = models.IntegerField(default=0, verbose_name='缴费形式 0 预付金额  1及时支付  2公对公支付')
    pre_pay_price = models.FloatField(default=0, verbose_name='预交费金额 公对公支付 单位分')
    charge_rules = models.IntegerField(default=0, verbose_name='收费规则 0次缴费 1重量缴费  2包月')
    charge_price = models.FloatField(default=0, verbose_name='收费详情金额 单位分 元/次  元/月  元/kg')
    open_bank = models.CharField(max_length=64, default='', verbose_name='开户行')
    corporate_account = models.CharField(max_length=32, default='', verbose_name='对公账户')
    status = models.IntegerField(default=0, verbose_name='状态 0 应用 1不应用')
    is_deleted = models.BooleanField(default=0, verbose_name='是否删除')
    
    
    class Meta:
        db_table = 'pay_config'
        verbose_name = '支付配置'
        app_label = 'transport_db'


class TransportDriver(models.Model):
    # 司机账号信息加密存储
    username = models.CharField(max_length=64, verbose_name='账号')
    password = models.CharField(max_length=255, verbose_name='账号')
    email = models.CharField(max_length=255, verbose_name='关联清运公司邮箱', null=True, blank=True)
    is_active = models.SmallIntegerField(default=1, verbose_name='是否可用')
    role = models.CharField(max_length=255, default="TransportDriver", verbose_name='角色')
    transport_company_id = models.CharField(max_length=255, verbose_name='关联清运公司id', null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    update_time = models.DateTimeField(auto_now=True, blank=True, null=True)

    class Meta:
        db_table = 'transport_driver'
        app_label = "transport_db"
        verbose_name = "司机账号"
