from django.conf.urls import include
from django.urls import path
from rest_framework import routers
from MiniApp import views as mini_view

router = routers.DefaultRouter()

urlpatterns = [
    path('', include(router.urls)),
    path('mini_login/', mini_view.MiniLoginVs.as_view()),
    path('mini_sign_out/', mini_view.SignOutVs.as_view()),
    path('login/', mini_view.LoginVs.as_view()),
    path('login_for_car/', mini_view.LoginCar.as_view()),
    path('login_for_car_encrypt/', mini_view.LoginCarEncrypt.as_view()),
    path('collect_app_forget_password/', mini_view.CollectAppForgetPassword.as_view()),
    path('login_for_cleaning_point/', mini_view.LoginCleaningPoint.as_view()),
    path('login_by_wechat_phone/', mini_view.LoginByWechatPhone.as_view()),
    path('login_for_company_rfid/', mini_view.CompanyRfidLogin.as_view()),  # 收运公司（打签专用登录接口）
    path('live_area/login/', mini_view.LiveAreaLogin.as_view()),  # 居住区登录接口
]
