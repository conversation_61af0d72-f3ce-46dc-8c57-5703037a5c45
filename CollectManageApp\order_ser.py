#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

import datetime
import time

from django.db.models import Sum
from rest_framework import serializers
from django.core.cache import cache
from django.conf import settings

from Base.utils.const import ConstWeightType
from Base.utils.tools import time_to_timestamp
from CollectManageApp.models import OrgNonresidentDeclare, AuthorizedOrgRelationId
from CollectManageApp.models_base import Organization, TerminalRecord, CarRecord, RubbishType, OrgType, CityRegion, \
    TransportCompany, Car, CarRecordOrg, CarFlowRecord, TerminalFactoryRecord, CarType, TransportCompanyArea, \
    OrgDetail, FactoryLocation, RfidType, OrgRfid, TransportContract, OpinionDetails, ApplyStationRecod, VersionModel, \
    AppealRecod, CarRecordFlow
from CollectManageApp.models_solid import RepairWorkingOrder
from .models import NonresidentOrder, NonresidentOrderWarning
from CollectManageApp.scripts import __area_coding_to_name__, export_status


def get_rubbish_mapping(serializer):
    if not hasattr(serializer, 'rubbish_mapping'):
        rubbish_queryset = RubbishType.objects.values('type_id', 'name').filter(is_deleted=0)
        serializer.rubbish_mapping = {obj['type_id']: obj['name'] for obj in rubbish_queryset}
    return serializer.rubbish_mapping


def get_transport_company_mapping(serializer):
    if not hasattr(serializer, 'transport_company_mapping'):
        transport_company_queryset = TransportCompany.objects.values('transport_company_id', 'company').all()
        serializer.transport_company_mapping = {obj['transport_company_id']: obj['company'] for obj in
                                                transport_company_queryset}
    return serializer.transport_company_mapping


class NonresidentOrderSer(serializers.ModelSerializer):
    def get_org_mapping(self):
        try:
            org_ids = set([i.org_id for i in self.instance])
        except Exception as e:
            org_ids = set([self.instance.org_id])
        if not hasattr(self, 'org_mapping'):
            Organization_queryset = Organization.objects.filter(org_id__in=org_ids)
            self.org_mapping = {obj.org_id: obj for obj in Organization_queryset}
        return self.org_mapping

    def get_city_region_mapping(self):
        codings = set([i.area_coding for i in self.instance] + [i.street_coding for i in self.instance] +
                      [i.comm_coding for i in self.instance])
        if not hasattr(self, 'city_region_mapping'):
            self.city_region_mapping = {i.coding: i.name for i in CityRegion.objects.filter(coding__in=codings)}
        return self.city_region_mapping

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        try:
            area_name = self.get_city_region_mapping().get(obj.area_coding) if obj.area_coding else ''
        except Exception as e:
            area_name = ''
        return area_name

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        try:
            street_name = self.get_city_region_mapping().get(obj.street_coding)
        except Exception as e:
            street_name = ''
        return street_name

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        try:
            comm_name = self.get_city_region_mapping().get(obj.comm_coding)
        except Exception as e:
            comm_name = ''
        return comm_name

    rubbish_type_name = serializers.SerializerMethodField()

    def get_rubbish_type_name(self, obj):
        try:
            name = (get_rubbish_mapping(self).get(obj.rubbish_type_id)) or ''
        except Exception as e:
            name = ''
        return name

    quality = serializers.SerializerMethodField()

    def get_quality(self, obj):
        quality_name = ''
        if obj.quality_code == 1:
            quality_name = '优'
        elif obj.quality_code == 2:
            quality_name = '良'
        elif obj.quality_code == 3:
            quality_name = '中'
        elif obj.quality_code == 4:
            quality_name = '差'
        return quality_name

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        try:
            return get_transport_company_mapping(self).get(obj.transport_company_id) or ''
        except Exception as e:
            return ''

    org_name = serializers.SerializerMethodField()

    def get_org_name(self, obj):
        # return OrgNonresidentDeclare.objects.filter(org_id=obj.org_id).first().name or ''
        try:
            return self.get_org_mapping().get(obj.org_id).name
        except Exception as e:
            return ''

    pic_list = serializers.SerializerMethodField()

    def get_pic_list(self, obj):
        if obj.pic:
            return obj.pic.split(',')
        else:
            return []

    class Meta:
        model = NonresidentOrder
        fields = '__all__'


class NewNonresidentOrderWarningSer(serializers.ModelSerializer):
    def get_org_mapping(self):
        try:
            org_ids = set([i.org_id for i in self.instance])
        except Exception as e:
            org_ids = set([self.instance.org_id])
        if not hasattr(self, 'org_mapping'):
            Organization_queryset = Organization.objects.filter(org_id__in=org_ids)
            self.org_mapping = {obj.org_id: obj for obj in Organization_queryset}
        return self.org_mapping

    def get_city_region_mapping(self):
        codings = set([i.area_coding for i in self.instance] + [i.street_coding for i in self.instance] +
                      [i.comm_coding for i in self.instance])
        if not hasattr(self, 'city_region_mapping'):
            self.city_region_mapping = {i.coding: i.name for i in CityRegion.objects.filter(coding__in=codings)}
        return self.city_region_mapping

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        try:
            area_name = self.get_city_region_mapping().get(obj.area_coding) if obj.area_coding else ''
        except Exception as e:
            area_name = ''
        return area_name

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        try:
            street_name = self.get_city_region_mapping().get(obj.street_coding)
        except Exception as e:
            street_name = ''
        return street_name

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        try:
            comm_name = self.get_city_region_mapping().get(obj.comm_coding)
        except Exception as e:
            comm_name = ''
        return comm_name

    rubbish_type_name = serializers.SerializerMethodField()

    def get_rubbish_type_name(self, obj):
        try:
            name = (get_rubbish_mapping(self).get(obj.rubbish_type_id)) or ''
        except Exception as e:
            name = ''
        return name

    quality = serializers.SerializerMethodField()

    def get_quality(self, obj):
        quality_name = ''
        if obj.quality_code == 1:
            quality_name = '优'
        elif obj.quality_code == 2:
            quality_name = '良'
        elif obj.quality_code == 3:
            quality_name = '中'
        elif obj.quality_code == 4:
            quality_name = '差'
        return quality_name

    transport_company_name = serializers.SerializerMethodField()

    def get_transport_company_name(self, obj):
        try:
            return get_transport_company_mapping(self).get(obj.transport_company_id) or ''
        except Exception as e:
            return ''

    org_name = serializers.SerializerMethodField()

    def get_org_name(self, obj):
        # return OrgNonresidentDeclare.objects.filter(org_id=obj.org_id).first().name or ''
        try:
            return self.get_org_mapping().get(obj.org_id).name
        except Exception as e:
            return ''

    pic_list = serializers.SerializerMethodField()

    def get_pic_list(self, obj):
        if obj.pic:
            return obj.pic.split(',')
        else:
            return []

    warning_type = serializers.SerializerMethodField()

    def get_warning_type(self, obj):
        status = obj.status
        name = ""
        if status == 0:  # 未调度
            name = "not_dispatch"
        elif status == 1:  # 未下发
            name = "not_apply"
        elif status == 2:  # 未清运
            name = "not_clean"
        return name

    class Meta:
        model = NonresidentOrder
        fields = '__all__'

class NonresidentOrderWarningSer(serializers.ModelSerializer):
    def get_org_mapping(self):
        try:
            org_ids = set([i.org_id for i in self.instance])
        except Exception as e:
            org_ids = set([self.instance.org_id])
        if not hasattr(self, 'org_mapping'):
            Organization_queryset = Organization.objects.filter(org_id__in=org_ids)
            self.org_mapping = {obj.org_id: obj for obj in Organization_queryset}
        return self.org_mapping

    def get_city_region_mapping(self):
        codings = set([i.area_coding for i in self.instance] + [i.street_coding for i in self.instance] +
                      [i.comm_coding for i in self.instance])
        if not hasattr(self, 'city_region_mapping'):
            self.city_region_mapping = {i.coding: i.name for i in CityRegion.objects.filter(coding__in=codings)}
        return self.city_region_mapping

    area_name = serializers.SerializerMethodField()

    def get_area_name(self, obj):
        try:
            area_name = self.get_city_region_mapping().get(obj.area_coding) if obj.area_coding else ''
        except Exception as e:
            area_name = ''
        return area_name

    street_name = serializers.SerializerMethodField()

    def get_street_name(self, obj):
        try:
            street_name = self.get_city_region_mapping().get(obj.street_coding)
        except Exception as e:
            street_name = ''
        return street_name

    comm_name = serializers.SerializerMethodField()

    def get_comm_name(self, obj):
        try:
            comm_name = self.get_city_region_mapping().get(obj.comm_coding)
        except Exception as e:
            comm_name = ''
        return comm_name

    org_name = serializers.SerializerMethodField()

    def get_org_name(self, obj):
        # return OrgNonresidentDeclare.objects.filter(org_id=obj.org_id).first().name or ''
        try:
            return self.get_org_mapping().get(obj.org_id).name
        except Exception as e:
            return ''
    class Meta:
        model = NonresidentOrderWarning
        fields = '__all__'


class SolidWorkingOrderSer(serializers.ModelSerializer):

    class Meta:
        model = RepairWorkingOrder
        fields = '__all__'