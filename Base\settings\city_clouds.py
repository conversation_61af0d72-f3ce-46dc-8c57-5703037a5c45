#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import *

DEBUG = False

ALLOWED_HOSTS = [
    '*'
]
DATABASES = {
    'default': {},
    'ljfl_declare_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_declare_db',
        'USER': 'cityzt',
        'HOST': '************',
        'PASSWORD': 'Ct_ztbory20221226',
        'PORT': 3399,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'cityzt',
        'HOST': '************',
        'PASSWORD': 'Ct_ztbory20221226',
        'PORT': 3399,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'ljfl_db_replica': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': 'cityzt',
        'HOST': '************',
        'PASSWORD': 'Ct_ztbory20221226',
        'PORT': 3399,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'transport_db': {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': '************',  # 生产环境
        'PORT': 3399,
        'USER': 'cityzt',
        'PASSWORD': 'Ct_ztbory20221226',
        'NAME': 'transport_db',
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'solid': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'solid_logistics',
        'USER': 'cityzt',
        'PASSWORD': 'Ct_ztbory20221226',
        'HOST': '************',
        'PORT': 3399,
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    "jfpt": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "bj_jfpt_admin",
        "USER": "cityzt",
        "HOST": "************",
        "PASSWORD": "Ct_ztbory20221226",
        "PORT": 3399,
    },
    "tidb_ljfl_db": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "ljfl_db",
        "USER": "cityzt",
        "PASSWORD": "Ct_ztbory20221226",
        "HOST": "************",
        "PORT": 3399,
        "OPTIONS": {"charset": "utf8mb4"},
    },
    "other_transport_db": {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': '************',  # 生产环境
        'PORT': 3399,
        'USER': 'cityzt',
        'PASSWORD': 'Ct_ztbory20221226',
        'NAME': 'other_transport_db',
        'OPTIONS': {'charset': 'utf8mb4'},
    },
}

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'KEY_PREFIX': 'apiProduction:collectManageAPPV3',
        'LOCATION': 'redis://************/4',
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": "City-Ljfl&20230222",
        },
    },
}

#################
# 负载均衡IP
#################
PROXY_IP_A = "http://************"
PROXY_IP_B = "************"

# 管理授权
AUTH_APPID = *********
AUTH_APPSECRET = 'jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7'
AUTH_HOST = f'{PROXY_IP_B}/inskip/v3/auth'
# AUTH_HOST = 'auth.ztbory.com'
# 基础服务
BASE_HOST = f'{PROXY_IP_B}/LJFLManage/ljflbasedata'
# OSS服务
OSS_HOST = 'filemanager.ztbory.com'
# 资质审核服务
QUALIFI_HOST = f'{PROXY_IP_B}/LJFLManage/v3/transport'

COMPANY_SYS_IP = f'{PROXY_IP_A}/LJFLManage/v3/transport'

# 市级服务
CITY_IP = f'{PROXY_IP_A}/LJFLManage/v3/city'

INTERNAL_NETWORKS = ["***********/24"]
