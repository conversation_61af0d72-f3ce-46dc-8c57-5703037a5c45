import os

ENV_PROXY_IP = os.environ.get('PROXY_IP')
ENV_DB_HOST = os.environ.get('DB_HOST')
ENV_DB_PORT = os.environ.get('DB_PORT', 3306)
ENV_DB_USER = os.environ.get('DB_USER', 'cityzt')
ENV_DB_PASSWORD = os.environ.get('DB_PASSWORD', 'Ct_ztbory20221226')
ENV_REDIS_HOST = os.environ.get('REDIS_HOST')
ENV_REDIS_PORT = os.environ.get('REDIS_PORT', 16380)
ENV_REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', 'City-Ljfl&20230222')

assert all([ENV_PROXY_IP, ENV_DB_HOST, ENV_REDIS_HOST]), "environment not complete."
